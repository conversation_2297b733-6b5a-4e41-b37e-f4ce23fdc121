{"pages": ["FiraSans-Bold.png"], "chars": [{"id": 106, "index": 511, "char": "j", "width": 16, "height": 47, "xoffset": -4, "yoffset": 7, "xadvance": 12, "chnl": 15, "x": 0, "y": 0, "page": 0}, {"id": 40, "index": 2191, "char": "(", "width": 16, "height": 47, "xoffset": -1, "yoffset": 5, "xadvance": 14, "chnl": 15, "x": 17, "y": 0, "page": 0}, {"id": 41, "index": 2192, "char": ")", "width": 16, "height": 47, "xoffset": -1, "yoffset": 5, "xadvance": 14, "chnl": 15, "x": 34, "y": 0, "page": 0}, {"id": 36, "index": 2238, "char": "$", "width": 26, "height": 46, "xoffset": -2, "yoffset": 6, "xadvance": 23, "chnl": 15, "x": 51, "y": 0, "page": 0}, {"id": 125, "index": 2178, "char": "}", "width": 16, "height": 45, "xoffset": -1, "yoffset": 6, "xadvance": 14, "chnl": 15, "x": 78, "y": 0, "page": 0}, {"id": 123, "index": 2177, "char": "{", "width": 16, "height": 45, "xoffset": -2, "yoffset": 6, "xadvance": 14, "chnl": 15, "x": 95, "y": 0, "page": 0}, {"id": 93, "index": 2180, "char": "]", "width": 15, "height": 44, "xoffset": -1, "yoffset": 6, "xadvance": 14, "chnl": 15, "x": 112, "y": 0, "page": 0}, {"id": 91, "index": 2179, "char": "[", "width": 15, "height": 44, "xoffset": -1, "yoffset": 6, "xadvance": 14, "chnl": 15, "x": 112, "y": 45, "page": 0}, {"id": 92, "index": 2150, "char": "\\", "width": 20, "height": 43, "xoffset": 1, "yoffset": 7, "xadvance": 22, "chnl": 15, "x": 78, "y": 46, "page": 0}, {"id": 47, "index": 2169, "char": "/", "width": 20, "height": 43, "xoffset": 1, "yoffset": 7, "xadvance": 22, "chnl": 15, "x": 51, "y": 47, "page": 0}, {"id": 64, "index": 2424, "char": "@", "width": 43, "height": 43, "xoffset": 0, "yoffset": 11, "xadvance": 43, "chnl": 15, "x": 0, "y": 48, "page": 0}, {"id": 124, "index": 2432, "char": "|", "width": 10, "height": 42, "xoffset": 3, "yoffset": 7, "xadvance": 16, "chnl": 15, "x": 99, "y": 46, "page": 0}, {"id": 81, "index": 230, "char": "Q", "width": 32, "height": 40, "xoffset": -1, "yoffset": 11, "xadvance": 29, "chnl": 15, "x": 128, "y": 0, "page": 0}, {"id": 74, "index": 141, "char": "J", "width": 15, "height": 40, "xoffset": -2, "yoffset": 12, "xadvance": 13, "chnl": 15, "x": 128, "y": 41, "page": 0}, {"id": 103, "index": 460, "char": "g", "width": 27, "height": 39, "xoffset": -2, "yoffset": 15, "xadvance": 23, "chnl": 15, "x": 128, "y": 82, "page": 0}, {"id": 87, "index": 301, "char": "W", "width": 39, "height": 33, "xoffset": -2, "yoffset": 12, "xadvance": 36, "chnl": 15, "x": 72, "y": 90, "page": 0}, {"id": 105, "index": 491, "char": "i", "width": 12, "height": 38, "xoffset": 0, "yoffset": 7, "xadvance": 12, "chnl": 15, "x": 112, "y": 90, "page": 0}, {"id": 37, "index": 2363, "char": "%", "width": 38, "height": 36, "xoffset": -1, "yoffset": 11, "xadvance": 36, "chnl": 15, "x": 0, "y": 92, "page": 0}, {"id": 108, "index": 525, "char": "l", "width": 14, "height": 37, "xoffset": 0, "yoffset": 9, "xadvance": 12, "chnl": 15, "x": 44, "y": 91, "page": 0}, {"id": 100, "index": 398, "char": "d", "width": 25, "height": 37, "xoffset": 0, "yoffset": 9, "xadvance": 25, "chnl": 15, "x": 144, "y": 41, "page": 0}, {"id": 98, "index": 377, "char": "b", "width": 25, "height": 37, "xoffset": 1, "yoffset": 9, "xadvance": 25, "chnl": 15, "x": 161, "y": 0, "page": 0}, {"id": 113, "index": 617, "char": "q", "width": 25, "height": 36, "xoffset": 0, "yoffset": 18, "xadvance": 25, "chnl": 15, "x": 156, "y": 79, "page": 0}, {"id": 107, "index": 519, "char": "k", "width": 25, "height": 36, "xoffset": 1, "yoffset": 9, "xadvance": 23, "chnl": 15, "x": 170, "y": 38, "page": 0}, {"id": 102, "index": 457, "char": "f", "width": 22, "height": 36, "xoffset": -2, "yoffset": 9, "xadvance": 15, "chnl": 15, "x": 187, "y": 0, "page": 0}, {"id": 112, "index": 610, "char": "p", "width": 25, "height": 36, "xoffset": 1, "yoffset": 18, "xadvance": 25, "chnl": 15, "x": 182, "y": 75, "page": 0}, {"id": 104, "index": 480, "char": "h", "width": 23, "height": 36, "xoffset": 1, "yoffset": 9, "xadvance": 24, "chnl": 15, "x": 196, "y": 37, "page": 0}, {"id": 121, "index": 714, "char": "y", "width": 26, "height": 36, "xoffset": -2, "yoffset": 19, "xadvance": 22, "chnl": 15, "x": 210, "y": 0, "page": 0}, {"id": 79, "index": 182, "char": "O", "width": 30, "height": 35, "xoffset": -1, "yoffset": 11, "xadvance": 29, "chnl": 15, "x": 208, "y": 74, "page": 0}, {"id": 67, "index": 45, "char": "C", "width": 26, "height": 35, "xoffset": -1, "yoffset": 11, "xadvance": 24, "chnl": 15, "x": 220, "y": 37, "page": 0}, {"id": 71, "index": 101, "char": "G", "width": 28, "height": 35, "xoffset": -1, "yoffset": 11, "xadvance": 27, "chnl": 15, "x": 59, "y": 124, "page": 0}, {"id": 38, "index": 2425, "char": "&", "width": 33, "height": 35, "xoffset": 1, "yoffset": 11, "xadvance": 32, "chnl": 15, "x": 208, "y": 110, "page": 0}, {"id": 83, "index": 242, "char": "S", "width": 27, "height": 35, "xoffset": -2, "yoffset": 11, "xadvance": 24, "chnl": 15, "x": 156, "y": 116, "page": 0}, {"id": 63, "index": 2164, "char": "?", "width": 23, "height": 35, "xoffset": -1, "yoffset": 11, "xadvance": 20, "chnl": 15, "x": 88, "y": 124, "page": 0}, {"id": 119, "index": 705, "char": "w", "width": 35, "height": 26, "xoffset": -2, "yoffset": 19, "xadvance": 31, "chnl": 15, "x": 112, "y": 129, "page": 0}, {"id": 48, "index": 2047, "char": 0, "width": 25, "height": 34, "xoffset": 0, "yoffset": 12, "xadvance": 25, "chnl": 15, "x": 0, "y": 129, "page": 0}, {"id": 51, "index": 2050, "char": 3, "width": 25, "height": 34, "xoffset": -2, "yoffset": 12, "xadvance": 22, "chnl": 15, "x": 26, "y": 129, "page": 0}, {"id": 109, "index": 543, "char": "m", "width": 34, "height": 27, "xoffset": 1, "yoffset": 18, "xadvance": 35, "chnl": 15, "x": 184, "y": 146, "page": 0}, {"id": 54, "index": 2053, "char": 6, "width": 25, "height": 34, "xoffset": 0, "yoffset": 12, "xadvance": 24, "chnl": 15, "x": 148, "y": 152, "page": 0}, {"id": 33, "index": 2159, "char": "!", "width": 12, "height": 34, "xoffset": -1, "yoffset": 12, "xadvance": 10, "chnl": 15, "x": 239, "y": 73, "page": 0}, {"id": 77, "index": 161, "char": "M", "width": 34, "height": 33, "xoffset": -1, "yoffset": 12, "xadvance": 33, "chnl": 15, "x": 112, "y": 156, "page": 0}, {"id": 56, "index": 2055, "char": 8, "width": 26, "height": 34, "xoffset": -1, "yoffset": 12, "xadvance": 24, "chnl": 15, "x": 219, "y": 146, "page": 0}, {"id": 57, "index": 2056, "char": 9, "width": 25, "height": 34, "xoffset": -1, "yoffset": 12, "xadvance": 24, "chnl": 15, "x": 174, "y": 174, "page": 0}, {"id": 85, "index": 269, "char": "U", "width": 27, "height": 34, "xoffset": 0, "yoffset": 12, "xadvance": 27, "chnl": 15, "x": 200, "y": 181, "page": 0}, {"id": 78, "index": 165, "char": "N", "width": 26, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 28, "chnl": 15, "x": 147, "y": 187, "page": 0}, {"id": 116, "index": 651, "char": "t", "width": 21, "height": 33, "xoffset": -2, "yoffset": 13, "xadvance": 16, "chnl": 15, "x": 184, "y": 112, "page": 0}, {"id": 80, "index": 225, "char": "P", "width": 26, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 25, "chnl": 15, "x": 228, "y": 181, "page": 0}, {"id": 65, "index": 3, "char": "A", "width": 30, "height": 33, "xoffset": -3, "yoffset": 12, "xadvance": 25, "chnl": 15, "x": 174, "y": 216, "page": 0}, {"id": 82, "index": 232, "char": "R", "width": 27, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 26, "chnl": 15, "x": 228, "y": 215, "page": 0}, {"id": 66, "index": 37, "char": "B", "width": 26, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 26, "chnl": 15, "x": 52, "y": 160, "page": 0}, {"id": 84, "index": 254, "char": "T", "width": 26, "height": 33, "xoffset": -2, "yoffset": 12, "xadvance": 20, "chnl": 15, "x": 79, "y": 160, "page": 0}, {"id": 52, "index": 2051, "char": 4, "width": 26, "height": 33, "xoffset": -1, "yoffset": 12, "xadvance": 24, "chnl": 15, "x": 106, "y": 190, "page": 0}, {"id": 86, "index": 298, "char": "V", "width": 29, "height": 33, "xoffset": -3, "yoffset": 12, "xadvance": 24, "chnl": 15, "x": 133, "y": 221, "page": 0}, {"id": 68, "index": 55, "char": "D", "width": 27, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 27, "chnl": 15, "x": 0, "y": 164, "page": 0}, {"id": 88, "index": 308, "char": "X", "width": 29, "height": 33, "xoffset": -2, "yoffset": 12, "xadvance": 24, "chnl": 15, "x": 28, "y": 194, "page": 0}, {"id": 89, "index": 309, "char": "Y", "width": 30, "height": 33, "xoffset": -2, "yoffset": 12, "xadvance": 25, "chnl": 15, "x": 58, "y": 194, "page": 0}, {"id": 90, "index": 321, "char": "Z", "width": 25, "height": 33, "xoffset": -1, "yoffset": 12, "xadvance": 23, "chnl": 15, "x": 0, "y": 198, "page": 0}, {"id": 69, "index": 70, "char": "E", "width": 22, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 22, "chnl": 15, "x": 205, "y": 216, "page": 0}, {"id": 70, "index": 98, "char": "F", "width": 21, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 20, "chnl": 15, "x": 237, "y": 0, "page": 0}, {"id": 53, "index": 2052, "char": 5, "width": 25, "height": 33, "xoffset": -1, "yoffset": 13, "xadvance": 23, "chnl": 15, "x": 242, "y": 108, "page": 0}, {"id": 72, "index": 113, "char": "H", "width": 27, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 28, "chnl": 15, "x": 247, "y": 34, "page": 0}, {"id": 73, "index": 122, "char": "I", "width": 11, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 12, "chnl": 15, "x": 89, "y": 194, "page": 0}, {"id": 59, "index": 2168, "char": ";", "width": 12, "height": 33, "xoffset": -1, "yoffset": 20, "xadvance": 10, "chnl": 15, "x": 259, "y": 0, "page": 0}, {"id": 75, "index": 145, "char": "K", "width": 28, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 27, "chnl": 15, "x": 272, "y": 0, "page": 0}, {"id": 50, "index": 2049, "char": 2, "width": 24, "height": 33, "xoffset": -2, "yoffset": 12, "xadvance": 22, "chnl": 15, "x": 246, "y": 142, "page": 0}, {"id": 55, "index": 2054, "char": 7, "width": 22, "height": 33, "xoffset": -1, "yoffset": 13, "xadvance": 20, "chnl": 15, "x": 255, "y": 176, "page": 0}, {"id": 76, "index": 149, "char": "L", "width": 22, "height": 33, "xoffset": 1, "yoffset": 12, "xadvance": 22, "chnl": 15, "x": 252, "y": 68, "page": 0}, {"id": 49, "index": 2048, "char": 1, "width": 19, "height": 32, "xoffset": -1, "yoffset": 13, "xadvance": 19, "chnl": 15, "x": 101, "y": 224, "page": 0}, {"id": 35, "index": 2161, "char": "#", "width": 26, "height": 32, "xoffset": -1, "yoffset": 13, "xadvance": 23, "chnl": 15, "x": 268, "y": 102, "page": 0}, {"id": 99, "index": 387, "char": "c", "width": 23, "height": 28, "xoffset": -1, "yoffset": 18, "xadvance": 20, "chnl": 15, "x": 28, "y": 164, "page": 0}, {"id": 115, "index": 637, "char": "s", "width": 23, "height": 28, "xoffset": -2, "yoffset": 18, "xadvance": 20, "chnl": 15, "x": 26, "y": 228, "page": 0}, {"id": 97, "index": 340, "char": "a", "width": 25, "height": 28, "xoffset": -1, "yoffset": 18, "xadvance": 23, "chnl": 15, "x": 50, "y": 228, "page": 0}, {"id": 101, "index": 417, "char": "e", "width": 25, "height": 28, "xoffset": -1, "yoffset": 18, "xadvance": 23, "chnl": 15, "x": 271, "y": 135, "page": 0}, {"id": 111, "index": 566, "char": "o", "width": 26, "height": 28, "xoffset": -1, "yoffset": 18, "xadvance": 25, "chnl": 15, "x": 256, "y": 210, "page": 0}, {"id": 114, "index": 621, "char": "r", "width": 18, "height": 27, "xoffset": 1, "yoffset": 18, "xadvance": 17, "chnl": 15, "x": 76, "y": 228, "page": 0}, {"id": 117, "index": 672, "char": "u", "width": 23, "height": 27, "xoffset": 0, "yoffset": 19, "xadvance": 24, "chnl": 15, "x": 278, "y": 164, "page": 0}, {"id": 110, "index": 549, "char": "n", "width": 23, "height": 27, "xoffset": 1, "yoffset": 18, "xadvance": 24, "chnl": 15, "x": 283, "y": 192, "page": 0}, {"id": 120, "index": 713, "char": "x", "width": 26, "height": 26, "xoffset": -2, "yoffset": 19, "xadvance": 22, "chnl": 15, "x": 283, "y": 220, "page": 0}, {"id": 122, "index": 726, "char": "z", "width": 22, "height": 26, "xoffset": -1, "yoffset": 19, "xadvance": 19, "chnl": 15, "x": 275, "y": 34, "page": 0}, {"id": 58, "index": 2155, "char": ":", "width": 12, "height": 26, "xoffset": -1, "yoffset": 20, "xadvance": 10, "chnl": 15, "x": 59, "y": 91, "page": 0}, {"id": 118, "index": 701, "char": "v", "width": 26, "height": 26, "xoffset": -2, "yoffset": 19, "xadvance": 22, "chnl": 15, "x": 275, "y": 61, "page": 0}, {"id": 94, "index": 2439, "char": "^", "width": 26, "height": 17, "xoffset": -1, "yoffset": 6, "xadvance": 23, "chnl": 15, "x": 256, "y": 239, "page": 0}, {"id": 95, "index": 2173, "char": "_", "width": 25, "height": 9, "xoffset": -2, "yoffset": 43, "xadvance": 22, "chnl": 15, "x": 0, "y": 232, "page": 0}, {"id": 60, "index": 2324, "char": "<", "width": 23, "height": 24, "xoffset": -1, "yoffset": 17, "xadvance": 22, "chnl": 15, "x": 298, "y": 34, "page": 0}, {"id": 126, "index": 2329, "char": "~", "width": 24, "height": 11, "xoffset": -1, "yoffset": 24, "xadvance": 22, "chnl": 15, "x": 0, "y": 242, "page": 0}, {"id": 62, "index": 2323, "char": ">", "width": 23, "height": 24, "xoffset": -1, "yoffset": 17, "xadvance": 22, "chnl": 15, "x": 301, "y": 0, "page": 0}, {"id": 61, "index": 2321, "char": "=", "width": 22, "height": 18, "xoffset": 0, "yoffset": 20, "xadvance": 22, "chnl": 15, "x": 295, "y": 88, "page": 0}, {"id": 43, "index": 2317, "char": "+", "width": 22, "height": 22, "xoffset": 0, "yoffset": 18, "xadvance": 22, "chnl": 15, "x": 295, "y": 107, "page": 0}, {"id": 42, "index": 2149, "char": "*", "width": 22, "height": 21, "xoffset": -2, "yoffset": 10, "xadvance": 19, "chnl": 15, "x": 302, "y": 59, "page": 0}, {"id": 44, "index": 2156, "char": ",", "width": 12, "height": 18, "xoffset": -1, "yoffset": 34, "xadvance": 10, "chnl": 15, "x": 133, "y": 190, "page": 0}, {"id": 34, "index": 2166, "char": "\"", "width": 18, "height": 16, "xoffset": 0, "yoffset": 12, "xadvance": 18, "chnl": 15, "x": 322, "y": 25, "page": 0}, {"id": 45, "index": 2203, "char": "-", "width": 17, "height": 9, "xoffset": 0, "yoffset": 25, "xadvance": 17, "chnl": 15, "x": 275, "y": 88, "page": 0}, {"id": 96, "index": 2585, "char": "`", "width": 16, "height": 13, "xoffset": -1, "yoffset": 6, "xadvance": 14, "chnl": 15, "x": 322, "y": 42, "page": 0}, {"id": 39, "index": 2167, "char": "'", "width": 10, "height": 16, "xoffset": 0, "yoffset": 12, "xadvance": 10, "chnl": 15, "x": 163, "y": 221, "page": 0}, {"id": 46, "index": 2163, "char": ".", "width": 12, "height": 12, "xoffset": -1, "yoffset": 34, "xadvance": 10, "chnl": 15, "x": 325, "y": 0, "page": 0}, {"id": 32, "index": 2, "char": " ", "width": 0, "height": 0, "xoffset": -2, "yoffset": 41, "xadvance": 10, "chnl": 15, "x": 255, "y": 210, "page": 0}], "info": {"face": "FiraSans-Bold", "size": 42, "bold": 0, "italic": 0, "charset": [" ", "!", "\"", "#", "$", "%", "&", "'", "(", ")", "*", "+", ",", "-", ".", "/", 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, ":", ";", "<", "=", ">", "?", "@", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "[", "\\", "]", "^", "_", "`", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "{", "|", "}", "~"], "unicode": 1, "stretchH": 100, "smooth": 1, "aa": 1, "padding": [0, 0, 0, 0], "spacing": [0, 0]}, "common": {"lineHeight": 50, "base": 41, "scaleW": 512, "scaleH": 256, "pages": 1, "packed": 0, "alphaChnl": 0, "redChnl": 0, "greenChnl": 0, "blueChnl": 0}, "distanceField": {"fieldType": "msdf", "distanceRange": 4}, "kernings": []}