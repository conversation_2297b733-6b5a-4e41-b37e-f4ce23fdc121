<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Flat Shading Matcap</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Flat Shading Matcap. Model by Google Poly.</div>
        <script type="module">
            import { Renderer, Camera, Transform, Texture, Program, Geometry, Mesh, Vec3, Orbit } from '../src/index.js';

            // When we use standard derivatives (dFdx & dFdy functions),
            // which are necessary for this effect, WebGL1 requires the
            // GL_OES_standard_derivatives extension, and WebGL2 complains
            // about the extension's existence. So unfortunately we're
            // forced to create a 300 es GLSL shader for WebGL2, and a 100 es
            // GLSL shader for WebGL1. There are only slight syntax changes.
            const vertex = /* glsl */ `
                attribute vec3 position;

                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;

                varying vec4 vMVPos;

                void main() {
                    vMVPos = modelViewMatrix * vec4(position, 1.0);
                    gl_Position = projectionMatrix * vMVPos;
                }
            `;

            const fragment = /* glsl */ `
                uniform sampler2D tMap;

                varying vec4 vMVPos;

                vec3 normals(vec3 pos) {
                    vec3 fdx = dFdx(pos);
                    vec3 fdy = dFdy(pos);
                    return normalize(cross(fdx, fdy));
                }

                vec2 matcap(vec3 eye, vec3 normal) {
                    vec3 reflected = reflect(eye, normal);
                    float m = 2.8284271247461903 * sqrt(reflected.z + 1.0);
                    return reflected.xy / m + 0.5;
                }

                void main() {
                    vec3 normal = normals(vMVPos.xyz);

                    // We're using the matcap to add some shininess to the model
                    float mat = texture2D(tMap, matcap(normalize(vMVPos.xyz), normal)).g;

                    gl_FragColor.rgb = normal + mat;
                    gl_FragColor.a = 1.0;
                }
            `;

            const vertex100 =
                /* glsl */ `
            ` + vertex;

            const fragment100 =
                /* glsl */ `#extension GL_OES_standard_derivatives : enable
                precision highp float;
            ` + fragment;

            const vertex300 =
                /* glsl */ `#version 300 es
                #define attribute in
                #define varying out
            ` + vertex;

            const fragment300 =
                /* glsl */ `#version 300 es
                precision highp float;
                #define varying in
                #define texture2D texture
                #define gl_FragColor FragColor
                out vec4 FragColor;
            ` + fragment;

            {
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(1, 1, 1, 1);

                const camera = new Camera(gl, { fov: 45 });
                camera.position.set(2, 1, 2);

                const controls = new Orbit(camera, {
                    target: new Vec3(0, 0.2, 0),
                });

                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                const scene = new Transform();

                const texture = new Texture(gl);
                const img = new Image();
                img.onload = () => (texture.image = img);
                img.src = 'assets/matcap.jpg';

                const program = new Program(gl, {
                    vertex: renderer.isWebgl2 ? vertex300 : vertex100,
                    fragment: renderer.isWebgl2 ? fragment300 : fragment100,
                    uniforms: {
                        tMap: { value: texture },
                    },
                    cullFace: false,
                });

                loadModel();
                async function loadModel() {
                    const data = await (await fetch(`assets/octopus.json`)).json();

                    // If you can generate the flat-shaded normals with attributes, it would
                    // be more efficient. However if your mesh is dynamic, indexed, or you're
                    // updating the vertices in the shader, we can still calculate the normals
                    // in the shader - all we need is the position.
                    const geometry = new Geometry(gl, {
                        position: { size: 3, data: new Float32Array(data.position) },
                    });

                    let mesh = new Mesh(gl, { geometry, program });
                    mesh.setParent(scene);
                }

                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update);

                    controls.update();
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
