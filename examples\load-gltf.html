<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Load GLTF</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info" style="color: #fff">
            Load GLTF (Graphics Language Transmission Format). Model by
            <a href="https://sketchfab.com/3d-models/hershel-little-america-f4d7a0ed8af2453e9adc632278c9aa50" target="_blank">VRModelFactory</a>
            <br /><a href="/ogl/examples/?src=gltf-draco-webp.html" target="_top">Draco and WebP Compressed Version</a>
        </div>
        <script type="module">
            // ========== 导入OGL库的核心组件 ==========
            // Renderer: WebGL渲染器，负责创建WebGL上下文和渲染场景
            // Camera: 相机，定义视角和投影矩阵
            // Transform: 变换节点，用于构建场景图层次结构
            // Orbit: 轨道控制器，提供鼠标交互控制相机
            // Program: 着色器程序，包含顶点着色器和片段着色器
            // GLTFLoader: GLTF格式3D模型加载器
            // Vec3: 三维向量类
            // TextureLoader: 纹理加载器
            import { Renderer, Camera, Transform, Orbit, Program, GLTFLoader, Vec3, TextureLoader } from '../src/index.js';

            // ========== PBR（基于物理的渲染）着色器定义 ==========
            // 这是一个通用的PBR着色器，支持多种材质特性和渲染技术
            const shader = {
                // ========== 顶点着色器 ==========
                // 处理顶点变换、骨骼动画、实例化渲染等
                vertex: /* glsl */ `
                    attribute vec3 position;  // 顶点位置属性

                    // ========== 条件编译：UV坐标 ==========
                    #ifdef UV
                        attribute vec2 uv;        // UV纹理坐标属性
                    #else
                        const vec2 uv = vec2(0);  // 如果没有UV，使用默认值
                    #endif

                    // ========== 条件编译：法线 ==========
                    #ifdef NORMAL
                        attribute vec3 normal;    // 顶点法线属性
                    #else
                        const vec3 normal = vec3(0);  // 如果没有法线，使用默认值
                    #endif

                    // ========== 条件编译：实例化渲染 ==========
                    #ifdef INSTANCED
                        attribute mat4 instanceMatrix;  // 实例化矩阵属性
                    #endif

                    // ========== 条件编译：骨骼动画 ==========
                    #ifdef SKINNING
                        attribute vec4 skinIndex;   // 骨骼索引（每个顶点最多4个骨骼）
                        attribute vec4 skinWeight;  // 骨骼权重（对应4个骨骼的影响权重）
                    #endif

                    // ========== 变换矩阵 ==========
                    uniform mat4 modelViewMatrix;   // 模型视图矩阵
                    uniform mat4 projectionMatrix;  // 投影矩阵
                    uniform mat4 modelMatrix;       // 模型矩阵
                    uniform mat3 normalMatrix;      // 法线矩阵

                    // ========== 骨骼动画相关 ==========
                    #ifdef SKINNING
                        uniform sampler2D boneTexture;  // 骨骼变换矩阵纹理
                        uniform int boneTextureSize;    // 骨骼纹理大小
                    #endif

                    // ========== 传递给片段着色器的变量 ==========
                    varying vec2 vUv;      // UV坐标
                    varying vec3 vNormal;  // 世界空间法线
                    varying vec3 vMPos;    // 世界空间位置
                    varying vec4 vMVPos;   // 视图空间位置

                    // ========== 骨骼动画函数 ==========
                    #ifdef SKINNING
                        // 从骨骼纹理中获取指定索引的骨骼变换矩阵
                        mat4 getBoneMatrix(const in float i) {
                            // 计算纹理坐标：每个矩阵占用4个像素（4x4矩阵的4列）
                            float j = i * 4.0;
                            float x = mod(j, float(boneTextureSize));        // X坐标
                            float y = floor(j / float(boneTextureSize));     // Y坐标

                            // 计算纹理采样的步长
                            float dx = 1.0 / float(boneTextureSize);
                            float dy = 1.0 / float(boneTextureSize);

                            // 调整Y坐标到像素中心
                            y = dy * (y + 0.5);

                            // 从纹理中读取矩阵的4列
                            vec4 v1 = texture2D(boneTexture, vec2(dx * (x + 0.5), y));  // 第1列
                            vec4 v2 = texture2D(boneTexture, vec2(dx * (x + 1.5), y));  // 第2列
                            vec4 v3 = texture2D(boneTexture, vec2(dx * (x + 2.5), y));  // 第3列
                            vec4 v4 = texture2D(boneTexture, vec2(dx * (x + 3.5), y));  // 第4列

                            return mat4(v1, v2, v3, v4);  // 构建4x4变换矩阵
                        }

                        // 对顶点位置和法线应用骨骼变换
                        void skin(inout vec4 pos, inout vec3 nml) {
                            // 获取影响当前顶点的4个骨骼的变换矩阵
                            mat4 boneMatX = getBoneMatrix(skinIndex.x);
                            mat4 boneMatY = getBoneMatrix(skinIndex.y);
                            mat4 boneMatZ = getBoneMatrix(skinIndex.z);
                            mat4 boneMatW = getBoneMatrix(skinIndex.w);

                            // ========== 更新法线 ==========
                            // 根据骨骼权重混合变换矩阵
                            mat4 skinMatrix = mat4(0.0);
                            skinMatrix += skinWeight.x * boneMatX;
                            skinMatrix += skinWeight.y * boneMatY;
                            skinMatrix += skinWeight.z * boneMatZ;
                            skinMatrix += skinWeight.w * boneMatW;
                            nml = vec4(skinMatrix * vec4(nml, 0.0)).xyz;

                            // ========== 更新位置 ==========
                            // 根据骨骼权重混合变换后的位置
                            vec4 transformed = vec4(0.0);
                            transformed += boneMatX * pos * skinWeight.x;
                            transformed += boneMatY * pos * skinWeight.y;
                            transformed += boneMatZ * pos * skinWeight.z;
                            transformed += boneMatW * pos * skinWeight.w;
                            pos = transformed;
                        }
                    #endif

                    // ========== 顶点着色器主函数 ==========
                    void main() {
                        vec4 pos = vec4(position, 1);  // 初始化顶点位置
                        vec3 nml = normal;              // 初始化法线

                        // ========== 应用骨骼动画变换 ==========
                        #ifdef SKINNING
                            skin(pos, nml);  // 对位置和法线应用骨骼变换
                        #endif

                        // ========== 应用实例化变换 ==========
                        #ifdef INSTANCED
                            pos = instanceMatrix * pos;  // 应用实例化矩阵到位置

                            // 对法线应用实例化变换（需要考虑非均匀缩放）
                            // 正确的法线变换应该使用逆转置矩阵: (M^-1)^T * normal
                            // 这里使用高效的近似方法来实现相同效果

                            // 第一步：从4x4实例化矩阵中提取3x3旋转缩放矩阵
                            // 去掉平移信息，保留旋转和缩放变换
                            mat3 m = mat3(instanceMatrix);

                            // 第二步：补偿非均匀缩放对法线的影响
                            // dot(m[i], m[i]) 计算每个轴的缩放因子的平方
                            // 除法操作相当于乘以缩放因子的倒数平方，实现逆转置效果
                            // 这样可以防止非均匀缩放导致法线方向错误

                            // 正确的法线变换应该使用变换矩阵的逆转置矩阵：
                            // nml = transpose(inverse(m)) * nml;
                            // 但直接计算逆转置矩阵开销很大，所以这里用了一个巧妙的近似方法：
                            // 先除以缩放因子的平方（补偿缩放）
                            // 再乘以原矩阵（应用旋转）
                            // 这种方法在大多数情况下能得到正确的结果，且计算效率更高。

                            nml /= vec3(dot(m[0], m[0]), dot(m[1], m[1]), dot(m[2], m[2]));

                            // 第三步：应用旋转变换到法线
                            // 将经过缩放补偿的法线乘以变换矩阵，应用旋转变换
                            nml = m * nml;
                        #endif

                        // ========== 设置输出变量 ==========
                        vUv = uv;                    // 传递UV坐标
                        vNormal = normalize(nml);    // 传递归一化的法线

                        // 计算世界空间位置
                        vec4 mPos = modelMatrix * pos;
                        vMPos = mPos.xyz / mPos.w;   // 透视除法得到世界坐标
                        vMVPos = modelViewMatrix * pos;  // 视图空间位置

                        // 计算最终的裁剪空间位置
                        gl_Position = projectionMatrix * vMVPos;
                    }
                `,

                // ========== PBR片段着色器 ==========
                // 实现基于物理的渲染，支持金属度/粗糙度工作流
                fragment: /* glsl */ `
                    // ========== 基础变换和相机信息 ==========
                    uniform mat4 viewMatrix;      // 视图矩阵
                    uniform vec3 cameraPosition;  // 相机世界坐标

                    // ========== 基础颜色相关 ==========
                    uniform vec4 uBaseColorFactor;  // 基础颜色因子
                    uniform sampler2D tBaseColor;   // 基础颜色纹理（漫反射贴图）

                    // ========== 金属度/粗糙度相关 ==========
                    uniform sampler2D tRM;      // 粗糙度/金属度纹理（R通道=遮挡，G通道=粗糙度，B通道=金属度）
                    uniform float uRoughness;   // 粗糙度因子
                    uniform float uMetallic;    // 金属度因子

                    // ========== 法线贴图相关 ==========
                    uniform sampler2D tNormal;    // 法线纹理
                    uniform float uNormalScale;   // 法线强度缩放

                    // ========== 自发光相关 ==========
                    uniform sampler2D tEmissive;  // 自发光纹理
                    uniform vec3 uEmissive;       // 自发光因子

                    // ========== 环境遮挡相关 ==========
                    uniform sampler2D tOcclusion; // 环境遮挡纹理

                    // ========== 基于图像的光照（IBL）相关 ==========
                    uniform sampler2D tLUT;         // BRDF查找表纹理
                    uniform sampler2D tEnvDiffuse;  // 环境漫反射纹理
                    uniform sampler2D tEnvSpecular; // 环境镜面反射纹理
                    uniform float uEnvDiffuse;      // 环境漫反射强度
                    uniform float uEnvSpecular;     // 环境镜面反射强度

                    // ========== 直接光照相关 ==========
                    uniform vec3 uLightDirection;   // 光源方向
                    uniform vec3 uLightColor;       // 光源颜色

                    // ========== 透明度相关 ==========
                    uniform float uAlpha;           // 透明度
                    uniform float uAlphaCutoff;     // Alpha测试阈值

                    // ========== 从顶点着色器接收的变量 ==========
                    varying vec2 vUv;      // UV坐标
                    varying vec3 vNormal;  // 世界空间法线
                    varying vec3 vMPos;    // 世界空间位置
                    varying vec4 vMVPos;   // 视图空间位置

                    // ========== 数学常量定义 ==========
                    const float PI = 3.14159265359;           // 圆周率
                    const float RECIPROCAL_PI = 0.31830988618; // 1/π
                    const float RECIPROCAL_PI2 = 0.15915494;   // 1/(2π)
                    const float LN2 = 0.6931472;              // ln(2)

                    const float ENV_LODS = 6.0;  // 环境贴图的mipmap级别数

                    // ========== 颜色空间转换函数 ==========

                    // 将sRGB颜色转换为线性空间（用于正确的光照计算）
                    vec4 SRGBtoLinear(vec4 srgb) {
                        vec3 linOut = pow(srgb.xyz, vec3(2.2));  // 应用gamma校正
                        return vec4(linOut, srgb.w);
                    }

                    // 将RGBM编码的HDR纹理转换为线性空间
                    // RGBM格式：RGB存储颜色，M（Alpha）存储亮度倍数
                    vec4 RGBMToLinear(in vec4 value) {
                        float maxRange = 6.0;  // 最大亮度范围
                        return vec4(value.xyz * value.w * maxRange, 1.0);
                    }

                    // 将线性空间颜色转换为sRGB（用于显示）
                    vec3 linearToSRGB(vec3 color) {
                        return pow(color, vec3(1.0 / 2.2));  // 应用逆gamma校正
                    }

                    // ========== 法线贴图计算函数 ==========
                    // 从法线贴图和几何体法线计算最终的世界空间法线
                    //
                    // 🔬 **法线贴图技术原理**：
                    // 法线贴图存储的是切线空间（Tangent Space）的法线信息，
                    // 需要转换到世界空间才能用于光照计算。
                    //
                    // 📐 **TBN矩阵构建**：
                    // - T (Tangent): 切线向量，沿着UV的U方向
                    // - B (Bitangent): 副切线向量，沿着UV的V方向
                    // - N (Normal): 几何体法线向量
                    // TBN矩阵将切线空间转换为世界空间
                    vec3 getNormal() {
               
                        #ifdef NORMAL_MAP
                            // ========== 计算屏幕空间偏导数 ==========
                            // 使用GPU的偏导数指令计算相邻像素间的差值
                            vec3 pos_dx = dFdx(vMPos.xyz);  // 世界位置在X方向的变化率
                            vec3 pos_dy = dFdy(vMPos.xyz);  // 世界位置在Y方向的变化率
                            vec2 tex_dx = dFdx(vUv);        // UV坐标在X方向的变化率
                            vec2 tex_dy = dFdy(vUv);        // UV坐标在Y方向的变化率

                            // ========== 构建切线空间基向量 ==========
                            // 使用偏导数计算切线和副切线向量
                            // 这种方法不需要预计算的切线属性，适用于任意几何体
                            vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);  // 切线向量T
                            vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s); // 副切线向量B
                            mat3 tbn = mat3(t, b, normalize(vNormal));                  // TBN变换矩阵

                            // ========== 法线贴图采样和解码 ==========
                            // 法线贴图存储的是[0,1]范围的值，需要转换为[-1,1]范围
                            vec3 n = texture2D(tNormal, vUv).rgb * 2.0 - 1.0;  // 解码法线向量
                            n.xy *= uNormalScale;  // 应用法线强度缩放（控制凹凸效果强度）

                            // ========== 切线空间到世界空间转换 ==========
                            vec3 normal = normalize(tbn * n);  // 使用TBN矩阵转换法线

                            // 返回世界空间法线（已经在世界空间中，无需额外转换）
                            return normalize(normal);
                        #else
                            // 如果没有法线贴图，直接使用几何体法线
                            return normalize(vNormal);
                        #endif
                    }

                    // ========== Fresnel反射函数 ==========
                    // 计算基于视角的镜面反射强度（Fresnel效应）
                    //
                    // 🔬 **Fresnel效应原理**：
                    // 当光线从一种介质进入另一种介质时，反射和折射的比例
                    // 取决于入射角度。垂直入射时反射较少，掠射角时反射较多。
                    //
                    // 📐 **Schlick近似**：
                    // 使用Schlick近似公式计算Fresnel反射，比完整的Fresnel方程更高效
                    // F = F0 + (F90 - F0) * (1 - VdH)^5
                    //
                    // @param specularEnvR0 - 垂直入射时的反射率（F0）
                    // @param specularEnvR90 - 掠射角时的反射率（F90，通常为1.0）
                    // @param VdH - 视线向量与半角向量的点积
                    vec3 specularReflection(vec3 specularEnvR0, vec3 specularEnvR90, float VdH) {
                        return specularEnvR0 + (specularEnvR90 - specularEnvR0) * pow(clamp(1.0 - VdH, 0.0, 1.0), 5.0);
                    }

                    // ========== 几何遮挡函数 ==========
                    // 计算微表面几何遮挡和阴影效应（Smith G函数）
                    //
                    // 🔬 **几何函数原理**：
                    // 微表面理论中，表面由无数微小的镜面组成。
                    // 几何函数描述了这些微表面之间的相互遮挡和阴影效应。
                    //
                    // 📐 **Smith模型**：
                    // G = G1(L) * G1(V)，分别计算光线和视线方向的遮挡
                    // 使用Smith近似，考虑粗糙度对遮挡的影响
                    //
                    // @param NdL - 法线与光线方向的点积
                    // @param NdV - 法线与视线方向的点积
                    // @param roughness - 表面粗糙度
                    float geometricOcclusion(float NdL, float NdV, float roughness) {
                        float r = roughness;

                        // 计算光线方向的几何衰减
                        float attenuationL = 2.0 * NdL / (NdL + sqrt(r * r + (1.0 - r * r) * (NdL * NdL)));
                        // 计算视线方向的几何衰减
                        float attenuationV = 2.0 * NdV / (NdV + sqrt(r * r + (1.0 - r * r) * (NdV * NdV)));
                        return attenuationL * attenuationV;
                    }

                    // ========== 微表面分布函数 ==========
                    // 计算微表面法线分布（GGX/Trowbridge-Reitz分布）
                    //
                    // 🔬 **法线分布函数原理**：
                    // 描述微表面法线的统计分布，决定了镜面反射的形状和强度。
                    // 粗糙度越高，分布越宽，反射越模糊。
                    //
                    // 📐 **GGX分布**：
                    // D = α² / (π * ((NdH)² * (α² - 1) + 1)²)
                    // 其中α = roughness²，提供了更真实的长尾分布
                    //
                    // @param roughness - 表面粗糙度
                    // @param NdH - 法线与半角向量的点积
                    float microfacetDistribution(float roughness, float NdH) {
                        float roughnessSq = roughness * roughness;  // α²
                        float f = (NdH * roughnessSq - NdH) * NdH + 1.0;  // 分母的一部分
                        return roughnessSq / (PI * f * f);  // GGX分布公式
                    }

                    // ========== 笛卡尔坐标到极坐标转换 ==========
                    // 将3D方向向量转换为2D纹理坐标，用于环境贴图采样
                    //
                    // 🔬 **球面映射原理**：
                    // 将3D球面上的点映射到2D纹理坐标，常用于环境贴图。
                    // 使用球坐标系统：θ（方位角）和φ（极角）
                    //
                    // 📐 **坐标转换公式**：
                    // - 方位角θ = atan2(z, x)，范围[-π, π]，映射到[0, 1]
                    // - 极角φ = asin(y)，范围[-π/2, π/2]，映射到[0, 1]
                    //
                    // @param n - 归一化的3D方向向量
                    // @return 对应的2D纹理坐标
                    vec2 cartesianToPolar(vec3 n) {
                        vec2 uv;
                        // 计算方位角并转换为[0,1]范围的U坐标
                        uv.x = atan(n.z, n.x) * RECIPROCAL_PI2 + 0.5;  // θ/(2π) + 0.5
                        // 计算极角并转换为[0,1]范围的V坐标
                        uv.y = asin(n.y) * RECIPROCAL_PI + 0.5;        // φ/π + 0.5
                        return uv;
                    }

                    // ========== 基于图像的光照（IBL）贡献计算 ==========
                    // 计算来自环境贴图的漫反射和镜面反射光照贡献
                    //
                    // 🔬 **IBL技术原理**：
                    // IBL使用预计算的环境贴图来模拟复杂的环境光照，
                    // 包括漫反射环境光和镜面反射环境光。
                    //
                    // 📐 **Split-Sum近似**：
                    // 将复杂的积分分解为两部分：
                    // 1. 环境贴图的预过滤（根据粗糙度）
                    // 2. BRDF积分的预计算（存储在LUT中）
                    //
                    // @param diffuse - 输出的漫反射贡献
                    // @param specular - 输出的镜面反射贡献
                    // @param NdV - 法线与视线的点积
                    // @param roughness - 表面粗糙度
                    // @param n - 表面法线向量
                    // @param reflection - 反射向量
                    // @param diffuseColor - 材质漫反射颜色
                    // @param specularColor - 材质镜面反射颜色
                    void getIBLContribution(inout vec3 diffuse, inout vec3 specular, float NdV, float roughness, vec3 n, vec3 reflection, vec3 diffuseColor, vec3 specularColor) {
                        // ========== BRDF查找表采样 ==========
                        // 从预计算的BRDF LUT中获取积分结果
                        // X轴：NdV（法线与视线夹角），Y轴：roughness（粗糙度）
                        vec3 brdf = SRGBtoLinear(texture2D(tLUT, vec2(NdV, roughness))).rgb;

                        // ========== 漫反射环境光计算 ==========
                        // 使用表面法线采样预过滤的漫反射环境贴图
                        vec3 diffuseLight = RGBMToLinear(texture2D(tEnvDiffuse, cartesianToPolar(n))).rgb;
                        diffuseLight = mix(vec3(1), diffuseLight, uEnvDiffuse);  // 与白光混合控制强度

                        // ========== 镜面反射Mipmap级别计算 ==========
                        // 根据粗糙度计算需要采样的mipmap级别
                        // 粗糙度越高，使用越模糊的mipmap级别
                        float blend = roughness * ENV_LODS;  // 计算浮点级别
                        float level0 = floor(blend);         // 下级别（整数）
                        float level1 = min(ENV_LODS, level0 + 1.0);  // 上级别（整数）
                        blend -= level0;                     // 级别间的插值权重

                        // ========== 镜面反射环境贴图采样 ==========
                        // 使用反射向量采样预过滤的镜面反射环境贴图
                        vec2 uvSpec = cartesianToPolar(reflection);
                        uvSpec.y /= 2.0;  // 调整V坐标以适应atlas布局

                        // ========== 双级别采样和混合 ==========
                        // 分别采样两个相邻的mipmap级别，然后线性插值
                        vec2 uv0 = uvSpec;
                        vec2 uv1 = uvSpec;

                        // 计算第一个级别的UV坐标（考虑atlas布局）
                        uv0 /= pow(2.0, level0);                    // 缩放UV以匹配mipmap尺寸
                        uv0.y += 1.0 - exp(-LN2 * level0);         // 调整V偏移以访问正确的atlas区域

                        // 计算第二个级别的UV坐标
                        uv1 /= pow(2.0, level1);                    // 缩放UV以匹配mipmap尺寸
                        uv1.y += 1.0 - exp(-LN2 * level1);         // 调整V偏移以访问正确的atlas区域

                        // 采样两个级别的镜面反射数据
                        vec3 specular0 = RGBMToLinear(texture2D(tEnvSpecular, uv0)).rgb;
                        vec3 specular1 = RGBMToLinear(texture2D(tEnvSpecular, uv1)).rgb;
                        vec3 specularLight = mix(specular0, specular1, blend);  // 线性插值

                        // ========== 最终光照贡献计算 ==========
                        // 计算漫反射贡献
                        diffuse = diffuseLight * diffuseColor;

                        // 计算镜面反射贡献（包含额外的光滑表面反射增强）
                        float reflectivity = pow((1.0 - roughness), 2.0) * 0.05;  // 光滑材质的额外反射
                        specular = specularLight * (specularColor * brdf.x + brdf.y + reflectivity);
                        specular *= uEnvSpecular;  // 应用镜面反射强度控制
                    }

                    // ========== PBR片段着色器主函数 ==========
                    // 实现完整的基于物理的渲染管线
                    //
                    // 🔬 **PBR渲染流程**：
                    // 1. 材质属性采样和解析
                    // 2. 光照向量计算
                    // 3. BRDF函数计算（F、G、D）
                    // 4. 直接光照计算
                    // 5. 间接光照（IBL）计算
                    // 6. 最终颜色合成和输出
                    void main() {
                        // ========== 基础颜色采样 ==========
                        // 获取材质的基础颜色（反照率）
                        vec4 baseColor = uBaseColorFactor;  // 基础颜色因子
                        #ifdef COLOR_MAP
                            // 如果有基础颜色纹理，进行采样并转换到线性空间
                            baseColor *= SRGBtoLinear(texture2D(tBaseColor, vUv));
                        #endif

                        // ========== Alpha通道处理 ==========
                        // 获取基础透明度值
                        float alpha = baseColor.a;

                        // ========== Alpha测试 ==========
                        // 如果启用了Alpha遮罩，丢弃低于阈值的像素
                        #ifdef ALPHA_MASK
                            if (alpha < uAlphaCutoff) discard;  // 提前退出，优化性能
                        #endif

                        // ========== 金属度/粗糙度采样 ==========
                        // 从RM贴图中采样材质属性
                        // 通道布局：R=遮挡，G=粗糙度，B=金属度，A=未使用
                        vec4 rmSample = vec4(1);  // 默认值（无纹理时）
                        #ifdef RM_MAP
                            rmSample *= texture2D(tRM, vUv);  // 采样RM纹理
                        #endif
                        // 应用因子并限制在合理范围内（避免除零和过度反射）
                        float roughness = clamp(rmSample.g * uRoughness, 0.04, 1.0);  // 粗糙度
                        float metallic = clamp(rmSample.b * uMetallic, 0.04, 1.0);    // 金属度

                        // ========== PBR材质属性计算 ==========
                        // 根据金属度工作流计算漫反射和镜面反射颜色

                        // 非金属材质的基础反射率（约4%，对应折射率1.5）
                        vec3 f0 = vec3(0.04);

                        // 🔬 **金属度工作流原理**：
                        // - 非金属：漫反射=基础颜色，镜面反射=4%灰度
                        // - 金属：漫反射=黑色，镜面反射=基础颜色
                        // - 混合：根据金属度在两者间插值
                        vec3 diffuseColor = baseColor.rgb * (vec3(1.0) - f0) * (1.0 - metallic);  // 漫反射颜色
                        vec3 specularColor = mix(f0, baseColor.rgb, metallic);                     // 镜面反射颜色

                        // ========== Fresnel反射参数 ==========
                        // 设置垂直入射和掠射角的反射率
                        vec3 specularEnvR0 = specularColor;  // F0：垂直入射时的反射率
                        // F90：掠射角时的反射率（通常接近1.0，这里根据材质亮度调整）
                        vec3 specularEnvR90 = vec3(clamp(max(max(specularColor.r, specularColor.g), specularColor.b) * 25.0, 0.0, 1.0));

                        // ========== 光照向量计算 ==========
                        // 计算光照计算所需的各种向量
                        vec3 N = getNormal();                                    // 表面法线（世界空间）
                        vec3 V = normalize(cameraPosition - vMPos);              // 视线向量（指向相机）
                        vec3 L = normalize(uLightDirection);                     // 光线向量（指向光源）
                        vec3 H = normalize(L + V);                               // 半角向量（L和V的平分线）
                        vec3 reflection = normalize(reflect(-V, N));             // 反射向量（用于环境映射）

                        // ========== 光照向量点积计算 ==========
                        // 计算各种向量间的点积，用于BRDF函数
                        // 使用小的偏移值避免除零错误和数值不稳定
                        float NdL = clamp(dot(N, L), 0.001, 1.0);    // 法线与光线夹角（Lambert项）
                        float NdV = clamp(abs(dot(N, V)), 0.001, 1.0); // 法线与视线夹角（用于Fresnel）
                        float NdH = clamp(dot(N, H), 0.0, 1.0);      // 法线与半角向量夹角（用于分布函数）
                        float LdH = clamp(dot(L, H), 0.0, 1.0);      // 光线与半角向量夹角
                        float VdH = clamp(dot(V, H), 0.0, 1.0);      // 视线与半角向量夹角（用于Fresnel）

                        // ========== BRDF函数计算 ==========
                        // 计算Cook-Torrance BRDF的三个组成部分

                        // F：Fresnel反射项（控制反射强度随视角的变化）
                        vec3 F = specularReflection(specularEnvR0, specularEnvR90, VdH);
                        // G：几何遮挡项（考虑微表面间的相互遮挡）
                        float G = geometricOcclusion(NdL, NdV, roughness);
                        // D：法线分布项（控制镜面反射的形状和强度）
                        float D = microfacetDistribution(roughness, NdH);

                        // ========== 直接光照BRDF计算 ==========
                        // 🔬 **Cook-Torrance BRDF公式**：
                        // BRDF = kd * (albedo/π) + ks * (F*G*D)/(4*NdL*NdV)
                        // 其中：kd = 1-F（能量守恒），ks = F

                        // 漫反射贡献：Lambert模型除以π进行归一化
                        vec3 diffuseContrib = (1.0 - F) * (diffuseColor / PI);
                        // 镜面反射贡献：Cook-Torrance镜面反射BRDF
                        vec3 specContrib = F * G * D / (4.0 * NdL * NdV);

                        // ========== 直接光照计算 ==========
                        // 应用Lambert余弦定律和光源颜色
                        vec3 color = NdL * uLightColor * (diffuseContrib + specContrib);

                        // ========== 透明表面Alpha增强 ==========
                        // 为透明材质（如玻璃）添加镜面反射到Alpha通道
                        // 这样可以在透明表面上看到反射效果
                        alpha = max(alpha, max(max(specContrib.r, specContrib.g), specContrib.b));

                        // ========== 基于图像的光照（IBL）计算 ==========
                        // 计算来自环境贴图的间接光照贡献
                        vec3 diffuseIBL;   // 环境漫反射贡献
                        vec3 specularIBL;  // 环境镜面反射贡献
                        getIBLContribution(diffuseIBL, specularIBL, NdV, roughness, N, reflection, diffuseColor, specularColor);

                        // ========== 合成最终颜色 ==========
                        // 将直接光照和间接光照相加
                        color += diffuseIBL + specularIBL;

                        // ========== IBL镜面反射Alpha增强 ==========
                        // 同样为IBL镜面反射添加Alpha贡献
                        alpha = max(alpha, max(max(specularIBL.r, specularIBL.g), specularIBL.b));

                        // ========== 环境遮挡处理 ==========
                        #ifdef OCC_MAP
                            // TODO: 实现正确的环境遮挡应用
                            // 环境遮挡应该主要影响间接光照，而不是直接光照
                            // color *= SRGBtoLinear(texture2D(tOcclusion, vUv)).rgb;
                        #endif

                        // ========== 自发光处理 ==========
                        #ifdef EMISSIVE_MAP
                            // 添加自发光贡献（不受光照影响的发光效果）
                            vec3 emissive = SRGBtoLinear(texture2D(tEmissive, vUv)).rgb * uEmissive;
                            color += emissive;  // 直接相加，不参与光照计算
                        #endif

                        // ========== 最终输出 ==========
                        // 将线性空间颜色转换为sRGB显示空间
                        gl_FragColor.rgb = linearToSRGB(color);

                        // 应用最终的Alpha值（覆盖之前的镜面反射Alpha增强）
                        gl_FragColor.a = alpha * uAlpha;
                    }
                `,
            };

            {
                // ========== 初始化WebGL渲染器 ==========
                // dpr: 2 表示设备像素比为2，提供高分辨率渲染
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl; // 获取WebGL上下文
                document.body.appendChild(gl.canvas); // 将canvas添加到页面
                gl.clearColor(0.1, 0.1, 0.1, 1); // 设置背景色为深灰色

                // ========== 初始化相机 ==========
                // 设置近裁剪面和远裁剪面，适合大型3D模型
                const camera = new Camera(gl, { near: 1, far: 1000 });
                // camera.position.set(60, 25, -60);  // 备用相机位置
                camera.position.set(30, 15, -30); // 设置相机初始位置

                // ========== 创建轨道控制器 ==========
                // 提供鼠标交互功能：拖拽旋转、滚轮缩放、右键平移
                const controls = new Orbit(camera);
                // controls.target.y = 25;  // 备用目标点设置

                // ========== 窗口大小调整处理 ==========
                function resize() {
                    // 设置渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机的宽高比以匹配新的窗口尺寸
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                // ========== 创建场景根节点 ==========
                const scene = new Transform();

                // ========== GLTF模型变量 ==========
                let gltf; // 存储加载的GLTF模型数据

                // ========== 加载PBR渲染所需的公共纹理 ==========
                // 这些纹理被所有材质共享，用于基于图像的光照（IBL）

                // BRDF查找表：预计算的双向反射分布函数数据
                const lutTexture = TextureLoader.load(gl, {
                    src: 'assets/pbr/lut.png',
                });

                // 环境漫反射纹理：用于环境光的漫反射计算
                const envDiffuseTexture = TextureLoader.load(gl, {
                    src: 'assets/sunset-diffuse-RGBM.png',
                });

                // 环境镜面反射纹理：用于环境光的镜面反射计算
                const envSpecularTexture = TextureLoader.load(gl, {
                    src: 'assets/sunset-specular-RGBM.png',
                });

                {
                    // ========== 初始化应用程序 ==========
                    loadInitial(); // 加载默认模型
                    handlers(); // 设置拖拽处理器
                }

                // ========== 加载默认GLTF模型 ==========
                async function loadInitial() {
                    // 加载预设的背包模型（Hershel背包）
                    gltf = await GLTFLoader.load(gl, `assets/gltf/hershel.glb`);
                    addGLTF(gltf); // 将模型添加到场景
                }

                // ========== 设置拖拽事件处理器 ==========
                // 允许用户拖拽GLTF文件到canvas上进行加载
                function handlers() {
                    gl.canvas.addEventListener('dragover', over); // 拖拽悬停事件
                    gl.canvas.addEventListener('drop', drop); // 拖拽释放事件
                }

                // ========== 拖拽悬停处理 ==========
                function over(e) {
                    e.preventDefault(); // 阻止默认行为，允许拖拽
                }

                // ========== 拖拽释放处理 ==========
                function drop(e) {
                    e.preventDefault(); // 阻止默认行为
                    const file = e.dataTransfer.files[0]; // 获取拖拽的文件
                    const reader = new FileReader();
                    reader.readAsArrayBuffer(file); // 以ArrayBuffer格式读取文件

                    reader.onload = async function (e) {
                        let desc;
                        const textDecoder = new TextDecoder();

                        // ========== 检测文件格式 ==========
                        // 检查文件头部是否为'glTF'（GLB二进制格式）
                        if (textDecoder.decode(new Uint8Array(e.target.result, 0, 4)) === 'glTF') {
                            // GLB格式：解包二进制数据
                            desc = GLTFLoader.unpackGLB(e.target.result);
                        } else {
                            // GLTF格式：解析JSON数据
                            desc = JSON.parse(textDecoder.decode(e.target.result));
                        }

                        const dir = ''; // 资源目录（空字符串表示相对路径）
                        // 解析GLTF数据并创建3D对象
                        gltf = await GLTFLoader.parse(gl, desc, dir);
                        addGLTF(gltf); // 将新模型添加到场景
                    };
                }

                // ========== 将GLTF模型添加到场景 ==========
                // 处理GLTF模型的场景图集成、材质设置和相机自适应
                //
                // 🔬 **GLTF集成流程**：
                // 1. 清除现有场景内容
                // 2. 添加新模型到场景图
                // 3. 为每个网格创建PBR着色器程序
                // 4. 计算模型包围盒
                // 5. 自动调整相机位置和参数
                //
                // @param {Object} gltf - 解析后的GLTF模型数据
                function addGLTF(gltf) {
                    // ========== 清除现有场景 ==========
                    // 移除所有现有的子对象，为新模型腾出空间
                    scene.children.forEach((child) => child.setParent(null));
                    console.log(gltf); // 调试输出：查看GLTF结构

                    // ========== 添加GLTF场景到渲染场景 ==========
                    // GLTF可能包含多个场景，选择主场景或第一个场景
                    const s = gltf.scene || gltf.scenes[0];
                    s.forEach((root) => {
                        // 将GLTF根节点添加为场景的子节点
                        root.setParent(scene);

                        // ========== 遍历场景图并设置着色器程序 ==========
                        // 为每个包含几何体的节点创建PBR着色器程序
                        root.traverse((node) => {
                            if (node.program) {
                                // 使用自定义的PBR着色器替换默认程序
                                node.program = createProgram(node);
                            }
                        });
                    });

                    // ========== 更新世界变换矩阵 ==========
                    // 计算所有节点的世界空间变换矩阵，用于包围盒计算
                    scene.updateMatrixWorld();

                    // ========== 初始化包围盒计算变量 ==========
                    // 计算整个模型的轴对齐包围盒（AABB）
                    const min = new Vec3(+Infinity); // 包围盒最小点
                    const max = new Vec3(-Infinity); // 包围盒最大点
                    const center = new Vec3(); // 包围盒中心点
                    const scale = new Vec3(); // 包围盒尺寸

                    // 临时变量用于单个网格的包围盒计算
                    const boundsMin = new Vec3(); // 单个网格包围盒最小点
                    const boundsMax = new Vec3(); // 单个网格包围盒最大点
                    const boundsCenter = new Vec3(); // 单个网格包围盒中心
                    const boundsScale = new Vec3(); // 单个网格的世界空间缩放

                    // ========== 遍历所有网格计算总包围盒 ==========
                    // 遍历GLTF模型中的所有网格组和图元
                    gltf.meshes.forEach((group) => {
                        group.primitives.forEach((mesh) => {
                            // 跳过未附加到场景图的网格
                            if (!mesh.parent) return;

                            // TODO: 对于骨骼动画，应该遍历关节而不是网格
                            // if (mesh instanceof GLTFSkin) return; // 跳过骨骼几何体

                            // ========== 确保几何体有包围球 ==========
                            // 如果几何体没有预计算的包围球，现在计算
                            if (!mesh.geometry.bounds) mesh.geometry.computeBoundingSphere();

                            // ========== 将包围球转换到世界空间 ==========
                            // 将几何体的包围球中心转换到世界坐标系
                            boundsCenter.copy(mesh.geometry.bounds.center).applyMatrix4(mesh.worldMatrix);

                            // ========== 计算世界空间缩放影响 ==========
                            // 获取世界变换矩阵的缩放分量
                            mesh.worldMatrix.getScaling(boundsScale);
                            // 取最大缩放轴作为半径缩放因子（处理非均匀缩放）
                            const radiusScale = Math.max(Math.max(boundsScale[0], boundsScale[1]), boundsScale[2]);
                            // 计算世界空间中的实际半径
                            const radius = mesh.geometry.bounds.radius * radiusScale;

                            // ========== 计算网格的轴对齐包围盒 ==========
                            // 从球心向各方向扩展半径得到包围盒
                            boundsMin.set(-radius).add(boundsCenter); // 最小点 = 中心 - 半径
                            boundsMax.set(+radius).add(boundsCenter); // 最大点 = 中心 + 半径

                            // ========== 更新总包围盒 ==========
                            // 将当前网格的包围盒合并到总包围盒中
                            for (let i = 0; i < 3; i++) {
                                min[i] = Math.min(min[i], boundsMin[i]); // 取最小值
                                max[i] = Math.max(max[i], boundsMax[i]); // 取最大值
                            }
                        });
                    });
                    // ========== 计算包围盒属性 ==========
                    scale.sub(max, min); // 包围盒尺寸 = 最大点 - 最小点
                    const maxRadius = Math.max(Math.max(scale[0], scale[1]), scale[2]) * 0.5; // 最大半径
                    center.add(min, max).divide(2); // 包围盒中心 = (最小点 + 最大点) / 2

                    // ========== 自动调整相机位置 ==========
                    // 根据模型大小和位置自动设置最佳观察角度
                    //
                    // 🔬 **相机定位策略**：
                    // 1. 设置观察方向（右上后方，常用的3D建模视角）
                    // 2. 根据模型大小计算合适的距离
                    // 3. 将相机定位在模型中心周围
                    camera.position
                        .set(1, 0.5, -1) // 设置观察方向向量（右上后方）
                        .normalize() // 归一化为单位向量
                        .multiply(maxRadius * 2.5) // 乘以适当的距离因子
                        .add(center); // 加上模型中心偏移

                    // ========== 设置轨道控制器目标 ==========
                    controls.target.copy(center); // 设置旋转中心为模型中心
                    controls.forcePosition(); // 强制更新控制器状态

                    // ========== 调整相机投影参数 ==========
                    // 根据模型大小设置合适的近远裁剪面
                    const far = maxRadius * 5; // 远裁剪面：确保整个模型都在视野内
                    const near = far * 0.001; // 近裁剪面：避免Z-fighting，保持精度
                    camera.perspective({ near, far }); // 更新透视投影参数
                }

                // ========== 创建PBR着色器程序 ==========
                // 根据GLTF材质属性动态生成着色器程序
                //
                // 🔬 **着色器程序创建流程**：
                // 1. 提取GLTF材质属性
                // 2. 根据WebGL版本添加适当的前缀
                // 3. 根据可用属性和纹理生成预处理器定义
                // 4. 设置uniform变量和纹理
                // 5. 配置渲染状态
                //
                // @param {Object} node - 包含几何体和材质信息的场景节点
                // @returns {Program} 配置好的着色器程序对象
                function createProgram(node) {
                    // ========== 提取GLTF材质属性 ==========
                    const gltf = node.program.gltfMaterial || {}; // 获取GLTF材质数据
                    let { vertex, fragment } = shader; // 获取基础着色器代码

                    // ========== WebGL版本兼容性处理 ==========
                    // 根据WebGL版本添加适当的着色器前缀

                    // WebGL 2.0顶点着色器前缀
                    const vertexPrefix = renderer.isWebgl2
                        ? /* glsl */ `#version 300 es
                        #define attribute in      // WebGL2使用in替代attribute
                        #define varying out       // WebGL2使用out替代varying
                        #define texture2D texture // WebGL2统一使用texture函数
                    `
                        : ``; // WebGL 1.0不需要前缀

                    // WebGL 2.0片段着色器前缀
                    const fragmentPrefix = renderer.isWebgl2
                        ? /* glsl */ `#version 300 es
                        precision highp float;
                        #define varying in        // WebGL2使用in替代varying
                        #define texture2D texture // WebGL2统一使用texture函数
                        #define gl_FragColor FragColor  // WebGL2使用自定义输出变量
                        out vec4 FragColor;       // 声明颜色输出变量
                    `
                        : /* glsl */ `#extension GL_OES_standard_derivatives : enable
                        precision highp float;   // WebGL1需要启用导数扩展
                    `;

                    // ========== 动态生成预处理器定义 ==========
                    // 根据几何体属性和材质纹理生成条件编译指令
                    let defines = `
                        ${node.geometry.attributes.uv ? `#define UV` : ``}                    // UV坐标可用
                        ${node.geometry.attributes.normal ? `#define NORMAL` : ``}            // 法线可用
                        ${node.geometry.isInstanced ? `#define INSTANCED` : ``}               // 实例化渲染
                        ${node.boneTexture ? `#define SKINNING` : ``}                         // 骨骼动画
                        ${gltf.alphaMode === 'MASK' ? `#define ALPHA_MASK` : ``}              // Alpha遮罩
                        ${gltf.baseColorTexture ? `#define COLOR_MAP` : ``}                   // 基础颜色纹理
                        ${gltf.normalTexture ? `#define NORMAL_MAP` : ``}                     // 法线贴图
                        ${gltf.metallicRoughnessTexture ? `#define RM_MAP` : ``}              // 金属度/粗糙度贴图
                        ${gltf.occlusionTexture ? `#define OCC_MAP` : ``}                     // 环境遮挡贴图
                        ${gltf.emissiveTexture ? `#define EMISSIVE_MAP` : ``}                 // 自发光贴图
                    `;

                    // ========== 组装最终着色器代码 ==========
                    vertex = vertexPrefix + defines + vertex; // 顶点着色器 = 前缀 + 定义 + 主体
                    fragment = fragmentPrefix + defines + fragment; // 片段着色器 = 前缀 + 定义 + 主体

                    // ========== 创建着色器程序对象 ==========
                    // 配置所有uniform变量、纹理和渲染状态
                    const program = new Program(gl, {
                        vertex, // 编译后的顶点着色器代码
                        fragment, // 编译后的片段着色器代码
                        uniforms: {
                            // ========== 基础材质属性 ==========
                            // 基础颜色因子（RGBA，默认白色不透明）
                            uBaseColorFactor: { value: gltf.baseColorFactor || [1, 1, 1, 1] },
                            // 基础颜色纹理（反照率贴图）
                            tBaseColor: { value: gltf.baseColorTexture ? gltf.baseColorTexture.texture : null },

                            // ========== 金属度/粗糙度属性 ==========
                            // 金属度/粗糙度纹理（R=遮挡，G=粗糙度，B=金属度）
                            tRM: { value: gltf.metallicRoughnessTexture ? gltf.metallicRoughnessTexture.texture : null },
                            // 粗糙度因子（0=完全光滑，1=完全粗糙）
                            uRoughness: { value: gltf.roughnessFactor !== undefined ? gltf.roughnessFactor : 1 },
                            // 金属度因子（0=非金属，1=金属）
                            uMetallic: { value: gltf.metallicFactor !== undefined ? gltf.metallicFactor : 1 },

                            // ========== 法线贴图属性 ==========
                            // 法线贴图纹理（切线空间法线）
                            tNormal: { value: gltf.normalTexture ? gltf.normalTexture.texture : null },
                            // 法线贴图强度缩放因子
                            uNormalScale: { value: gltf.normalTexture ? gltf.normalTexture.scale || 1 : 1 },

                            // ========== 环境遮挡属性 ==========
                            // 环境遮挡纹理（影响间接光照）
                            tOcclusion: { value: gltf.occlusionTexture ? gltf.occlusionTexture.texture : null },

                            // ========== 自发光属性 ==========
                            // 自发光纹理（不受光照影响的发光效果）
                            tEmissive: { value: gltf.emissiveTexture ? gltf.emissiveTexture.texture : null },
                            // 自发光颜色因子（RGB，默认黑色无发光）
                            uEmissive: { value: gltf.emissiveFactor || [0, 0, 0] },

                            // ========== IBL（基于图像的光照）纹理 ==========
                            // BRDF查找表：预计算的双向反射分布函数
                            tLUT: { value: lutTexture },
                            // 环境漫反射纹理：用于环境光漫反射计算
                            tEnvDiffuse: { value: envDiffuseTexture },
                            // 环境镜面反射纹理：用于环境光镜面反射计算
                            tEnvSpecular: { value: envSpecularTexture },
                            // 环境漫反射强度控制（0-1）
                            uEnvDiffuse: { value: 0.5 },
                            // 环境镜面反射强度控制（0-1）
                            uEnvSpecular: { value: 0.5 },

                            // ========== 直接光照属性 ==========
                            // 主光源方向（归一化向量）
                            uLightDirection: { value: new Vec3(0, 1, 1) },
                            // 主光源颜色和强度（RGB，值>1表示HDR）
                            uLightColor: { value: new Vec3(2.5) },

                            // ========== 透明度控制 ==========
                            // 全局透明度因子（0=完全透明，1=完全不透明）
                            uAlpha: { value: 1 },
                            // Alpha测试阈值（用于Alpha遮罩模式）
                            uAlphaCutoff: { value: gltf.alphaCutoff },
                        },
                        // ========== 渲染状态配置 ==========
                        // 透明度混合：BLEND模式启用Alpha混合
                        transparent: gltf.alphaMode === 'BLEND',
                        // 面剔除：双面材质禁用背面剔除，否则剔除背面
                        cullFace: gltf.doubleSided ? false : gl.BACK,
                    });

                    return program; // 返回配置完成的着色器程序
                }

                // ========== 启动渲染循环 ==========
                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update); // 请求下一帧动画

                    // ========== 播放GLTF动画 ==========
                    // 如果模型包含动画，播放第一个动画
                    if (gltf && gltf.animations && gltf.animations.length) {
                        let { animation } = gltf.animations[0]; // 获取第一个动画
                        animation.elapsed += 0.01; // 增加动画时间（控制播放速度）
                        animation.update(); // 更新动画状态
                    }

                    // ========== 更新控制器和渲染场景 ==========
                    controls.update(); // 更新轨道控制器（处理鼠标交互）
                    // 渲染场景：
                    // sort: false - 禁用深度排序（提高性能）
                    // frustumCull: false - 禁用视锥体剔除（确保所有对象都被渲染）
                    renderer.render({ scene, camera, sort: false, frustumCull: false });
                }
            }
        </script>
    </body>
</html>
