<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Paths</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Paths</div>
        <script type="module">
            // ========== 导入OGL库的核心组件 ==========
            // Renderer: WebGL渲染器，负责创建WebGL上下文和渲染场景
            // Camera: 相机，定义视角和投影矩阵
            // Transform: 变换节点，用于构建场景图层次结构
            // Program: 着色器程序，包含顶点着色器和片段着色器
            // Mesh: 网格对象，结合几何体和着色器程序
            // Sphere: 球体几何体
            // Polyline: 折线几何体，用于渲染路径
            // Vec3: 三维向量类
            // Color: 颜色类
            // Path: 路径类，用于创建和操作3D路径
            import { Renderer, Camera, Transform, Program, Mesh, Sphere, Polyline, Vec3, Color, Path } from '../src/index.js';

            // ========== 顶点着色器 ==========
            // 用于处理球体几何体的顶点变换和法线计算
            const vertex = /* glsl */ `
                attribute vec3 position;  // 顶点位置属性
                attribute vec3 normal;    // 顶点法线属性

                uniform mat4 modelViewMatrix;   // 模型视图矩阵
                uniform mat4 projectionMatrix;  // 投影矩阵
                uniform mat3 normalMatrix;      // 法线矩阵（用于变换法线）

                varying vec3 vNormal;  // 传递给片段着色器的法线

                void main() {
                    // 将法线从模型空间变换到视图空间并归一化
                    vNormal = normalize(normalMatrix * normal);
                    // 计算最终的顶点位置（模型空间 -> 视图空间 -> 裁剪空间）
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // ========== 片段着色器1 - 蓝色球体 ==========
            // 为路径左侧的球体提供蓝色材质
            const fragmentColor1 = /* glsl */ `
                precision highp float;
                varying vec3 vNormal;  // 从顶点着色器接收的法线

                void main() {
                    vec3 normal = normalize(vNormal);
                    // 计算简单的方向光照：光源方向为(-0.3, 0.8, 0.6)
                    float lighting = dot(normal, normalize(vec3(-0.3, 0.8, 0.6)));
                    // 基础颜色为蓝色(0.24, 0.84, 1.0)，加上光照效果
                    gl_FragColor.rgb = vec3(0.24, 0.84, 1.0) + lighting * 0.1;
                    gl_FragColor.a = 1.0;
                }
            `;

            // ========== 片段着色器2 - 红色球体 ==========
            // 为路径右侧的球体提供红色材质
            const fragmentColor2 = /* glsl */ `
                precision highp float;
                varying vec3 vNormal;  // 从顶点着色器接收的法线

                void main() {
                    vec3 normal = normalize(vNormal);
                    // 计算简单的方向光照：光源方向为(-0.3, 0.8, 0.6)
                    float lighting = dot(normal, normalize(vec3(-0.3, 0.8, 0.6)));
                    // 基础颜色为红色(0.93, 0.25, 0.41)，加上光照效果
                    gl_FragColor.rgb = vec3(0.93, 0.25, 0.41) + lighting * 0.1;
                    gl_FragColor.a = 1.0;
                }
            `;

            {
                // ========== 初始化WebGL渲染器 ==========
                // dpr: 2 表示设备像素比为2，提供高分辨率渲染
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl; // 获取WebGL上下文
                document.body.appendChild(gl.canvas); // 将canvas添加到页面
                gl.clearColor(1, 1, 1, 1); // 设置背景色为白色

                // ========== 初始化相机 ==========
                // fov: 35度的视野角度，创建透视投影相机
                const camera = new Camera(gl, { fov: 35 });

                // ========== 窗口大小调整处理 ==========
                function resize() {
                    // 设置渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机的宽高比以匹配新的窗口尺寸
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                // ========== 创建场景根节点 ==========
                const scene = new Transform();

                // ========== 创建球体几何体和着色器程序 ==========
                const sphereGeom = new Sphere(gl); // 球体几何体，用于路径上的标记点
                // 蓝色球体着色器程序（用于路径左侧）
                const sphereProg1 = new Program(gl, { vertex, fragment: fragmentColor1 });
                // 红色球体着色器程序（用于路径右侧）
                const sphereProg2 = new Program(gl, { vertex, fragment: fragmentColor2 });

                // ========== 路径坐标数据 ==========
                // 这是一个预定义的3D路径坐标数组，用于创建复杂的贝塞尔曲线路径
                // 前3个数字是moveTo命令的起始点坐标，其余每9个数字为一组bezierCurveTo命令
                // 每组9个数字的格式：[控制点1_x, 控制点1_y, 控制点1_z, 控制点2_x, 控制点2_y, 控制点2_z, 终点_x, 终点_y, 终点_z]
                const pathCoords = [
                    -1.0, 0.0, 0.0, -0.9368709325790405, 0.1762027144432068, 0.04529910162091255, -0.7061498761177063, 0.089231938123703, 0.19381383061408997, -0.6332840919494629, 0.2674865424633026,
                    0.1930660903453827, -0.5615311861038208, 0.4430185854434967, 0.1923297792673111, -0.7734173536300659, 0.5522762537002563, 0.08718681335449219, -0.7070468664169312,
                    0.7070468664169312, 0.0, -0.6498651504516602, 0.8403899073600769, -0.07511603832244873, -0.5399253368377686, 0.8584117889404297, -0.16668838262557983, -0.38917776942253113,
                    0.921392560005188, -0.1677459478378296, -0.23391252756118774, 0.9862607717514038, -0.16883520781993866, -0.14611071348190308, 1.0727460384368896, -0.04093937203288078, 0.0, 1.0,
                    0.0, 0.1674281358718872, 0.9166403412818909, 0.046912387013435364, 0.07777689397335052, 0.6478093862533569, -0.06732462346553802, 0.2400655597448349, 0.5683639645576477, 0.0,
                    0.4298238456249237, 0.4754713177680969, 0.07872024923563004, 0.49316900968551636, 0.7766210436820984, 0.32597726583480835, 0.7070468664169312, 0.7070468664169312,
                    0.3101717531681061, 0.8896822333335876, 0.647635817527771, 0.29667505621910095, 0.8556811809539795, 0.5579423308372498, 0.06533028930425644, 0.921392560005188, 0.38917776942253113,
                    0.0, 0.9742976427078247, 0.2533031702041626, -0.05259828269481659, 1.0508142709732056, 0.14183028042316437, -0.036462459713220596, 1.0, 0.0, 0.0, 0.9368709325790405,
                    -0.1762027144432068, 0.04529910162091255, 0.7061498761177063, -0.089231938123703, 0.19381383061408997, 0.6332840919494629, -0.2674865424633026, 0.1930660903453827,
                    0.5615311861038208, -0.4430185854434967, 0.1923297792673111, 0.7734173536300659, -0.5522762537002563, 0.08718681335449219, 0.7070468664169312, -0.7070468664169312, 0.0,
                    0.6498651504516602, -0.8403899073600769, -0.07511603832244873, 0.5399253368377686, -0.8584117889404297, -0.16668838262557983, 0.38917776942253113, -0.921392560005188,
                    -0.1677459478378296, 0.23391252756118774, -0.9862607717514038, -0.16883520781993866, 0.14611071348190308, -1.0727460384368896, -0.04093937203288078, 0.0, -1.0, 0.0,
                    -0.1674281358718872, -0.9166403412818909, 0.046912387013435364, -0.07777689397335052, -0.6478093862533569, -0.06732462346553802, -0.2400655597448349, -0.5683639645576477, 0.0,
                    -0.4298238456249237, -0.4754713177680969, 0.07872024923563004, -0.49316900968551636, -0.7766210436820984, 0.32597726583480835, -0.7070468664169312, -0.7070468664169312,
                    0.3101717531681061, -0.8896822333335876, -0.647635817527771, 0.29667505621910095, -0.8556811809539795, -0.5579423308372498, 0.06533028930425644, -0.921392560005188,
                    -0.38917776942253113, 0.0, -0.9742976427078247, -0.2533031702041626, -0.05259828269481659, -1.0508142709732056, -0.14183028042316437, -0.036462459713220596, -1.0, 0.0, 0.0,
                ];

                // ========== 构建3D贝塞尔曲线路径 ==========
                // 使用Path类创建一个复杂的3D路径，支持多种路径命令
                // 可用命令：moveTo（移动到）、bezierCurveTo（三次贝塞尔曲线）、quadraticCurveTo（二次贝塞尔曲线）、lineTo（直线）
                const path = new Path();
                // 设置路径起始点：从pathCoords数组的前3个坐标开始
                path.moveTo(new Vec3(pathCoords[0], pathCoords[1], pathCoords[2]));
                // 遍历剩余坐标，每9个数字构成一个三次贝塞尔曲线段
                for (let i = 3; i < pathCoords.length; i += 9) {
                    // 第一个控制点
                    const cp1 = new Vec3(pathCoords[i + 0], pathCoords[i + 1], pathCoords[i + 2]);
                    // 第二个控制点
                    const cp2 = new Vec3(pathCoords[i + 3], pathCoords[i + 4], pathCoords[i + 5]);
                    // 曲线终点
                    const p = new Vec3(pathCoords[i + 6], pathCoords[i + 7], pathCoords[i + 8]);
                    // 添加三次贝塞尔曲线段到路径
                    path.bezierCurveTo(cp1, cp2, p);
                }

                // ========== 设置路径倾斜函数 ==========
                // 倾斜函数接收三个参数：当前角度、路径长度偏移量[0..1]、路径对象实例
                // 返回新的倾斜角度，这里实现了沿路径的螺旋扭转效果
                // 8 * 360 * t 表示沿路径完整长度旋转8圈（8 * 360度）
                path.tiltFunction = (angle, t, path) => 8 * 360 * t;

                // ========== 路径细分和点生成 ==========
                // 将路径细分为256个点，数值越大路径越平滑但性能开销越大
                const pathSubdivisions = 256;
                // 从路径生成实际的3D点数组
                const points = path.getPoints(pathSubdivisions);

                // ========== 创建路径可视化折线 ==========
                // 使用Polyline类将路径点转换为可渲染的折线几何体
                // 参考示例：https://oframe.github.io/ogl/examples/?src=polylines.html
                const polyline = new Polyline(gl, {
                    points, // 路径点数组
                    uniforms: {
                        uColor: { value: new Color('#ddd') }, // 折线颜色：浅灰色
                        uThickness: { value: 3 }, // 折线粗细：3像素
                    },
                });

                // ========== 创建折线网格对象并添加到场景 ==========
                const mesh = new Mesh(gl, { geometry: polyline.geometry, program: polyline.program });
                mesh.setParent(scene); // 将折线添加到场景根节点

                // ========== 计算Frenet标架 ==========
                // Frenet标架是描述3D曲线局部几何性质的数学工具，包含切线、法线和副法线
                // 参考论文：http://www.cs.indiana.edu/pub/techreports/TR425.pdf
                // 第二个参数true表示如果路径是闭合的，则路径两端会对齐
                const frenetFrames = path.computeFrenetFrames(pathSubdivisions, true);

                // ========== 沿路径创建标记球体 ==========
                // 在路径的每个点上放置两个球体（左右各一个），用于可视化路径的方向和扭转
                for (let i = 0; i < points.length - 1; i++) {
                    const point = points[i]; // 当前路径点
                    const tangent = frenetFrames.tangents[i]; // 切线向量（路径方向）
                    const normal = frenetFrames.normals[i]; // 法线向量（向上方向）
                    const binormal = frenetFrames.binormals[i]; // 副法线向量（左右方向）

                    // ========== 创建左侧蓝色球体 ==========
                    const leftSphere = new Mesh(gl, { geometry: sphereGeom, program: sphereProg1 });
                    leftSphere.scale.set(0.04); // 设置球体大小
                    // 将球体放置在路径点的左侧（沿副法线正方向偏移0.12单位）
                    leftSphere.position.add(point, binormal.clone().scale(0.12));
                    leftSphere.setParent(scene);

                    // ========== 创建右侧红色球体 ==========
                    const rightSphere = new Mesh(gl, { geometry: sphereGeom, program: sphereProg2 });
                    rightSphere.scale.set(0.04); // 设置球体大小
                    // 将球体放置在路径点的右侧（沿副法线负方向偏移0.12单位）
                    rightSphere.position.add(point, binormal.clone().scale(-0.12));
                    rightSphere.setParent(scene);
                }

                // ========== 创建动画所需的向量变量 ==========
                // 这些向量将在动画循环中重复使用，避免频繁创建新对象
                const tangentVec = new Vec3(); // 存储路径切线向量
                const normalVec = new Vec3(); // 存储插值后的法线向量
                const positionVec = new Vec3(); // 存储路径上的位置

                // ========== 启动动画循环 ==========
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update); // 请求下一帧动画

                    // ========== 计算动画进度 ==========
                    // 将时间转换为[0,1]范围的进度值，0.00001控制动画速度
                    const progress = (t * 0.00001) % 1;

                    // ========== 计算当前Frenet标架索引和插值参数 ==========
                    // 确定当前进度对应的标架索引
                    const frameIndex = Math.floor(progress * pathSubdivisions);
                    // 计算在当前标架和下一个标架之间的插值比例
                    const frameProgress = (progress * pathSubdivisions) % 1;

                    // ========== 插值计算平滑的法线向量 ==========
                    // 获取当前和下一个标架的法线向量
                    const normal1 = frenetFrames.normals[frameIndex];
                    const normal2 = frenetFrames.normals[(frameIndex + 1) % pathSubdivisions];
                    // 在两个法线之间进行线性插值，确保相机上方向的平滑过渡
                    normalVec.copy(normal1).lerp(normal2, frameProgress).normalize();

                    // ========== 获取路径上的位置和切线 ==========
                    // 根据进度获取路径上的精确位置
                    path.getPointAt(progress, positionVec);
                    // 根据进度获取路径的切线方向（前进方向）
                    path.getTangentAt(progress, tangentVec);

                    // ========== 设置相机跟随路径运动 ==========
                    // 设置相机的上方向为插值后的法线方向
                    camera.up.copy(normalVec);
                    // 设置相机位置：在路径点的后方和上方
                    // .sub(tangentVec) 向后偏移（沿切线反方向）
                    // .add(normalVec) 向上偏移（沿法线方向）
                    camera.position.copy(positionVec).sub(tangentVec).add(normalVec);

                    // 让相机始终朝向路径上的当前点
                    camera.lookAt(positionVec);

                    // ========== 渲染场景 ==========
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
