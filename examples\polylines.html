<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Polylines</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Polylines</div>
        <script type="module">
            // 导入OGL库的核心组件
            import { Renderer, Transform, Vec3, Color, Polyline } from '../src/index.js';

            /**
             * 自定义顶点着色器 - 用于创建具有锥形效果的线条
             * 这个着色器实现了屏幕空间的线条渲染，支持可变宽度和平滑连接
             */
            const vertex = /* glsl */ `
                precision highp float;

                // 顶点属性 - 从几何体传入的数据
                attribute vec3 position;  // 当前顶点的3D位置
                attribute vec3 next;      // 下一个点的3D位置（用于计算方向）
                attribute vec3 prev;      // 前一个点的3D位置（用于计算方向）
                attribute vec2 uv;        // 纹理坐标 (u: 0-1横向, v: 0-1沿线方向)
                attribute float side;     // 线条的哪一侧 (-1: 左侧, 1: 右侧)

                // 统一变量 - 全局参数
                uniform vec2 uResolution; // 画布分辨率 (宽度, 高度)
                uniform float uDPR;       // 设备像素比 (用于高DPI屏幕)
                uniform float uThickness; // 线条粗细（像素单位）

                /**
                 * 计算顶点的最终屏幕位置
                 * 这是线条渲染的核心算法
                 */
                vec4 getPosition() {
                    // 1. 将当前顶点位置转换为齐次坐标（添加w分量为1）
                    vec4 current = vec4(position, 1);

                    // 2. 计算屏幕宽高比，用于校正不同屏幕比例下的变形
                    // 这确保线条在不同宽高比的屏幕上保持正确的形状
                    vec2 aspect = vec2(uResolution.x / uResolution.y, 1);

                    // 3. 将相邻点的位置应用宽高比校正，转换到屏幕空间
                    vec2 nextScreen = next.xy * aspect;
                    vec2 prevScreen = prev.xy * aspect;

                    // 4. 计算线段的切线方向（从前一个点到下一个点的方向向量）
                    // 这个向量表示线条在当前点的走向
                    vec2 tangent = normalize(nextScreen - prevScreen);

                    // 5. 将切线向量逆时针旋转90度得到法线向量（垂直于线段的方向）
                    // 旋转矩阵：[0 -1; 1 0] 应用到 [tangent.x, tangent.y]
                    // 法线向量用于确定线条宽度的方向
                    vec2 normal = vec2(-tangent.y, tangent.x);
                    // 将法线向量除以宽高比，还原到原始坐标空间
                    normal /= aspect;

                    // 6. 实现线条的锥形效果：中间粗，两端细
                    // uv.y 沿着线条从0变化到1
                    // abs(uv.y - 0.5) * 2.0 将uv.y从[0,1]映射到[0,1]，中心为0，两端为1
                    // pow(..., 2.0) 使过渡更加平滑（二次曲线）
                    // mix(1.0, 0.1, ...) 在1.0（中间粗）和0.1（两端细）之间插值
                    normal *= mix(1.0, 0.1, pow(abs(uv.y - 0.5) * 2.0, 2.0) );

                    // 7. 当相邻两点距离过近时，缩小线条宽度以避免渲染伪影
                    float dist = length(nextScreen - prevScreen);
                    // smoothstep(0.0, 0.02, dist) 当距离小于0.02时平滑过渡到0
                    // 这防止了在点密集区域出现视觉错误
                    normal *= smoothstep(0.0, 0.02, dist);

                    // 8. 计算像素宽度比例，用于将线条宽度转换为像素单位
                    // uDPR是设备像素比，uResolution.y是屏幕高度
                    float pixelWidthRatio = 1.0 / (uResolution.y / uDPR);
                    // 计算当前顶点的像素宽度（考虑透视投影的w分量）
                    float pixelWidth = current.w * pixelWidthRatio;

                    // 9. 将法线向量缩放到最终的线条宽度
                    normal *= pixelWidth * uThickness;

                    // 10. 根据side值（-1或1）将顶点沿法线方向偏移，形成线条的两侧边缘
                    // 这是创建线条宽度的关键步骤
                    current.xy -= normal * side;

                    // 返回计算后的顶点位置
                    return current;
                }

                void main() {
                    // 设置顶点的最终位置
                    gl_Position = getPosition();
                }
            `;

            {
                // ========== 初始化WebGL渲染器 ==========
                // 创建渲染器，设置设备像素比为2以获得更清晰的显示
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;

                // 将画布添加到页面并设置背景色为浅灰色
                document.body.appendChild(gl.canvas);
                gl.clearColor(0.9, 0.9, 0.9, 1);

                // 创建场景根节点 - 所有3D对象的容器
                const scene = new Transform();

                // 存储所有线条对象的数组
                const lines = [];

                /**
                 * 窗口大小调整处理函数
                 * 当窗口大小改变时，需要更新渲染器和所有线条的分辨率
                 */
                function resize() {
                    // 更新渲染器的画布大小
                    renderer.setSize(window.innerWidth, window.innerHeight);

                    // 更新所有线条的分辨率统一变量
                    // 这确保线条在不同屏幕尺寸下保持正确的像素宽度
                    lines.forEach((line) => line.polyline.resize());
                }
                window.addEventListener('resize', resize, false);

                /**
                 * 随机数生成辅助函数
                 * @param {number} a - 最小值
                 * @param {number} b - 最大值
                 * @returns {number} a和b之间的随机数
                 */
                function random(a, b) {
                    const alpha = Math.random();
                    return a * (1.0 - alpha) + b * alpha;
                }

                // ========== 线条渲染技术说明 ==========
                // 如果您想了解更多关于几何线条绘制的知识，
                // 请阅读Matt DesLauriers的详细文章：
                // https://mattdesl.svbtle.com/drawing-lines-is-hard
                // 这是对各种方法及其陷阱的优秀分析。

                // 在这个示例中，我们创建屏幕空间的多段线。基本原理是：
                // 1. 沿着路径创建顶点几何体 - 每个点有两个顶点
                // 2. 在顶点着色器中，将每对顶点推开以产生线条宽度
                // 3. 使用三角形来填充线条区域

                // ========== 创建多条不同颜色的线条 ==========
                // 定义5种不同的颜色，创建5条动态线条
                ['#e09f7d', '#ef5d60', '#ec4067', '#a01a7d', '#311847'].forEach((color, i) => {
                    // 为每条线存储物理运动参数
                    const line = {
                        spring: random(0.02, 0.1), // 弹簧强度（控制跟随鼠标的速度）
                        friction: random(0.7, 0.95), // 摩擦力（控制运动的阻尼）
                        mouseVelocity: new Vec3(), // 鼠标速度向量
                        mouseOffset: new Vec3(random(-1, 1) * 0.02), // 随机偏移（让线条不完全重叠）
                    };

                    // 创建线条的点数组
                    // 注意：只需要传入每个点的一个Vec3 - Polyline类会自动处理
                    // 顶点的加倍以实现多段线效果
                    const count = 20; // 每条线有20个点
                    const points = (line.points = []);
                    for (let i = 0; i < count; i++) {
                        points.push(new Vec3()); // 初始化为原点，后续会在动画中更新
                    }

                    // 创建Polyline实例，传入点数组和自定义参数
                    line.polyline = new Polyline(gl, {
                        points, // 线条的点数组
                        vertex, // 自定义顶点着色器
                        uniforms: {
                            // 着色器统一变量
                            uColor: { value: new Color(color) }, // 线条颜色
                            uThickness: { value: random(20, 50) }, // 线条粗细（随机20-50像素）
                        },
                    });

                    // 将线条网格添加到场景中
                    line.polyline.mesh.setParent(scene);

                    // 将线条对象添加到数组中以便后续更新
                    lines.push(line);
                });

                // 创建线条后调用初始大小调整
                resize();

                // ========== 鼠标/触摸交互设置 ==========
                // 存储鼠标位置的向量（标准化坐标：-1到1）
                const mouse = new Vec3();

                // 检测设备是否支持触摸，添加相应的事件监听器
                if ('ontouchstart' in window) {
                    // 移动设备：监听触摸事件
                    window.addEventListener('touchstart', updateMouse, false);
                    window.addEventListener('touchmove', updateMouse, false);
                } else {
                    // 桌面设备：监听鼠标移动事件
                    window.addEventListener('mousemove', updateMouse, false);
                }

                /**
                 * 更新鼠标/触摸位置
                 * 将屏幕坐标转换为标准化的设备坐标（-1到1）
                 * @param {Event} e - 鼠标或触摸事件
                 */
                function updateMouse(e) {
                    // 处理触摸事件
                    if (e.changedTouches && e.changedTouches.length) {
                        e.x = e.changedTouches[0].pageX;
                        e.y = e.changedTouches[0].pageY;
                    }
                    // 处理鼠标事件
                    if (e.x === undefined) {
                        e.x = e.pageX;
                        e.y = e.pageY;
                    }

                    // 将屏幕坐标转换为标准化设备坐标（NDC）
                    // X: 0到width映射为-1到1
                    // Y: 0到height映射为1到-1（翻转Y轴，因为WebGL的Y轴向上）
                    mouse.set(
                        (e.x / gl.renderer.width) * 2 - 1, // X坐标：-1到1
                        (e.y / gl.renderer.height) * -2 + 1, // Y坐标：1到-1（翻转）
                        0 // Z坐标：保持为0
                    );
                }

                // 临时向量，用于计算中避免重复创建对象
                const tmp = new Vec3();

                // ========== 动画循环 ==========
                // 启动渲染循环
                requestAnimationFrame(update);

                /**
                 * 主更新函数 - 每帧调用
                 * 实现线条的物理运动和渲染
                 * @param {number} t - 时间戳（毫秒）
                 */
                function update(t) {
                    // 请求下一帧动画
                    requestAnimationFrame(update);

                    // 更新每条线的所有点
                    lines.forEach((line) => {
                        // 从后往前更新点（这样每个点都跟随前一个点）
                        for (let i = line.points.length - 1; i >= 0; i--) {
                            if (!i) {
                                // ========== 第一个点：跟随鼠标运动 ==========
                                // 使用弹簧物理系统让第一个点平滑地跟随鼠标

                                // 1. 计算目标位置（鼠标位置 + 随机偏移）
                                // 2. 计算从当前位置到目标位置的向量
                                // 3. 乘以弹簧强度得到加速度
                                tmp.copy(mouse) // 复制鼠标位置
                                    .add(line.mouseOffset) // 添加随机偏移
                                    .sub(line.points[i]) // 减去当前位置（得到方向向量）
                                    .multiply(line.spring); // 乘以弹簧强度

                                // 更新速度：添加加速度，然后应用摩擦力
                                line.mouseVelocity
                                    .add(tmp) // 添加加速度到速度
                                    .multiply(line.friction); // 应用摩擦力（减速）

                                // 更新位置：添加速度到当前位置
                                line.points[i].add(line.mouseVelocity);
                            } else {
                                // ========== 其他点：跟随前一个点 ==========
                                // 使用线性插值让每个点平滑地跟随前一个点
                                // lerp(a, b, t) = a + (b - a) * t
                                // 这里t=0.9意味着每帧移动到前一个点位置的90%
                                line.points[i].lerp(line.points[i - 1], 0.9);
                            }
                        }

                        // 更新线条的几何体数据
                        // 这会重新计算所有顶点的位置、前一个点和下一个点的信息
                        line.polyline.updateGeometry();
                    });

                    // 渲染场景
                    renderer.render({ scene });
                }
            }
        </script>
    </body>
</html>
