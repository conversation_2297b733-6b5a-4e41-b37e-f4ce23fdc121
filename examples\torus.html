<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Torus</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Torus</div>
        <script type="module">
            import { Renderer, Camera, Orbit, Transform, Program, Torus, Mesh } from '../src/index.js';

            const vertex = /* glsl */ `
                attribute vec3 position;
                attribute vec3 normal;

                uniform mat3 normalMatrix;
                uniform mat4 modelViewMatrix;
                uniform mat4 projectionMatrix;

                varying vec3 vNormal;

                void main() {
                    vNormal = normalize(normalMatrix * normal);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            const fragment = /* glsl */ `
                precision highp float;

                varying vec3 vNormal;

                void main() {
                    gl_FragColor.rgb = normalize(vNormal);
                    gl_FragColor.a = 1.0;
                }
            `;

            {
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl;
                document.body.appendChild(gl.canvas);
                gl.clearColor(1, 1, 1, 1);

                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(5, 3, 6);
                camera.lookAt([0, 0, 0]);

                function resize() {
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize();

                const scene = new Transform();
                const controls = new Orbit(camera);

                const program = new Program(gl, {
                    vertex,
                    fragment,
                });

                const torusGeometry = new Torus(gl, {
                    radius: 1,
                    tube: 0.4,
                    radialSegments: 16,
                    tubularSegments: 32,
                });

                const torus = new Mesh(gl, { geometry: torusGeometry, program });
                torus.setParent(scene);

                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update);

                    torus.rotation.x += 0.001;
                    torus.rotation.y += 0.005;
                    torus.rotation.z += 0.003;

                    controls.update();
                    renderer.render({ scene, camera });
                }
            }
        </script>
    </body>
</html>
