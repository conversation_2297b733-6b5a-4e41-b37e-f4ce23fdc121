<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Tube</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info">Tube</div>
        <script type="module">
            // ========== 导入OGL库的核心组件 ==========
            // Renderer: WebGL渲染器，负责创建WebGL上下文和渲染场景
            // Camera: 相机，定义视角和投影矩阵
            // Transform: 变换节点，用于构建场景图层次结构
            // Program: 着色器程序，包含顶点着色器和片段着色器
            // Mesh: 网格对象，结合几何体和着色器程序
            // Sphere: 球体几何体
            // Orbit: 轨道控制器，提供鼠标交互控制相机
            // Vec3: 三维向量类
            // Color: 颜色类
            // Path: 路径类，用于创建和操作3D路径
            // Tube: 管道几何体，沿路径生成管状几何体
            import { Renderer, Camera, Transform, Program, Mesh, Sphere, Orbit, Vec3, Color, Path, Tube } from '../src/index.js';

            // ========== 球体顶点着色器 ==========
            // 用于处理球体几何体的顶点变换和法线计算
            const vertexColor = /* glsl */ `
                attribute vec3 position;  // 顶点位置属性
                attribute vec3 normal;    // 顶点法线属性

                uniform mat4 modelViewMatrix;   // 模型视图矩阵
                uniform mat4 projectionMatrix;  // 投影矩阵
                uniform mat3 normalMatrix;      // 法线矩阵（用于变换法线）

                varying vec3 vNormal;  // 传递给片段着色器的法线

                void main() {
                    // 将法线从模型空间变换到视图空间并归一化
                    vNormal = normalize(normalMatrix * normal);
                    // 计算最终的顶点位置（模型空间 -> 视图空间 -> 裁剪空间）
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // ========== 球体片段着色器 ==========
            // 为球体提供带光照的颜色渲染
            const fragmentColor = /* glsl */ `
                precision highp float;
                uniform vec3 uColor;      // 球体基础颜色
                varying vec3 vNormal;     // 从顶点着色器接收的法线

                void main() {
                    vec3 normal = normalize(vNormal);
                    // 计算简单的方向光照：光源方向为(-0.3, 0.8, 0.6)
                    float lighting = dot(normal, normalize(vec3(-0.3, 0.8, 0.6)));
                    // 基础颜色加上光照效果
                    gl_FragColor.rgb = uColor + lighting * 0.1;
                    gl_FragColor.a = 1.0;
                }
            `;

            // ========== 管道顶点着色器 ==========
            // 用于处理管道几何体的顶点变换，传递UV坐标用于纹理映射
            const vertexUv = /* glsl */ `
                attribute vec3 position;  // 顶点位置属性
                attribute vec2 uv;        // UV纹理坐标属性
                uniform mat4 modelViewMatrix;   // 模型视图矩阵
                uniform mat4 projectionMatrix;  // 投影矩阵
                varying vec2 vUv;         // 传递给片段着色器的UV坐标

                void main() {
                    vUv = uv;  // 将UV坐标传递给片段着色器
                    // 计算最终的顶点位置
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `;

            // ========== 管道片段着色器 ==========
            // 为管道创建程序化的条纹图案效果
            const fragmentUv = /* glsl */ `
                precision highp float;
                varying vec2 vUv;  // 从顶点着色器接收的UV坐标

                void main() {
                    // 创建条纹效果：当cos(vUv.x * 512.0) < 0时丢弃片段，形成条纹
                    if (cos(vUv.x * 512.0) < 0.0) discard;
                    // 使用UV坐标生成彩色图案：
                    // cos(vUv * 6.283185307179586) 创建周期性变化
                    // * 0.5 + 0.5 将范围从[-1,1]映射到[0,1]
                    // 6.283185307179586 = 2π，创建完整的正弦周期
                    gl_FragColor.rgb = vec3(cos(vUv * 6.283185307179586) * 0.5 + 0.5, 1.0);
                    gl_FragColor.a = 1.0;
                }
            `;

            {
                // ========== 初始化WebGL渲染器 ==========
                // dpr: 2 表示设备像素比为2，提供高分辨率渲染
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl; // 获取WebGL上下文
                document.body.appendChild(gl.canvas); // 将canvas添加到页面
                gl.clearColor(1, 1, 1, 1); // 设置背景色为白色

                // ========== 初始化相机 ==========
                // fov: 35度的视野角度，创建透视投影相机
                const camera = new Camera(gl, { fov: 35 });
                camera.position.set(0, 0, 5); // 设置相机位置

                // ========== 创建轨道控制器 ==========
                // 提供鼠标交互功能：拖拽旋转、滚轮缩放、右键平移
                const controls = new Orbit(camera, {
                    target: new Vec3(0, 0, 0), // 相机围绕的目标点（原点）
                });

                // ========== 窗口大小调整处理 ==========
                function resize() {
                    // 设置渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机的宽高比以匹配新的窗口尺寸
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                // ========== 创建场景根节点 ==========
                const scene = new Transform();

                // ========== 路径坐标数据 ==========
                // 这是一个预定义的3D路径坐标数组，用于创建复杂的贝塞尔曲线路径
                // 前3个数字是moveTo命令的起始点坐标，其余每9个数字为一组bezierCurveTo命令
                // 每组9个数字的格式：[控制点1_x, 控制点1_y, 控制点1_z, 控制点2_x, 控制点2_y, 控制点2_z, 终点_x, 终点_y, 终点_z]
                const pathCoords = [
                    -1.0, 0.0, 0.0, -0.9368709325790405, 0.1762027144432068, 0.04529910162091255, -0.7061498761177063, 0.089231938123703, 0.19381383061408997, -0.6332840919494629, 0.2674865424633026,
                    0.1930660903453827, -0.5615311861038208, 0.4430185854434967, 0.1923297792673111, -0.7734173536300659, 0.5522762537002563, 0.08718681335449219, -0.7070468664169312,
                    0.7070468664169312, 0.0, -0.6498651504516602, 0.8403899073600769, -0.07511603832244873, -0.5399253368377686, 0.8584117889404297, -0.16668838262557983, -0.38917776942253113,
                    0.921392560005188, -0.1677459478378296, -0.23391252756118774, 0.9862607717514038, -0.16883520781993866, -0.14611071348190308, 1.0727460384368896, -0.04093937203288078, 0.0, 1.0,
                    0.0, 0.1674281358718872, 0.9166403412818909, 0.046912387013435364, 0.07777689397335052, 0.6478093862533569, -0.06732462346553802, 0.2400655597448349, 0.5683639645576477, 0.0,
                    0.4298238456249237, 0.4754713177680969, 0.07872024923563004, 0.49316900968551636, 0.7766210436820984, 0.32597726583480835, 0.7070468664169312, 0.7070468664169312,
                    0.3101717531681061, 0.8896822333335876, 0.647635817527771, 0.29667505621910095, 0.8556811809539795, 0.5579423308372498, 0.06533028930425644, 0.921392560005188, 0.38917776942253113,
                    0.0, 0.9742976427078247, 0.2533031702041626, -0.05259828269481659, 1.0508142709732056, 0.14183028042316437, -0.036462459713220596, 1.0, 0.0, 0.0, 0.9368709325790405,
                    -0.1762027144432068, 0.04529910162091255, 0.7061498761177063, -0.089231938123703, 0.19381383061408997, 0.6332840919494629, -0.2674865424633026, 0.1930660903453827,
                    0.5615311861038208, -0.4430185854434967, 0.1923297792673111, 0.7734173536300659, -0.5522762537002563, 0.08718681335449219, 0.7070468664169312, -0.7070468664169312, 0.0,
                    0.6498651504516602, -0.8403899073600769, -0.07511603832244873, 0.5399253368377686, -0.8584117889404297, -0.16668838262557983, 0.38917776942253113, -0.921392560005188,
                    -0.1677459478378296, 0.23391252756118774, -0.9862607717514038, -0.16883520781993866, 0.14611071348190308, -1.0727460384368896, -0.04093937203288078, 0.0, -1.0, 0.0,
                    -0.1674281358718872, -0.9166403412818909, 0.046912387013435364, -0.07777689397335052, -0.6478093862533569, -0.06732462346553802, -0.2400655597448349, -0.5683639645576477, 0.0,
                    -0.4298238456249237, -0.4754713177680969, 0.07872024923563004, -0.49316900968551636, -0.7766210436820984, 0.32597726583480835, -0.7070468664169312, -0.7070468664169312,
                    0.3101717531681061, -0.8896822333335876, -0.647635817527771, 0.29667505621910095, -0.8556811809539795, -0.5579423308372498, 0.06533028930425644, -0.921392560005188,
                    -0.38917776942253113, 0.0, -0.9742976427078247, -0.2533031702041626, -0.05259828269481659, -1.0508142709732056, -0.14183028042316437, -0.036462459713220596, -1.0, 0.0, 0.0,
                ];

                // ========== 构建3D贝塞尔曲线路径 ==========
                const path = new Path();
                // 设置路径倾斜函数：沿路径完整长度旋转8圈，创建螺旋扭转效果
                path.tiltFunction = (angle, t, path) => 8 * 360 * t;

                // 设置路径起始点：从pathCoords数组的前3个坐标开始
                path.moveTo(new Vec3(pathCoords[0], pathCoords[1], pathCoords[2]));
                // 遍历剩余坐标，每9个数字构成一个三次贝塞尔曲线段
                for (let i = 3; i < pathCoords.length; i += 9) {
                    // 第一个控制点
                    const cp1 = new Vec3(pathCoords[i + 0], pathCoords[i + 1], pathCoords[i + 2]);
                    // 第二个控制点
                    const cp2 = new Vec3(pathCoords[i + 3], pathCoords[i + 4], pathCoords[i + 5]);
                    // 曲线终点
                    const p = new Vec3(pathCoords[i + 6], pathCoords[i + 7], pathCoords[i + 8]);
                    // 添加三次贝塞尔曲线段到路径
                    path.bezierCurveTo(cp1, cp2, p);
                }

                // ========== 路径预处理 ==========
                // 将路径细分为256个点，数值越大路径越平滑但性能开销越大
                const pathSubdivisions = 256;
                // 生成路径点数组
                path.getPoints(pathSubdivisions);
                // 计算Frenet标架（切线、法线、副法线），用于管道几何体生成
                path.computeFrenetFrames(pathSubdivisions, true);

                // ========== 创建管道几何体 ==========
                // 沿路径生成管状几何体，参数说明：
                // path: 路径对象，定义管道的中心线
                // radius: 0.1，管道半径
                // tubularSegments: 256，沿路径方向的分段数（影响弯曲平滑度）
                // radialSegments: 16，管道圆周方向的分段数（影响圆形平滑度）
                // closed: true，管道是否闭合（首尾相连）
                const tubeGeom = new Tube(gl, { path, radius: 0.1, tubularSegments: 256, radialSegments: 16, closed: true });

                // ========== 创建管道着色器程序 ==========
                const tubeProg = new Program(gl, {
                    vertex: vertexUv, // 使用UV顶点着色器
                    fragment: fragmentUv, // 使用UV片段着色器（条纹效果）
                    cullFace: false, // 禁用面剔除，使管道内外都可见
                });

                // ========== 创建管道网格对象并添加到场景 ==========
                const tubeMesh = new Mesh(gl, { geometry: tubeGeom, program: tubeProg });
                tubeMesh.setParent(scene);

                // ========== 创建球体几何体和着色器程序 ==========
                const sphereGeom = new Sphere(gl); // 球体几何体
                const sphereProg = new Program(gl, {
                    vertex: vertexColor, // 使用颜色顶点着色器
                    fragment: fragmentColor, // 使用颜色片段着色器
                    uniforms: {
                        uColor: { value: new Color('#4caf50') }, // 设置球体颜色为绿色
                    },
                });

                // ========== 创建球体网格对象 ==========
                // 这个球体将沿路径移动，作为动画标记
                const sphereMesh = new Mesh(gl, { geometry: sphereGeom, program: sphereProg });
                sphereMesh.scale.set(0.1); // 设置球体大小为原来的0.1倍
                sphereMesh.setParent(scene);

                // ========== 启动动画循环 ==========
                requestAnimationFrame(update);
                function update(t) {
                    requestAnimationFrame(update); // 请求下一帧动画

                    // ========== 计算球体沿路径的动画进度 ==========
                    // 将时间转换为[0,1]范围的进度值，0.0001控制动画速度
                    const progress = (t * 0.0001) % 1;
                    // 根据进度获取路径上的位置，并直接设置为球体位置
                    path.getPointAt(progress, sphereMesh.position);

                    // ========== 更新控制器和渲染场景 ==========
                    controls.update(); // 更新轨道控制器（处理鼠标交互）
                    renderer.render({ scene, camera }); // 渲染场景
                }
            }
        </script>
    </body>
</html>
