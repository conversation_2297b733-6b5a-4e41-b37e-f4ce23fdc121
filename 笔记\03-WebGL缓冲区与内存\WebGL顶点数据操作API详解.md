# WebGL 顶点数据操作 API 详解

## 目录

1. [核心概念](#核心概念)
2. [顶点数据流程](#顶点数据流程)
3. [关键 API 详解](#关键api详解)
    - [gl.enableVertexAttribArray()](#1-glenablevertexattribarray)
    - [gl.vertexAttribPointer()](#2-glvertexattribpointer)
    - [gl.vertexAttribDivisor()](#3-glvertexattribdivisor) ⭐ 实例化渲染
4. [实际操作示例](#实际操作示例)
    - [基础三角形](#示例-1-基础三角形)
    - [多属性顶点](#示例-2-多属性顶点)
    - [交错数据布局](#示例-3-交错数据布局)
    - [实例化渲染](#示例-4-实例化渲染) ⭐ 新增
5. [常见问题和陷阱](#常见问题和陷阱)
6. [最佳实践](#最佳实践)
    - [使用 VAO 封装顶点状态](#1-使用-vao-封装顶点状态)
    - [统一的属性配置函数](#2-统一的属性配置函数)
    - [错误检查和调试](#3-错误检查和调试)
    - [内存管理](#4-内存管理)
    - [性能优化建议](#5-性能优化建议)
    - [实例化渲染最佳实践](#6-实例化渲染最佳实践) ⭐ 新增

## 核心概念

### 顶点数据处理管线

```
CPU数据 → VBO(GPU内存) → 顶点属性配置 → 顶点着色器 → 图元装配
```

### 关键组件关系

-   **VBO (Vertex Buffer Object)**: 存储在 GPU 内存中的顶点数据
-   **顶点属性 (Vertex Attribute)**: 描述如何解释 VBO 中的数据
-   **着色器属性位置**: 着色器中 attribute 变量的索引位置

## 顶点数据流程

### 完整的数据流程

```javascript
// 1. 创建并填充VBO
const buffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
gl.bufferData(gl.ARRAY_BUFFER, vertexData, gl.STATIC_DRAW);

// 2. 获取着色器中attribute的位置
const attributeLocation = gl.getAttribLocation(program, 'a_position');

// 3. 启用顶点属性数组
gl.enableVertexAttribArray(attributeLocation);

// 4. 配置顶点属性指针
gl.vertexAttribPointer(attributeLocation, 3, gl.FLOAT, false, 0, 0);

// 5. 绘制
gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
```

### 数据流向图解

```
JavaScript数组 → Float32Array → VBO → 顶点属性配置 → 着色器attribute
     ↓              ↓           ↓           ↓              ↓
  [1,2,3,4,5,6] → GPU内存 → 解释规则 → a_position → vec3 position
```

## 关键 API 详解

### 1. gl.enableVertexAttribArray()

**作用**: 启用指定索引的顶点属性数组

```javascript
gl.enableVertexAttribArray(index);
```

**参数**:

-   `index`: 顶点属性的索引位置 (0-15)

**详细说明**:

-   WebGL 默认情况下所有顶点属性都是**禁用**的
-   必须显式启用才能在着色器中接收数据
-   每个顶点属性索引都有独立的启用/禁用状态

**状态机影响**:

```javascript
// WebGL内部状态
vertexAttribArrayEnabled[index] = true;
```

**示例**:

```javascript
const positionLocation = gl.getAttribLocation(program, 'a_position');
gl.enableVertexAttribArray(positionLocation); // 启用位置属性

const colorLocation = gl.getAttribLocation(program, 'a_color');
gl.enableVertexAttribArray(colorLocation); // 启用颜色属性
```

### 2. gl.vertexAttribPointer()

**作用**: 定义顶点属性数组的数据格式和位置

```javascript
gl.vertexAttribPointer(index, size, type, normalized, stride, offset);
```

**参数详解**:

#### index (顶点属性索引)

-   类型: GLuint
-   范围: 0-15
-   说明: 对应着色器中 attribute 变量的位置

#### size (组件数量)

-   类型: GLint
-   取值: 1, 2, 3, 4
-   说明: 每个顶点属性包含的组件数量
-   示例:
    -   `1`: 标量 (如亮度值)
    -   `2`: 2D 坐标 (x, y)
    -   `3`: 3D 坐标 (x, y, z) 或 RGB 颜色
    -   `4`: 4D 坐标 (x, y, z, w) 或 RGBA 颜色

#### type (数据类型)

-   常用类型:
    -   `gl.FLOAT`: 32 位浮点数
    -   `gl.BYTE`: 8 位有符号整数 (-128 到 127)
    -   `gl.UNSIGNED_BYTE`: 8 位无符号整数 (0 到 255)
    -   `gl.SHORT`: 16 位有符号整数
    -   `gl.UNSIGNED_SHORT`: 16 位无符号整数

#### normalized (归一化标志)

-   类型: GLboolean
-   作用: 是否将整数值归一化到 [0,1] 或 [-1,1] 范围
-   示例:

```javascript
// 颜色数据存储为0-255的字节值
gl.vertexAttribPointer(colorLocation, 3, gl.UNSIGNED_BYTE, true, 0, 0);
// true: 255 → 1.0, 0 → 0.0

gl.vertexAttribPointer(colorLocation, 3, gl.UNSIGNED_BYTE, false, 0, 0);
// false: 255 → 255.0, 0 → 0.0
```

#### stride (步长)

-   类型: GLsizei
-   单位: 字节
-   说明: 连续顶点属性之间的字节间隔
-   0 表示数据紧密排列

#### offset (偏移量)

-   类型: GLintptr
-   单位: 字节
-   说明: 第一个组件在缓冲区中的字节偏移量

### 3. 数据布局示例

#### 分离式数据布局

```javascript
// 位置数据: [x1,y1,z1, x2,y2,z2, x3,y3,z3, ...]
const positions = new Float32Array([
    0.0,
    0.5,
    0.0, // 顶点1
    -0.5,
    -0.5,
    0.0, // 顶点2
    0.5,
    -0.5,
    0.0, // 顶点3
]);

// 颜色数据: [r1,g1,b1,a1, r2,g2,b2,a2, r3,g3,b3,a3, ...]
const colors = new Float32Array([
    1.0,
    0.0,
    0.0,
    1.0, // 红色
    0.0,
    1.0,
    0.0,
    1.0, // 绿色
    0.0,
    0.0,
    1.0,
    1.0, // 蓝色
]);

// 配置位置属性
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
//                     ↑位置索引    ↑3个分量 ↑浮点型 ↑不归一化 ↑紧密排列 ↑从头开始

// 配置颜色属性
gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);
//                     ↑颜色索引    ↑4个分量(RGBA)
```

### 3. gl.vertexAttribDivisor()

**作用**: 设置顶点属性的实例化除数，用于实例化渲染

```javascript
gl.vertexAttribDivisor(index, divisor);
```

**参数详解**:

#### index (顶点属性索引)

-   类型: GLuint
-   范围: 0-15
-   说明: 要设置除数的顶点属性索引

#### divisor (除数值) - 通俗易懂解释

**🤔 什么是 divisor？**

-   **类型**: GLuint (无符号整数)
-   **取值**: 0 或正整数 (1, 2, 3, 4...)
-   **简单理解**: divisor 就是告诉 GPU "多少个实例共享一个属性值"

**🎯 核心概念 - 用生活例子理解**:
想象你要画 100 个房子，每个房子都有门、窗户、屋顶：

-   **门的位置** (divisor=0): 每个房子的门位置都不同 → 每个顶点都有自己的数据
-   **房子颜色** (divisor=1): 每个房子一种颜色，但房子内所有部分都是这个颜色 → 每个实例共享一个数据
-   **街区风格** (divisor=4): 每 4 个房子用同一种建筑风格 → 每 4 个实例共享一个数据

**📚 三种模式详解 - 从简单到复杂**:

##### 🟢 模式 1: divisor = 0 (顶点模式) - 最基础

```javascript
gl.vertexAttribDivisor(positionLocation, 0);
```

**🔍 通俗解释**:

-   就像每个人都有自己的身份证号码一样，每个顶点都有自己独特的数据
-   **什么时候用**: 顶点位置、法线、纹理坐标等基础几何数据
-   **数据流向**: 顶点 0→ 数据[0], 顶点 1→ 数据[1], 顶点 2→ 数据[2]...
-   **结果**: 所有实例的形状完全相同

**📝 实际例子**:

```javascript
// 三角形的3个顶点位置，所有实例都用这个形状
const positions = [0.0, 0.1, 0.0, -0.1, -0.1, 0.0, 0.1, -0.1, 0.0];
gl.vertexAttribDivisor(positionLocation, 0); // 每个顶点用自己的位置
```

##### 🟡 模式 2: divisor = 1 (实例模式) - 最常用

```javascript
gl.vertexAttribDivisor(instanceColorLocation, 1);
```

**🔍 通俗解释**:

-   就像每个班级都有统一的校服颜色，班级内每个学生都穿同样颜色的衣服
-   **什么时候用**: 每个实例的独特属性（颜色、位置偏移、缩放、旋转等）
-   **数据流向**: 实例 0 的所有顶点 → 数据[0], 实例 1 的所有顶点 → 数据[1]...
-   **结果**: 每个实例有不同的属性值，但实例内部统一

**📝 实际例子**:

```javascript
// 4个实例，每个有不同颜色
const colors = [1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 0]; // 红、绿、蓝、黄
gl.vertexAttribDivisor(colorLocation, 1); // 每个实例用一种颜色
// 结果：实例0全红色，实例1全绿色，实例2全蓝色，实例3全黄色
```

##### 🟠 模式 3: divisor = n (分组模式) - 高级用法

```javascript
gl.vertexAttribDivisor(materialLocation, 3);
```

**🔍 通俗解释**:

-   就像每 3 个宿舍共用一个 WiFi 密码，或者每 4 个班级共用一个食堂
-   **什么时候用**: 材质类型、LOD 级别、光照组等需要分组的数据
-   **数据流向**: 实例 0,1,2→ 数据[0], 实例 3,4,5→ 数据[1], 实例 6,7,8→ 数据[2]...
-   **结果**: 每 n 个实例为一组，组内共享相同属性

**📝 实际例子**:

```javascript
// 12个实例，每4个共享一种材质
const materials = [0, 1, 2]; // 3种材质类型
gl.vertexAttribDivisor(materialLocation, 4); // 每4个实例共享一种材质
// 结果：实例0-3用材质0，实例4-7用材质1，实例8-11用材质2
```

**🎬 完整场景演示 - 绘制 6 个三角形实例**:

假设我们要绘制 6 个三角形，每个三角形有 3 个顶点：

```javascript
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 6);
```

**📊 数据分配对比表**:

| 实例编号 | divisor=0 (位置)                                | divisor=1 (颜色)   | divisor=2 (材质)      |
| -------- | ----------------------------------------------- | ------------------ | --------------------- |
| 实例 0   | 顶点 0→pos[0]<br>顶点 1→pos[1]<br>顶点 2→pos[2] | 所有顶点 →color[0] | 所有顶点 →material[0] |
| 实例 1   | 顶点 0→pos[0]<br>顶点 1→pos[1]<br>顶点 2→pos[2] | 所有顶点 →color[1] | 所有顶点 →material[0] |
| 实例 2   | 顶点 0→pos[0]<br>顶点 1→pos[1]<br>顶点 2→pos[2] | 所有顶点 →color[2] | 所有顶点 →material[1] |
| 实例 3   | 顶点 0→pos[0]<br>顶点 1→pos[1]<br>顶点 2→pos[2] | 所有顶点 →color[3] | 所有顶点 →material[1] |
| 实例 4   | 顶点 0→pos[0]<br>顶点 1→pos[1]<br>顶点 2→pos[2] | 所有顶点 →color[4] | 所有顶点 →material[2] |
| 实例 5   | 顶点 0→pos[0]<br>顶点 1→pos[1]<br>顶点 2→pos[2] | 所有顶点 →color[5] | 所有顶点 →material[2] |

**🧮 属性索引计算公式 - 简单易记**:

```javascript
// 万能公式
属性索引 = Math.floor(当前实例编号 / divisor)

// 🔍 具体计算示例
divisor = 1: 实例0→属性[0], 实例1→属性[1], 实例2→属性[2]...
divisor = 2: 实例0,1→属性[0], 实例2,3→属性[1], 实例4,5→属性[2]...
divisor = 3: 实例0,1,2→属性[0], 实例3,4,5→属性[1], 实例6,7,8→属性[2]...

// 📝 记忆技巧：divisor越大，共享的实例越多
```

**🎯 实际应用场景总结**:

| divisor 值 | 使用场景 | 典型属性                   | 效果             |
| ---------- | -------- | -------------------------- | ---------------- |
| 0          | 基础几何 | 位置、法线、UV 坐标        | 所有实例形状相同 |
| 1          | 实例差异 | 颜色、位置偏移、缩放、旋转 | 每个实例独特     |
| 2-4        | 小组分类 | 材质类型、光照组           | 小批量共享       |
| 更大值     | 大组分类 | LOD 级别、渲染层级         | 大批量共享       |

**实例化渲染示例**:

```javascript
function setupInstancedRendering(gl, program) {
    // 基础几何体（一个三角形）
    const baseVertices = new Float32Array([
        0.0,
        0.1,
        0.0, // 顶点1
        -0.1,
        -0.1,
        0.0, // 顶点2
        0.1,
        -0.1,
        0.0, // 顶点3
    ]);

    // 实例位置数据（每个实例的偏移位置）
    const instancePositions = new Float32Array([
        -0.5,
        0.5,
        0.0, // 实例1位置
        0.5,
        0.5,
        0.0, // 实例2位置
        -0.5,
        -0.5,
        0.0, // 实例3位置
        0.5,
        -0.5,
        0.0, // 实例4位置
    ]);

    // 创建基础几何体缓冲区
    const vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, baseVertices, gl.STATIC_DRAW);

    // 创建实例数据缓冲区
    const instanceBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, instancePositions, gl.STATIC_DRAW);

    // 配置基础顶点属性（每个顶点不同）
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(positionLocation, 0); // 每个顶点使用不同值

    // 配置实例属性（每个实例不同）
    const instancePosLocation = gl.getAttribLocation(program, 'a_instancePosition');
    gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
    gl.enableVertexAttribArray(instancePosLocation);
    gl.vertexAttribPointer(instancePosLocation, 3, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(instancePosLocation, 1); // 每个实例使用不同值

    // 绘制4个实例
    gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 4);
}
```

**对应的顶点着色器**:

```glsl
#version 300 es
in vec3 a_position;        // 基础顶点位置
in vec3 a_instancePosition; // 实例偏移位置

void main() {
    // 将基础位置加上实例偏移
    vec3 worldPosition = a_position + a_instancePosition;
    gl_Position = vec4(worldPosition, 1.0);
}
```

**高级实例化示例**:

```javascript
function setupAdvancedInstancing(gl, program) {
    // 基础几何体
    const baseVertices = new Float32Array([
        /* 三角形顶点 */
    ]);

    // 实例数据：位置(3) + 缩放(1) + 旋转(1) + 颜色(3)
    const instanceData = new Float32Array([
        // 实例1: x, y, z, scale, rotation, r, g, b
        -0.5, 0.5, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.5, 0.5, 0.0, 0.8, 0.5, 0.0, 1.0, 0.0, -0.5, -0.5, 0.0, 1.2, 1.0, 0.0, 0.0, 1.0, 0.5, -0.5, 0.0, 0.6, 1.5, 1.0, 1.0, 0.0,
    ]);

    // 创建缓冲区
    const vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, baseVertices, gl.STATIC_DRAW);

    const instanceBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, instanceData, gl.STATIC_DRAW);

    // 配置基础顶点属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.enableVertexAttribArray(positionLocation);
    gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
    gl.vertexAttribDivisor(positionLocation, 0);

    // 配置实例属性
    gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
    const stride = 8 * 4; // 8个float * 4字节

    // 实例位置
    const instancePosLocation = gl.getAttribLocation(program, 'a_instancePosition');
    gl.enableVertexAttribArray(instancePosLocation);
    gl.vertexAttribPointer(instancePosLocation, 3, gl.FLOAT, false, stride, 0);
    gl.vertexAttribDivisor(instancePosLocation, 1);

    // 实例缩放
    const instanceScaleLocation = gl.getAttribLocation(program, 'a_instanceScale');
    gl.enableVertexAttribArray(instanceScaleLocation);
    gl.vertexAttribPointer(instanceScaleLocation, 1, gl.FLOAT, false, stride, 12);
    gl.vertexAttribDivisor(instanceScaleLocation, 1);

    // 实例旋转
    const instanceRotLocation = gl.getAttribLocation(program, 'a_instanceRotation');
    gl.enableVertexAttribArray(instanceRotLocation);
    gl.vertexAttribPointer(instanceRotLocation, 1, gl.FLOAT, false, stride, 16);
    gl.vertexAttribDivisor(instanceRotLocation, 1);

    // 实例颜色
    const instanceColorLocation = gl.getAttribLocation(program, 'a_instanceColor');
    gl.enableVertexAttribArray(instanceColorLocation);
    gl.vertexAttribPointer(instanceColorLocation, 3, gl.FLOAT, false, stride, 20);
    gl.vertexAttribDivisor(instanceColorLocation, 1);

    // 绘制实例
    gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 4);
}
```

**使用场景**:

-   **粒子系统**: 大量相似的粒子，每个有不同的位置、速度、颜色
-   **草地渲染**: 大量草叶，每个有不同的位置、高度、弯曲度
-   **建筑群**: 相同的建筑模型，不同的位置、旋转、缩放
-   **UI 元素**: 大量相似的按钮或图标

**性能优势**:

```javascript
// ❌ 低效：多次绘制调用
for (let i = 0; i < 1000; i++) {
    setUniforms(positions[i], colors[i]);
    gl.drawArrays(gl.TRIANGLES, 0, 3);
}

// ✅ 高效：单次实例化绘制
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 1000);
```

#### 交错式数据布局

```javascript
// 交错数据: [x1,y1,z1,r1,g1,b1,a1, x2,y2,z2,r2,g2,b2,a2, ...]
const interleavedData = new Float32Array([
    // 顶点1: 位置(3) + 颜色(4)
    0.0, 0.5, 0.0, 1.0, 0.0, 0.0, 1.0,
    // 顶点2: 位置(3) + 颜色(4)
    -0.5, -0.5, 0.0, 0.0, 1.0, 0.0, 1.0,
    // 顶点3: 位置(3) + 颜色(4)
    0.5, -0.5, 0.0, 0.0, 0.0, 1.0, 1.0,
]);

const stride = 7 * 4; // 7个float，每个4字节 = 28字节

// 配置位置属性 (偏移量: 0)
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);
//                                                      ↑28字节步长 ↑从0开始

// 配置颜色属性 (偏移量: 3*4=12字节)
gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, stride, 12);
//                                                      ↑28字节步长 ↑从12字节开始
```

## 实际操作示例

### 示例 1: 基础三角形

```javascript
function createTriangle(gl, program) {
    // 顶点数据
    const vertices = new Float32Array([
        0.0,
        0.5,
        0.0, // 顶点1
        -0.5,
        -0.5,
        0.0, // 顶点2
        0.5,
        -0.5,
        0.0, // 顶点3
    ]);

    // 1. 创建VBO
    const buffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

    // 2. 获取attribute位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');

    // 3. 启用顶点属性
    gl.enableVertexAttribArray(positionLocation);

    // 4. 配置顶点属性指针
    gl.vertexAttribPointer(
        positionLocation, // attribute位置
        3, // 每个顶点3个分量 (x, y, z)
        gl.FLOAT, // 数据类型为float
        false, // 不需要归一化
        0, // 步长为0 (紧密排列)
        0 // 偏移量为0 (从缓冲区开始)
    );

    return {
        buffer,
        vertexCount: 3,
        draw: () => gl.drawArrays(gl.TRIANGLES, 0, 3),
    };
}

// ✅ createTriangle 完整使用示例
function exampleCreateTriangle() {
    // 1. 获取WebGL上下文和着色器程序
    const canvas = document.getElementById('canvas');
    const gl = canvas.getContext('webgl2');

    // 假设已经创建了着色器程序
    const vertexShaderSource = `#version 300 es
        in vec3 a_position;
        void main() {
            gl_Position = vec4(a_position, 1.0);
        }`;

    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        out vec4 fragColor;
        void main() {
            fragColor = vec4(1.0, 0.0, 0.0, 1.0); // 红色
        }`;

    const program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

    // 2. 创建三角形几何体
    const triangle = createTriangle(gl, program);

    // 3. 渲染循环
    function render() {
        // 清空画布
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        // 使用着色器程序
        gl.useProgram(program);

        // 绘制三角形
        triangle.draw();

        requestAnimationFrame(render);
    }

    // 启动渲染
    render();
}

// 调用示例
// exampleCreateTriangle();
```

### 示例 2: 多属性顶点

```javascript
function createColoredTriangle(gl, program) {
    // 分离的顶点数据
    const positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);

    const colors = new Float32Array([
        1.0,
        0.0,
        0.0,
        1.0, // 红色
        0.0,
        1.0,
        0.0,
        1.0, // 绿色
        0.0,
        0.0,
        1.0,
        1.0, // 蓝色
    ]);

    // 创建位置缓冲区
    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

    // 创建颜色缓冲区
    const colorBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);

    // 获取attribute位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');

    return {
        setup: () => {
            // 配置位置属性
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

            // 配置颜色属性
            gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
            gl.enableVertexAttribArray(colorLocation);
            gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);
        },
        draw: () => gl.drawArrays(gl.TRIANGLES, 0, 3),
    };
}

// ✅ createColoredTriangle 完整使用示例
function exampleCreateColoredTriangle() {
    // 1. 获取WebGL上下文
    const canvas = document.getElementById('canvas');
    const gl = canvas.getContext('webgl2');

    // 2. 创建支持颜色的着色器程序
    const vertexShaderSource = `#version 300 es
        in vec3 a_position;
        in vec4 a_color;
        out vec4 v_color;

        void main() {
            gl_Position = vec4(a_position, 1.0);
            v_color = a_color; // 传递颜色到片段着色器
        }`;

    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        in vec4 v_color;
        out vec4 fragColor;

        void main() {
            fragColor = v_color; // 使用顶点颜色
        }`;

    const program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

    // 3. 创建彩色三角形
    const coloredTriangle = createColoredTriangle(gl, program);

    // 4. 渲染函数
    function render() {
        // 清空画布
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        // 使用着色器程序
        gl.useProgram(program);

        // 设置顶点属性（每帧都需要调用）
        coloredTriangle.setup();

        // 绘制彩色三角形
        coloredTriangle.draw();
    }

    // 5. 单次渲染或动画循环
    render(); // 单次渲染

    // 或者动画循环
    // function animate() {
    //     render();
    //     requestAnimationFrame(animate);
    // }
    // animate();
}

// 使用场景示例：多个彩色三角形
function exampleMultipleColoredTriangles() {
    const gl = getWebGLContext();
    const program = createColorProgram();

    // 创建多个不同颜色的三角形
    const triangles = [];

    // 红色三角形
    triangles.push(createColoredTriangle(gl, program));

    // 可以创建更多三角形...

    function renderAll() {
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        triangles.forEach((triangle) => {
            triangle.setup();
            triangle.draw();
        });
    }

    renderAll();
}

// 调用示例
// exampleCreateColoredTriangle();
// exampleMultipleColoredTriangles();
```

### 示例 3: 交错数据布局

```javascript
function createInterleavedTriangle(gl, program) {
    // 交错数据: 位置(3) + 颜色(4) 每个顶点
    const interleavedData = new Float32Array([
        // 顶点1: x, y, z, r, g, b, a
        0.0, 0.5, 0.0, 1.0, 0.0, 0.0, 1.0,
        // 顶点2: x, y, z, r, g, b, a
        -0.5, -0.5, 0.0, 0.0, 1.0, 0.0, 1.0,
        // 顶点3: x, y, z, r, g, b, a
        0.5, -0.5, 0.0, 0.0, 0.0, 1.0, 1.0,
    ]);

    // 创建缓冲区
    const buffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, interleavedData, gl.STATIC_DRAW);

    // 获取attribute位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');

    // 计算步长和偏移量
    const stride = 7 * 4; // 7个float * 4字节 = 28字节
    const positionOffset = 0; // 位置从0开始
    const colorOffset = 3 * 4; // 颜色从第12字节开始

    return {
        setup: () => {
            gl.bindBuffer(gl.ARRAY_BUFFER, buffer);

            // 配置位置属性
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, positionOffset);

            // 配置颜色属性
            gl.enableVertexAttribArray(colorLocation);
            gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, stride, colorOffset);
        },
        draw: () => gl.drawArrays(gl.TRIANGLES, 0, 3),
    };
}

// ✅ createInterleavedTriangle 完整使用示例
function exampleCreateInterleavedTriangle() {
    // 1. 初始化WebGL
    const canvas = document.getElementById('canvas');
    const gl = canvas.getContext('webgl2');

    // 2. 创建着色器程序（与彩色三角形相同）
    const vertexShaderSource = `#version 300 es
        in vec3 a_position;
        in vec4 a_color;
        out vec4 v_color;

        void main() {
            gl_Position = vec4(a_position, 1.0);
            v_color = a_color;
        }`;

    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        in vec4 v_color;
        out vec4 fragColor;

        void main() {
            fragColor = v_color;
        }`;

    const program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

    // 3. 创建交错数据三角形
    const interleavedTriangle = createInterleavedTriangle(gl, program);

    // 4. 渲染函数
    function render() {
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        gl.useProgram(program);

        // 设置交错数据的顶点属性
        interleavedTriangle.setup();

        // 绘制
        interleavedTriangle.draw();
    }

    render();
}

// 交错数据的优势示例：更复杂的顶点结构
function exampleComplexInterleavedData() {
    const gl = getWebGLContext();
    const program = createComplexProgram();

    // 复杂的交错数据：位置(3) + 法线(3) + 纹理坐标(2) + 颜色(4)
    const complexInterleavedData = new Float32Array([
        // 顶点1: pos(3) + normal(3) + texCoord(2) + color(4) = 12个分量
        0.0, 0.5, 0.0, 0.0, 0.0, 1.0, 0.5, 1.0, 1.0, 0.0, 0.0, 1.0, -0.5, -0.5, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.5, -0.5, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0,
    ]);

    const buffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, complexInterleavedData, gl.STATIC_DRAW);

    // 计算步长和偏移量
    const stride = 12 * 4; // 12个float * 4字节 = 48字节

    // 配置多个属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const normalLocation = gl.getAttribLocation(program, 'a_normal');
    const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');
    const colorLocation = gl.getAttribLocation(program, 'a_color');

    function setupComplexAttributes() {
        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);

        // 位置属性 (偏移: 0)
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

        // 法线属性 (偏移: 3*4 = 12字节)
        gl.enableVertexAttribArray(normalLocation);
        gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 12);

        // 纹理坐标属性 (偏移: 6*4 = 24字节)
        gl.enableVertexAttribArray(texCoordLocation);
        gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, stride, 24);

        // 颜色属性 (偏移: 8*4 = 32字节)
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, stride, 32);
    }

    function render() {
        gl.useProgram(program);
        setupComplexAttributes();
        gl.drawArrays(gl.TRIANGLES, 0, 3);
    }

    render();
}

// 调用示例
// exampleCreateInterleavedTriangle();
// exampleComplexInterleavedData();
```

### 示例 4: 实例化渲染

```javascript
function createInstancedTriangles(gl, program, instanceCount = 100) {
    // 基础三角形几何体
    const baseVertices = new Float32Array([
        0.0,
        0.05,
        0.0, // 顶点1
        -0.05,
        -0.05,
        0.0, // 顶点2
        0.05,
        -0.05,
        0.0, // 顶点3
    ]);

    // 生成实例数据：位置 + 颜色 + 缩放
    const instanceData = new Float32Array(instanceCount * 7); // 每个实例7个分量
    for (let i = 0; i < instanceCount; i++) {
        const offset = i * 7;

        // 随机位置 (-1 到 1)
        instanceData[offset + 0] = (Math.random() - 0.5) * 2;
        instanceData[offset + 1] = (Math.random() - 0.5) * 2;
        instanceData[offset + 2] = 0.0;

        // 随机颜色
        instanceData[offset + 3] = Math.random();
        instanceData[offset + 4] = Math.random();
        instanceData[offset + 5] = Math.random();

        // 随机缩放 (0.5 到 2.0)
        instanceData[offset + 6] = 0.5 + Math.random() * 1.5;
    }

    // 创建基础几何体缓冲区
    const vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, baseVertices, gl.STATIC_DRAW);

    // 创建实例数据缓冲区
    const instanceBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, instanceData, gl.STATIC_DRAW);

    // 获取属性位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const instancePosLocation = gl.getAttribLocation(program, 'a_instancePosition');
    const instanceColorLocation = gl.getAttribLocation(program, 'a_instanceColor');
    const instanceScaleLocation = gl.getAttribLocation(program, 'a_instanceScale');

    return {
        setup: () => {
            // 配置基础顶点属性
            gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer);
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
            gl.vertexAttribDivisor(positionLocation, 0); // 每个顶点不同

            // 配置实例属性
            gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
            const stride = 7 * 4; // 7个float * 4字节

            // 实例位置
            gl.enableVertexAttribArray(instancePosLocation);
            gl.vertexAttribPointer(instancePosLocation, 3, gl.FLOAT, false, stride, 0);
            gl.vertexAttribDivisor(instancePosLocation, 1); // 每个实例不同

            // 实例颜色
            gl.enableVertexAttribArray(instanceColorLocation);
            gl.vertexAttribPointer(instanceColorLocation, 3, gl.FLOAT, false, stride, 12);
            gl.vertexAttribDivisor(instanceColorLocation, 1); // 每个实例不同

            // 实例缩放
            gl.enableVertexAttribArray(instanceScaleLocation);
            gl.vertexAttribPointer(instanceScaleLocation, 1, gl.FLOAT, false, stride, 24);
            gl.vertexAttribDivisor(instanceScaleLocation, 1); // 每个实例不同
        },
        draw: () => gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, instanceCount),
        instanceCount,
    };
}

// ✅ createInstancedTriangles 完整使用示例
function exampleCreateInstancedTriangles() {
    // 1. 获取WebGL上下文
    const canvas = document.getElementById('canvas');
    const gl = canvas.getContext('webgl2');

    // 2. 创建支持实例化的着色器程序
    const vertexShaderSource = `#version 300 es
        in vec3 a_position;
        in vec3 a_instancePosition;
        in vec3 a_instanceColor;
        in float a_instanceScale;

        out vec3 v_color;

        void main() {
            // 应用缩放和位置偏移
            vec3 scaledPosition = a_position * a_instanceScale;
            vec3 worldPosition = scaledPosition + a_instancePosition;

            gl_Position = vec4(worldPosition, 1.0);
            v_color = a_instanceColor;
        }`;

    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        in vec3 v_color;
        out vec4 fragColor;

        void main() {
            fragColor = vec4(v_color, 1.0);
        }`;

    const program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

    // 3. 创建实例化三角形
    const instancedTriangles = createInstancedTriangles(gl, program, 50);

    // 4. 渲染函数
    function render() {
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        gl.useProgram(program);

        // 设置实例化属性
        instancedTriangles.setup();

        // 绘制所有实例
        instancedTriangles.draw();
    }

    render();

    console.log(`渲染了 ${instancedTriangles.instanceCount} 个三角形实例`);
}

// 动态实例化示例：粒子系统
function exampleDynamicInstancedParticles() {
    const gl = getWebGLContext();
    const program = createInstancedProgram();

    // 粒子系统参数
    const particleCount = 200;
    const particles = [];

    // 初始化粒子
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            position: [Math.random() * 2 - 1, Math.random() * 2 - 1, 0],
            velocity: [(Math.random() - 0.5) * 0.02, (Math.random() - 0.5) * 0.02, 0],
            color: [Math.random(), Math.random(), Math.random()],
            scale: 0.5 + Math.random() * 0.5,
            life: 1.0,
        });
    }

    // 创建动态实例缓冲区
    const instanceData = new Float32Array(particleCount * 7);
    const instanceBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, instanceData, gl.DYNAMIC_DRAW);

    function updateParticles(deltaTime) {
        let activeParticles = 0;

        for (let i = 0; i < particles.length; i++) {
            const particle = particles[i];

            // 更新位置
            particle.position[0] += particle.velocity[0] * deltaTime;
            particle.position[1] += particle.velocity[1] * deltaTime;

            // 更新生命值
            particle.life -= deltaTime * 0.001;

            // 重置死亡的粒子
            if (particle.life <= 0) {
                particle.position[0] = Math.random() * 2 - 1;
                particle.position[1] = Math.random() * 2 - 1;
                particle.velocity[0] = (Math.random() - 0.5) * 0.02;
                particle.velocity[1] = (Math.random() - 0.5) * 0.02;
                particle.life = 1.0;
            }

            // 更新实例数据
            const offset = activeParticles * 7;
            instanceData[offset + 0] = particle.position[0];
            instanceData[offset + 1] = particle.position[1];
            instanceData[offset + 2] = particle.position[2];
            instanceData[offset + 3] = particle.color[0];
            instanceData[offset + 4] = particle.color[1];
            instanceData[offset + 5] = particle.color[2];
            instanceData[offset + 6] = particle.scale * particle.life;

            activeParticles++;
        }

        // 更新GPU缓冲区
        gl.bindBuffer(gl.ARRAY_BUFFER, instanceBuffer);
        gl.bufferSubData(gl.ARRAY_BUFFER, 0, instanceData);

        return activeParticles;
    }

    let lastTime = 0;
    function animate(currentTime) {
        const deltaTime = currentTime - lastTime;
        lastTime = currentTime;

        const activeCount = updateParticles(deltaTime);

        // 渲染
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        setupInstancedAttributes(gl, program, instanceBuffer);
        gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, activeCount);

        requestAnimationFrame(animate);
    }

    animate(0);
}

// 调用示例
// exampleCreateInstancedTriangles();
// exampleDynamicInstancedParticles();
```

## 常见问题和陷阱

### 1. 忘记启用顶点属性

```javascript
// ❌ 错误: 忘记启用属性
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
gl.drawArrays(gl.TRIANGLES, 0, 3); // 不会渲染任何内容

// ✅ 正确: 先启用再配置
gl.enableVertexAttribArray(positionLocation);
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
gl.drawArrays(gl.TRIANGLES, 0, 3);
```

### 2. 缓冲区绑定状态混乱

```javascript
// ❌ 错误: 配置属性时绑定了错误的缓冲区
gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0); // 错误!

// ✅ 正确: 确保绑定正确的缓冲区
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
```

### 3. 步长和偏移量计算错误

```javascript
// 数据布局: position(3 floats) + normal(3 floats) + texCoord(2 floats)
const stride = 8 * 4; // 8个float * 4字节

// ❌ 错误的偏移量
gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 3); // 应该是字节!

// ✅ 正确的偏移量
gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 3 * 4); // 12字节
gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, stride, 6 * 4); // 24字节
```

### 4. 数据类型不匹配

```javascript
// 着色器中定义: attribute vec3 a_position;
// ❌ 错误: size参数不匹配
gl.vertexAttribPointer(positionLocation, 4, gl.FLOAT, false, 0, 0); // vec3需要3个分量

// ✅ 正确: 匹配着色器定义
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
```

### 5. 归一化标志使用错误

```javascript
// 颜色数据存储为0-255的字节值
const colors = new Uint8Array([255, 0, 0, 255, 0, 255, 0, 255, 0, 0, 255, 255]);

// ❌ 错误: 不归一化会导致颜色值过大
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, false, 0, 0);
// 着色器接收到: (255.0, 0.0, 0.0, 255.0) - 超出[0,1]范围

// ✅ 正确: 归一化到[0,1]范围
gl.vertexAttribPointer(colorLocation, 4, gl.UNSIGNED_BYTE, true, 0, 0);
// 着色器接收到: (1.0, 0.0, 0.0, 1.0)
```

### 6. vertexAttribDivisor 使用错误

```javascript
// ❌ 错误: 忘记设置实例属性的除数
gl.enableVertexAttribArray(instancePosLocation);
gl.vertexAttribPointer(instancePosLocation, 3, gl.FLOAT, false, 0, 0);
// 缺少: gl.vertexAttribDivisor(instancePosLocation, 1);
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 100);
// 结果: 所有实例使用相同的属性值

// ✅ 正确: 设置实例属性除数
gl.enableVertexAttribArray(instancePosLocation);
gl.vertexAttribPointer(instancePosLocation, 3, gl.FLOAT, false, 0, 0);
gl.vertexAttribDivisor(instancePosLocation, 1); // 每个实例不同
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 100);
```

### 7. 实例化渲染中的数据对齐问题

```javascript
// ❌ 错误: 实例数据不足
const instanceData = new Float32Array(50 * 3); // 只有50个实例的数据
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 100); // 尝试绘制100个实例

// ✅ 正确: 确保数据量匹配
const instanceCount = 100;
const instanceData = new Float32Array(instanceCount * 3);
gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, instanceCount);
```

### 8. 混合普通属性和实例属性时的除数设置

```javascript
// ❌ 错误: 所有属性都设置了除数
gl.vertexAttribDivisor(positionLocation, 1); // 位置应该是每个顶点不同
gl.vertexAttribDivisor(instanceColorLocation, 1); // 颜色是每个实例不同

// ✅ 正确: 区分顶点属性和实例属性
gl.vertexAttribDivisor(positionLocation, 0); // 顶点属性：每个顶点不同
gl.vertexAttribDivisor(instanceColorLocation, 1); // 实例属性：每个实例不同
```

## 最佳实践

### 1. 使用 VAO 封装顶点状态

```javascript
function createGeometryVAO(gl, program, geometry) {
    const vao = gl.createVertexArray();
    gl.bindVertexArray(vao);

    // 在VAO中配置所有顶点属性
    setupVertexAttributes(gl, program, geometry);

    gl.bindVertexArray(null);
    return vao;
}

function render(gl, vao) {
    gl.bindVertexArray(vao);
    gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
    gl.bindVertexArray(null);
}
```

### 2. 统一的属性配置函数

```javascript
function setupAttribute(gl, program, attributeName, buffer, size, type = gl.FLOAT, normalized = false, stride = 0, offset = 0) {
    const location = gl.getAttribLocation(program, attributeName);
    if (location === -1) {
        console.warn(`Attribute ${attributeName} not found in shader`);
        return;
    }

    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.enableVertexAttribArray(location);
    gl.vertexAttribPointer(location, size, type, normalized, stride, offset);
}

// ✅ createBuffer 辅助函数
function createBuffer(gl, data, usage = gl.STATIC_DRAW) {
    const buffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, data, usage);
    return buffer;
}

// ✅ setupAttribute 基础使用示例
function exampleBasicSetupAttribute() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 示例数据
    const positionData = new Float32Array([
        0.0,
        0.5,
        0.0, // 顶点1
        -0.5,
        -0.5,
        0.0, // 顶点2
        0.5,
        -0.5,
        0.0, // 顶点3
    ]);

    const colorData = new Float32Array([
        1.0,
        0.0,
        0.0,
        1.0, // 红色
        0.0,
        1.0,
        0.0,
        1.0, // 绿色
        0.0,
        0.0,
        1.0,
        1.0, // 蓝色
    ]);

    const texCoordData = new Float32Array([
        0.5,
        1.0, // 顶点1纹理坐标
        0.0,
        0.0, // 顶点2纹理坐标
        1.0,
        0.0, // 顶点3纹理坐标
    ]);

    // 创建各种缓冲区
    const positionBuffer = createBuffer(gl, positionData);
    const colorBuffer = createBuffer(gl, colorData);
    const texCoordBuffer = createBuffer(gl, texCoordData);

    // 使用setupAttribute简化属性配置
    setupAttribute(gl, program, 'a_position', positionBuffer, 3);
    setupAttribute(gl, program, 'a_color', colorBuffer, 4);
    setupAttribute(gl, program, 'a_texCoord', texCoordBuffer, 2);

    // 现在可以直接绘制
    const vertexCount = 3;
    gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
}

// ✅ setupAttribute 高级使用示例：不同数据类型
function exampleAdvancedSetupAttribute() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 位置数据 (Float32Array)
    const positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);
    const positionBuffer = createBuffer(gl, positions);

    // 颜色数据 (Uint8Array, 需要归一化)
    const colors = new Uint8Array([255, 0, 0, 255, 0, 255, 0, 255, 0, 0, 255, 255]);
    const colorBuffer = createBuffer(gl, colors);

    // 法线数据 (Int16Array, 需要归一化)
    const normals = new Int16Array([0, 0, 32767, 0, 0, 32767, 0, 0, 32767]);
    const normalBuffer = createBuffer(gl, normals);

    // 配置不同类型的属性
    setupAttribute(gl, program, 'a_position', positionBuffer, 3, gl.FLOAT, false);
    setupAttribute(gl, program, 'a_color', colorBuffer, 4, gl.UNSIGNED_BYTE, true); // 归一化
    setupAttribute(gl, program, 'a_normal', normalBuffer, 3, gl.SHORT, true); // 归一化

    gl.drawArrays(gl.TRIANGLES, 0, 3);
}

// ✅ setupAttribute 交错数据使用示例
function exampleInterleavedSetupAttribute() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 交错数据：位置(3) + 颜色(4) + 纹理坐标(2)
    const interleavedData = new Float32Array([
        // 顶点1: x, y, z, r, g, b, a, u, v
        0.0, 0.5, 0.0, 1.0, 0.0, 0.0, 1.0, 0.5, 1.0, -0.5, -0.5, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.5, -0.5, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0,
    ]);

    const buffer = createBuffer(gl, interleavedData);
    const stride = 9 * 4; // 9个float * 4字节 = 36字节

    // 使用setupAttribute配置交错数据
    setupAttribute(gl, program, 'a_position', buffer, 3, gl.FLOAT, false, stride, 0);
    setupAttribute(gl, program, 'a_color', buffer, 4, gl.FLOAT, false, stride, 12);
    setupAttribute(gl, program, 'a_texCoord', buffer, 2, gl.FLOAT, false, stride, 28);

    gl.drawArrays(gl.TRIANGLES, 0, 3);
}

// ✅ setupAttribute 批量配置示例
function exampleBatchSetupAttribute() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 属性配置数组
    const attributeConfigs = [
        { name: 'a_position', buffer: positionBuffer, size: 3 },
        { name: 'a_normal', buffer: normalBuffer, size: 3 },
        { name: 'a_texCoord', buffer: texCoordBuffer, size: 2 },
        { name: 'a_color', buffer: colorBuffer, size: 4, type: gl.UNSIGNED_BYTE, normalized: true },
        { name: 'a_tangent', buffer: tangentBuffer, size: 3 },
    ];

    // 批量配置属性
    attributeConfigs.forEach((config) => {
        setupAttribute(gl, program, config.name, config.buffer, config.size, config.type || gl.FLOAT, config.normalized || false, config.stride || 0, config.offset || 0);
    });

    gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
}

// ✅ setupAttribute 错误处理示例
function exampleSetupAttributeWithErrorHandling() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 增强版setupAttribute，带返回值
    function setupAttributeWithReturn(gl, program, attributeName, buffer, size, type = gl.FLOAT, normalized = false, stride = 0, offset = 0) {
        const location = gl.getAttribLocation(program, attributeName);
        if (location === -1) {
            console.warn(`Attribute ${attributeName} not found in shader`);
            return false;
        }

        gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
        gl.enableVertexAttribArray(location);
        gl.vertexAttribPointer(location, size, type, normalized, stride, offset);
        return true;
    }

    // 使用带错误检查的版本
    const positionOk = setupAttributeWithReturn(gl, program, 'a_position', positionBuffer, 3);
    const colorOk = setupAttributeWithReturn(gl, program, 'a_color', colorBuffer, 4);
    const normalOk = setupAttributeWithReturn(gl, program, 'a_normal', normalBuffer, 3);

    if (positionOk && colorOk) {
        console.log('基础属性配置成功，可以渲染');
        gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
    } else {
        console.error('关键属性配置失败，无法渲染');
    }

    if (!normalOk) {
        console.warn('法线属性不可用，将使用默认光照');
    }
}

// 调用示例
// exampleBasicSetupAttribute();
// exampleAdvancedSetupAttribute();
// exampleInterleavedSetupAttribute();
// exampleBatchSetupAttribute();
// exampleSetupAttributeWithErrorHandling();
```

### 3. 错误检查和调试

```javascript
function checkAttributeSetup(gl, program, attributeName) {
    const location = gl.getAttribLocation(program, attributeName);

    if (location === -1) {
        console.error(`Attribute ${attributeName} not found in shader`);
        return false;
    }

    // 检查属性是否启用
    const enabled = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_ENABLED);
    if (!enabled) {
        console.error(`Attribute ${attributeName} is not enabled`);
        return false;
    }

    // 获取属性配置信息
    const size = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_SIZE);
    const type = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_TYPE);
    const normalized = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_NORMALIZED);
    const stride = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_STRIDE);

    console.log(`Attribute ${attributeName}:`, { location, size, type, normalized, stride });
    return true;
}

// ✅ checkAttributeSetup 基础调试使用示例
function exampleBasicAttributeDebugging() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 设置一些属性
    setupAttribute(gl, program, 'a_position', positionBuffer, 3);
    setupAttribute(gl, program, 'a_color', colorBuffer, 4);

    // 调试检查属性配置
    console.log('=== 属性配置检查 ===');
    checkAttributeSetup(gl, program, 'a_position');
    checkAttributeSetup(gl, program, 'a_color');
    checkAttributeSetup(gl, program, 'a_normal'); // 这个可能不存在

    // 只有在所有关键属性都正确时才渲染
    if (checkAttributeSetup(gl, program, 'a_position') && checkAttributeSetup(gl, program, 'a_color')) {
        gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
    }
}

// ✅ checkAttributeSetup 批量调试示例
function exampleBatchAttributeDebugging() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 配置多个属性
    const attributes = [
        { name: 'a_position', buffer: positionBuffer, size: 3 },
        { name: 'a_normal', buffer: normalBuffer, size: 3 },
        { name: 'a_texCoord', buffer: texCoordBuffer, size: 2 },
        { name: 'a_color', buffer: colorBuffer, size: 4 },
    ];

    // 设置属性
    attributes.forEach((attr) => {
        setupAttribute(gl, program, attr.name, attr.buffer, attr.size);
    });

    // 批量检查所有属性
    console.log('=== 批量属性检查 ===');
    const allValid = attributes.every((attr) => {
        return checkAttributeSetup(gl, program, attr.name);
    });

    if (allValid) {
        console.log('✅ 所有属性配置正确');
        gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
    } else {
        console.error('❌ 部分属性配置有问题');
    }
}

// ✅ checkAttributeSetup 详细调试信息示例
function exampleDetailedAttributeDebugging() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 增强版调试函数
    function detailedCheckAttributeSetup(gl, program, attributeName) {
        console.log(`\n=== 检查属性: ${attributeName} ===`);

        const location = gl.getAttribLocation(program, attributeName);
        if (location === -1) {
            console.error(`❌ 属性 ${attributeName} 在着色器中不存在`);
            return false;
        }
        console.log(`✅ 属性位置: ${location}`);

        const enabled = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_ENABLED);
        console.log(`${enabled ? '✅' : '❌'} 属性启用状态: ${enabled}`);

        if (enabled) {
            const size = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_SIZE);
            const type = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_TYPE);
            const normalized = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_NORMALIZED);
            const stride = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_STRIDE);
            const buffer = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_BUFFER_BINDING);

            console.log(`   - 组件数量: ${size}`);
            console.log(`   - 数据类型: ${getTypeName(type)}`);
            console.log(`   - 归一化: ${normalized}`);
            console.log(`   - 步长: ${stride} 字节`);
            console.log(`   - 绑定缓冲区: ${buffer ? '有效' : '无效'}`);

            // 检查当前值（对于禁用的属性）
            const currentValue = gl.getVertexAttrib(location, gl.CURRENT_VERTEX_ATTRIB);
            console.log(`   - 当前值: [${currentValue.join(', ')}]`);
        }

        return enabled;
    }

    // 辅助函数：获取类型名称
    function getTypeName(type) {
        const typeNames = {
            [gl.BYTE]: 'BYTE',
            [gl.UNSIGNED_BYTE]: 'UNSIGNED_BYTE',
            [gl.SHORT]: 'SHORT',
            [gl.UNSIGNED_SHORT]: 'UNSIGNED_SHORT',
            [gl.FLOAT]: 'FLOAT',
        };
        return typeNames[type] || `Unknown(${type})`;
    }

    // 设置属性
    setupAttribute(gl, program, 'a_position', positionBuffer, 3);
    setupAttribute(gl, program, 'a_color', colorBuffer, 4, gl.UNSIGNED_BYTE, true);

    // 详细检查
    detailedCheckAttributeSetup(gl, program, 'a_position');
    detailedCheckAttributeSetup(gl, program, 'a_color');
    detailedCheckAttributeSetup(gl, program, 'a_nonexistent');
}

// ✅ checkAttributeSetup 渲染前验证示例
function examplePreRenderValidation() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 渲染前验证函数
    function validateBeforeRender(gl, program, requiredAttributes) {
        console.log('=== 渲染前验证 ===');

        let allValid = true;
        const missingAttributes = [];
        const disabledAttributes = [];

        requiredAttributes.forEach((attrName) => {
            const location = gl.getAttribLocation(program, attrName);
            if (location === -1) {
                missingAttributes.push(attrName);
                allValid = false;
                return;
            }

            const enabled = gl.getVertexAttrib(location, gl.VERTEX_ATTRIB_ARRAY_ENABLED);
            if (!enabled) {
                disabledAttributes.push(attrName);
                allValid = false;
            }
        });

        if (missingAttributes.length > 0) {
            console.error('❌ 缺失的属性:', missingAttributes);
        }

        if (disabledAttributes.length > 0) {
            console.error('❌ 未启用的属性:', disabledAttributes);
        }

        if (allValid) {
            console.log('✅ 所有必需属性都已正确配置');
        }

        return allValid;
    }

    // 设置属性
    setupAttribute(gl, program, 'a_position', positionBuffer, 3);
    setupAttribute(gl, program, 'a_color', colorBuffer, 4);

    // 渲染前验证
    const requiredAttributes = ['a_position', 'a_color', 'a_normal'];
    if (validateBeforeRender(gl, program, requiredAttributes)) {
        gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
    } else {
        console.log('跳过渲染，因为属性配置不完整');
    }
}

// 调用示例
// exampleBasicAttributeDebugging();
// exampleBatchAttributeDebugging();
// exampleDetailedAttributeDebugging();
// examplePreRenderValidation();
```

### 4. 内存管理

```javascript
class VertexBuffer {
    constructor(gl, data, usage = gl.STATIC_DRAW) {
        this.gl = gl;
        this.buffer = gl.createBuffer();
        this.bind();
        gl.bufferData(gl.ARRAY_BUFFER, data, usage);
        this.size = data.length;
    }

    bind() {
        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffer);
    }

    update(data, offset = 0) {
        this.bind();
        this.gl.bufferSubData(this.gl.ARRAY_BUFFER, offset, data);
    }

    delete() {
        if (this.buffer) {
            this.gl.deleteBuffer(this.buffer);
            this.buffer = null;
        }
    }
}

// ✅ VertexBuffer 基础使用示例
function exampleBasicVertexBuffer() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 创建顶点数据
    const positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);

    const colors = new Float32Array([1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0]);

    // 使用VertexBuffer类创建缓冲区
    const positionBuffer = new VertexBuffer(gl, positions);
    const colorBuffer = new VertexBuffer(gl, colors);

    // 配置属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');

    function render() {
        gl.useProgram(program);

        // 绑定位置缓冲区并配置属性
        positionBuffer.bind();
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

        // 绑定颜色缓冲区并配置属性
        colorBuffer.bind();
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);

        gl.drawArrays(gl.TRIANGLES, 0, 3);
    }

    render();

    // 清理资源
    positionBuffer.delete();
    colorBuffer.delete();
}

// ✅ VertexBuffer 动态更新示例
function exampleDynamicVertexBuffer() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 初始顶点数据
    let positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);

    // 创建动态缓冲区（使用DYNAMIC_DRAW）
    const positionBuffer = new VertexBuffer(gl, positions, gl.DYNAMIC_DRAW);
    const positionLocation = gl.getAttribLocation(program, 'a_position');

    let time = 0;

    function animate() {
        time += 0.016; // 约60fps

        // 动态修改顶点位置（创建波动效果）
        const newPositions = new Float32Array([0.0, 0.5 + Math.sin(time * 2) * 0.2, 0.0, -0.5 + Math.cos(time) * 0.1, -0.5, 0.0, 0.5 + Math.sin(time * 1.5) * 0.1, -0.5, 0.0]);

        // 更新缓冲区数据
        positionBuffer.update(newPositions);

        // 渲染
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        positionBuffer.bind();
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

        gl.drawArrays(gl.TRIANGLES, 0, 3);

        requestAnimationFrame(animate);
    }

    animate();
}

// ✅ VertexBuffer 部分更新示例
function examplePartialVertexBufferUpdate() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 创建较大的顶点数据（多个三角形）
    const positions = new Float32Array([
        // 三角形1
        0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0,
        // 三角形2
        0.0, -0.5, 0.0, -0.5, -1.0, 0.0, 0.5, -1.0, 0.0,
    ]);

    const positionBuffer = new VertexBuffer(gl, positions, gl.DYNAMIC_DRAW);
    const positionLocation = gl.getAttribLocation(program, 'a_position');

    function updateFirstTriangle() {
        // 只更新第一个三角形的数据
        const newFirstTriangle = new Float32Array([
            0.0,
            0.8,
            0.0, // 修改第一个顶点
            -0.3,
            -0.2,
            0.0, // 修改第二个顶点
            0.3,
            -0.2,
            0.0, // 修改第三个顶点
        ]);

        // 部分更新：从偏移量0开始更新
        positionBuffer.update(newFirstTriangle, 0);
    }

    function updateSecondTriangle() {
        // 只更新第二个三角形的数据
        const newSecondTriangle = new Float32Array([0.0, -0.3, 0.0, -0.3, -0.8, 0.0, 0.3, -0.8, 0.0]);

        // 部分更新：从偏移量9*4=36字节开始更新（3个顶点*3个分量*4字节）
        positionBuffer.update(newSecondTriangle, 9 * 4);
    }

    function render() {
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        positionBuffer.bind();
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

        gl.drawArrays(gl.TRIANGLES, 0, 6); // 绘制6个顶点（2个三角形）
    }

    // 初始渲染
    render();

    // 2秒后更新第一个三角形
    setTimeout(() => {
        updateFirstTriangle();
        render();
    }, 2000);

    // 4秒后更新第二个三角形
    setTimeout(() => {
        updateSecondTriangle();
        render();
    }, 4000);
}

// ✅ VertexBuffer 资源管理示例
function exampleVertexBufferResourceManagement() {
    const gl = getWebGLContext();

    // 缓冲区管理器
    class BufferManager {
        constructor(gl) {
            this.gl = gl;
            this.buffers = new Map();
        }

        createBuffer(name, data, usage = gl.STATIC_DRAW) {
            if (this.buffers.has(name)) {
                console.warn(`Buffer ${name} already exists, deleting old one`);
                this.deleteBuffer(name);
            }

            const buffer = new VertexBuffer(gl, data, usage);
            this.buffers.set(name, buffer);
            return buffer;
        }

        getBuffer(name) {
            return this.buffers.get(name);
        }

        deleteBuffer(name) {
            const buffer = this.buffers.get(name);
            if (buffer) {
                buffer.delete();
                this.buffers.delete(name);
                console.log(`Buffer ${name} deleted`);
            }
        }

        deleteAllBuffers() {
            console.log('Deleting all buffers...');
            this.buffers.forEach((buffer, name) => {
                buffer.delete();
                console.log(`Buffer ${name} deleted`);
            });
            this.buffers.clear();
        }

        getBufferCount() {
            return this.buffers.size;
        }
    }

    // 使用缓冲区管理器
    const bufferManager = new BufferManager(gl);

    // 创建多个缓冲区
    const positions = new Float32Array([0.0, 0.5, 0.0, -0.5, -0.5, 0.0, 0.5, -0.5, 0.0]);
    const colors = new Float32Array([1.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0]);

    bufferManager.createBuffer('positions', positions);
    bufferManager.createBuffer('colors', colors);

    console.log(`Created ${bufferManager.getBufferCount()} buffers`);

    // 使用缓冲区
    const positionBuffer = bufferManager.getBuffer('positions');
    const colorBuffer = bufferManager.getBuffer('colors');

    // 渲染...

    // 清理特定缓冲区
    bufferManager.deleteBuffer('colors');

    // 或清理所有缓冲区
    bufferManager.deleteAllBuffers();
}

// 调用示例
// exampleBasicVertexBuffer();
// exampleDynamicVertexBuffer();
// examplePartialVertexBufferUpdate();
// exampleVertexBufferResourceManagement();
```

### 5. 性能优化建议

#### 减少状态切换

```javascript
// ❌ 低效: 频繁切换缓冲区
for (const object of objects) {
    gl.bindBuffer(gl.ARRAY_BUFFER, object.positionBuffer);
    gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 0, 0);
    gl.bindBuffer(gl.ARRAY_BUFFER, object.colorBuffer);
    gl.vertexAttribPointer(1, 4, gl.FLOAT, false, 0, 0);
    gl.drawArrays(gl.TRIANGLES, 0, object.vertexCount);
}

// ✅ 高效: 使用VAO减少状态切换
for (const object of objects) {
    gl.bindVertexArray(object.vao);
    gl.drawArrays(gl.TRIANGLES, 0, object.vertexCount);
}
```

#### 批量处理

```javascript
// 将多个小对象合并到一个大缓冲区中
function createBatchedGeometry(geometries) {
    let totalVertices = 0;
    let totalIndices = 0;

    // 计算总大小
    for (const geo of geometries) {
        totalVertices += geo.vertices.length;
        totalIndices += geo.indices.length;
    }

    // 创建合并的缓冲区
    const batchedVertices = new Float32Array(totalVertices);
    const batchedIndices = new Uint16Array(totalIndices);

    // 合并数据...

    return { vertices: batchedVertices, indices: batchedIndices };
}

// ✅ createBatchedGeometry 完整使用示例
function exampleCreateBatchedGeometry() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 创建多个小几何体
    const geometries = [
        // 三角形1
        {
            vertices: new Float32Array([
                0.0,
                0.5,
                0.0,
                1.0,
                0.0,
                0.0,
                1.0, // 位置 + 颜色
                -0.5,
                -0.5,
                0.0,
                0.0,
                1.0,
                0.0,
                1.0,
                0.5,
                -0.5,
                0.0,
                0.0,
                0.0,
                1.0,
                1.0,
            ]),
            indices: new Uint16Array([0, 1, 2]),
        },
        // 三角形2（偏移位置）
        {
            vertices: new Float32Array([
                1.0,
                0.5,
                0.0,
                1.0,
                1.0,
                0.0,
                1.0, // 黄色
                0.5,
                -0.5,
                0.0,
                1.0,
                0.0,
                1.0,
                1.0, // 紫色
                1.5,
                -0.5,
                0.0,
                0.0,
                1.0,
                1.0,
                1.0, // 青色
            ]),
            indices: new Uint16Array([0, 1, 2]),
        },
        // 四边形（两个三角形）
        {
            vertices: new Float32Array([
                -1.5,
                0.5,
                0.0,
                1.0,
                0.5,
                0.0,
                1.0, // 橙色
                -0.5,
                0.5,
                0.0,
                0.5,
                1.0,
                0.0,
                1.0,
                -0.5,
                -0.5,
                0.0,
                0.0,
                0.5,
                1.0,
                1.0,
                -1.5,
                -0.5,
                0.0,
                1.0,
                0.0,
                0.5,
                1.0,
            ]),
            indices: new Uint16Array([0, 1, 2, 0, 2, 3]),
        },
    ];

    // 改进的批量几何体创建函数
    function createBatchedGeometryImproved(geometries) {
        let totalVertices = 0;
        let totalIndices = 0;

        // 计算总大小
        geometries.forEach((geo) => {
            totalVertices += geo.vertices.length;
            totalIndices += geo.indices.length;
        });

        // 创建合并的缓冲区
        const batchedVertices = new Float32Array(totalVertices);
        const batchedIndices = new Uint16Array(totalIndices);

        let vertexOffset = 0;
        let indexOffset = 0;
        let vertexIndexOffset = 0; // 用于调整索引值

        const drawCalls = []; // 记录每个几何体的绘制信息

        // 合并数据
        geometries.forEach((geo, i) => {
            // 复制顶点数据
            batchedVertices.set(geo.vertices, vertexOffset);

            // 复制并调整索引数据
            for (let j = 0; j < geo.indices.length; j++) {
                batchedIndices[indexOffset + j] = geo.indices[j] + vertexIndexOffset;
            }

            // 记录绘制信息
            drawCalls.push({
                indexOffset: indexOffset,
                indexCount: geo.indices.length,
                vertexOffset: vertexIndexOffset,
            });

            // 更新偏移量
            vertexOffset += geo.vertices.length;
            indexOffset += geo.indices.length;
            vertexIndexOffset += geo.vertices.length / 7; // 假设每个顶点7个分量（位置3+颜色4）
        });

        return {
            vertices: batchedVertices,
            indices: batchedIndices,
            drawCalls: drawCalls,
        };
    }

    // 创建批量几何体
    const batchedGeometry = createBatchedGeometryImproved(geometries);

    // 创建缓冲区
    const vertexBuffer = new VertexBuffer(gl, batchedGeometry.vertices);
    const indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, batchedGeometry.indices, gl.STATIC_DRAW);

    // 配置属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');

    function setupAttributes() {
        vertexBuffer.bind();

        const stride = 7 * 4; // 7个float * 4字节

        // 位置属性
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

        // 颜色属性
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, stride, 12);
    }

    function render() {
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        setupAttributes();
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);

        // 一次性绘制所有几何体
        gl.drawElements(gl.TRIANGLES, batchedGeometry.indices.length, gl.UNSIGNED_SHORT, 0);
    }

    function renderSelective() {
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        setupAttributes();
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);

        // 选择性绘制特定几何体
        batchedGeometry.drawCalls.forEach((drawCall, index) => {
            if (index === 1) return; // 跳过第二个几何体

            gl.drawElements(
                gl.TRIANGLES,
                drawCall.indexCount,
                gl.UNSIGNED_SHORT,
                drawCall.indexOffset * 2 // 字节偏移量
            );
        });
    }

    render(); // 绘制所有

    // 2秒后选择性绘制
    setTimeout(() => {
        renderSelective();
    }, 2000);
}

// ✅ createBatchedGeometry 动态批处理示例
function exampleDynamicBatchedGeometry() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 动态批处理管理器
    class DynamicBatchManager {
        constructor(gl, maxVertices = 10000, maxIndices = 15000) {
            this.gl = gl;
            this.maxVertices = maxVertices;
            this.maxIndices = maxIndices;

            // 创建大缓冲区
            this.vertexBuffer = new VertexBuffer(gl, new Float32Array(maxVertices * 7), gl.DYNAMIC_DRAW);
            this.indexBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(maxIndices), gl.DYNAMIC_DRAW);

            this.currentVertexCount = 0;
            this.currentIndexCount = 0;
            this.batches = [];
        }

        addGeometry(vertices, indices) {
            if (this.currentVertexCount + vertices.length / 7 > this.maxVertices || this.currentIndexCount + indices.length > this.maxIndices) {
                console.warn('Batch full, need to flush');
                return false;
            }

            // 更新顶点数据
            this.vertexBuffer.bind();
            gl.bufferSubData(gl.ARRAY_BUFFER, this.currentVertexCount * 7 * 4, vertices);

            // 更新索引数据
            const adjustedIndices = new Uint16Array(indices.length);
            for (let i = 0; i < indices.length; i++) {
                adjustedIndices[i] = indices[i] + this.currentVertexCount;
            }

            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.bufferSubData(gl.ELEMENT_ARRAY_BUFFER, this.currentIndexCount * 2, adjustedIndices);

            // 记录批次信息
            this.batches.push({
                indexOffset: this.currentIndexCount,
                indexCount: indices.length,
                vertexOffset: this.currentVertexCount,
            });

            this.currentVertexCount += vertices.length / 7;
            this.currentIndexCount += indices.length;

            return true;
        }

        render(program) {
            if (this.batches.length === 0) return;

            gl.useProgram(program);

            // 设置属性
            this.vertexBuffer.bind();
            const stride = 7 * 4;

            const positionLocation = gl.getAttribLocation(program, 'a_position');
            const colorLocation = gl.getAttribLocation(program, 'a_color');

            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

            gl.enableVertexAttribArray(colorLocation);
            gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, stride, 12);

            // 绘制所有批次
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.drawElements(gl.TRIANGLES, this.currentIndexCount, gl.UNSIGNED_SHORT, 0);
        }

        clear() {
            this.currentVertexCount = 0;
            this.currentIndexCount = 0;
            this.batches = [];
        }
    }

    // 使用动态批处理管理器
    const batchManager = new DynamicBatchManager(gl);

    // 添加几何体
    const triangle1Vertices = new Float32Array([0.0, 0.5, 0.0, 1.0, 0.0, 0.0, 1.0, -0.5, -0.5, 0.0, 0.0, 1.0, 0.0, 1.0, 0.5, -0.5, 0.0, 0.0, 0.0, 1.0, 1.0]);
    const triangle1Indices = new Uint16Array([0, 1, 2]);

    const triangle2Vertices = new Float32Array([1.0, 0.5, 0.0, 1.0, 1.0, 0.0, 1.0, 0.5, -0.5, 0.0, 1.0, 0.0, 1.0, 1.0, 1.5, -0.5, 0.0, 0.0, 1.0, 1.0, 1.0]);
    const triangle2Indices = new Uint16Array([0, 1, 2]);

    batchManager.addGeometry(triangle1Vertices, triangle1Indices);
    batchManager.addGeometry(triangle2Vertices, triangle2Indices);

    function render() {
        gl.clear(gl.COLOR_BUFFER_BIT);
        batchManager.render(program);
    }

    render();
}

// 调用示例
// exampleCreateBatchedGeometry();
// exampleDynamicBatchedGeometry();
```

### 6. 实例化渲染最佳实践

#### 合理组织实例数据

```javascript
// ✅ 推荐：将相关数据组织在一起
class InstancedRenderer {
    constructor(gl, maxInstances = 1000) {
        this.gl = gl;
        this.maxInstances = maxInstances;

        // 预分配大缓冲区，避免频繁重新分配
        this.instanceData = new Float32Array(maxInstances * 8); // 位置3+颜色3+缩放1+旋转1
        this.instanceBuffer = gl.createBuffer();

        gl.bindBuffer(gl.ARRAY_BUFFER, this.instanceBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, this.instanceData, gl.DYNAMIC_DRAW);

        this.currentInstanceCount = 0;
    }

    addInstance(position, color, scale, rotation) {
        if (this.currentInstanceCount >= this.maxInstances) {
            console.warn('Instance buffer full');
            return false;
        }

        const offset = this.currentInstanceCount * 8;

        // 位置
        this.instanceData[offset + 0] = position[0];
        this.instanceData[offset + 1] = position[1];
        this.instanceData[offset + 2] = position[2];

        // 颜色
        this.instanceData[offset + 3] = color[0];
        this.instanceData[offset + 4] = color[1];
        this.instanceData[offset + 5] = color[2];

        // 缩放和旋转
        this.instanceData[offset + 6] = scale;
        this.instanceData[offset + 7] = rotation;

        this.currentInstanceCount++;
        return true;
    }

    updateGPUBuffer() {
        if (this.currentInstanceCount === 0) return;

        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.instanceBuffer);
        // 只更新实际使用的部分
        const dataToUpdate = this.instanceData.subarray(0, this.currentInstanceCount * 8);
        this.gl.bufferSubData(this.gl.ARRAY_BUFFER, 0, dataToUpdate);
    }

    setupAttributes(program) {
        const gl = this.gl;
        const stride = 8 * 4; // 8个float * 4字节

        gl.bindBuffer(gl.ARRAY_BUFFER, this.instanceBuffer);

        // 实例位置
        const instancePosLocation = gl.getAttribLocation(program, 'a_instancePosition');
        if (instancePosLocation !== -1) {
            gl.enableVertexAttribArray(instancePosLocation);
            gl.vertexAttribPointer(instancePosLocation, 3, gl.FLOAT, false, stride, 0);
            gl.vertexAttribDivisor(instancePosLocation, 1);
        }

        // 实例颜色
        const instanceColorLocation = gl.getAttribLocation(program, 'a_instanceColor');
        if (instanceColorLocation !== -1) {
            gl.enableVertexAttribArray(instanceColorLocation);
            gl.vertexAttribPointer(instanceColorLocation, 3, gl.FLOAT, false, stride, 12);
            gl.vertexAttribDivisor(instanceColorLocation, 1);
        }

        // 实例缩放
        const instanceScaleLocation = gl.getAttribLocation(program, 'a_instanceScale');
        if (instanceScaleLocation !== -1) {
            gl.enableVertexAttribArray(instanceScaleLocation);
            gl.vertexAttribPointer(instanceScaleLocation, 1, gl.FLOAT, false, stride, 24);
            gl.vertexAttribDivisor(instanceScaleLocation, 1);
        }

        // 实例旋转
        const instanceRotLocation = gl.getAttribLocation(program, 'a_instanceRotation');
        if (instanceRotLocation !== -1) {
            gl.enableVertexAttribArray(instanceRotLocation);
            gl.vertexAttribPointer(instanceRotLocation, 1, gl.FLOAT, false, stride, 28);
            gl.vertexAttribDivisor(instanceRotLocation, 1);
        }
    }

    render(baseVertexCount) {
        if (this.currentInstanceCount === 0) return;

        this.updateGPUBuffer();
        this.gl.drawArraysInstanced(this.gl.TRIANGLES, 0, baseVertexCount, this.currentInstanceCount);
    }

    clear() {
        this.currentInstanceCount = 0;
    }
}

// ✅ InstancedRenderer 使用示例
function exampleInstancedRenderer() {
    const gl = getWebGLContext();
    const program = createInstancedProgram();

    const renderer = new InstancedRenderer(gl, 500);

    // 添加多个实例
    for (let i = 0; i < 100; i++) {
        const position = [Math.random() * 2 - 1, Math.random() * 2 - 1, 0];
        const color = [Math.random(), Math.random(), Math.random()];
        const scale = 0.5 + Math.random() * 0.5;
        const rotation = Math.random() * Math.PI * 2;

        renderer.addInstance(position, color, scale, rotation);
    }

    function render() {
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        // 设置基础几何体属性
        setupBaseGeometry(gl, program);

        // 设置实例属性并渲染
        renderer.setupAttributes(program);
        renderer.render(3); // 3个顶点的三角形
    }

    render();
}
```

#### 实例化渲染的性能优化技巧

```javascript
// ✅ 按材质分组批量渲染
function optimizedInstancedRendering() {
    const gl = getWebGLContext();

    // 按材质/纹理分组实例
    const instanceGroups = new Map();

    function addInstance(materialId, instanceData) {
        if (!instanceGroups.has(materialId)) {
            instanceGroups.set(materialId, []);
        }
        instanceGroups.get(materialId).push(instanceData);
    }

    function renderAllGroups() {
        instanceGroups.forEach((instances, materialId) => {
            // 设置材质特定的uniform
            setMaterialUniforms(gl, materialId);

            // 创建该组的实例数据
            const instanceData = new Float32Array(instances.length * 8);
            instances.forEach((instance, i) => {
                const offset = i * 8;
                instanceData.set(instance.position, offset);
                instanceData.set(instance.color, offset + 3);
                instanceData[offset + 6] = instance.scale;
                instanceData[offset + 7] = instance.rotation;
            });

            // 更新缓冲区并渲染
            updateInstanceBuffer(gl, instanceData);
            gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, instances.length);
        });
    }

    // 使用示例
    addInstance('wood', { position: [0, 0, 0], color: [0.6, 0.3, 0.1], scale: 1.0, rotation: 0 });
    addInstance('metal', { position: [1, 0, 0], color: [0.8, 0.8, 0.9], scale: 1.2, rotation: 0.5 });
    addInstance('wood', { position: [0, 1, 0], color: [0.5, 0.25, 0.1], scale: 0.8, rotation: 1.0 });

    renderAllGroups();
}
```

## 深入理解：为什么需要这些 API？

### 1. enableVertexAttribArray 的必要性

WebGL 采用**显式启用**的设计哲学：

```javascript
// WebGL内部状态（概念性）
const vertexAttributeState = {
    0: { enabled: false, pointer: null }, // 默认禁用
    1: { enabled: false, pointer: null },
    2: { enabled: false, pointer: null },
    // ... 最多16个属性
};

// 只有显式启用的属性才会被处理
gl.enableVertexAttribArray(0); // vertexAttributeState[0].enabled = true
```

**设计原因**：

-   **性能考虑**: 避免处理不需要的属性数据
-   **错误预防**: 防止意外使用未配置的属性
-   **灵活性**: 可以动态启用/禁用不同的属性组合

### 2. vertexAttribPointer 的复杂参数

这个函数需要告诉 GPU 如何"解读"内存中的字节流：

```javascript
// 内存中的原始字节: [0x3F800000, 0x40000000, 0x40400000, ...]
// 需要解释为: [1.0, 2.0, 3.0, ...] (float格式)

gl.vertexAttribPointer(
    location, // 发送到哪个着色器属性
    3, // 每次读取3个数值 (x, y, z)
    gl.FLOAT, // 按32位浮点数解释
    false, // 不需要归一化
    0, // 数据紧密排列
    0 // 从缓冲区开头开始
);
```

### 3. 状态机设计的影响

WebGL 的状态机设计意味着：

```javascript
// 当前绑定的缓冲区影响vertexAttribPointer的行为
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 0, 0); // 配置属性0使用positionBuffer

gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
gl.vertexAttribPointer(1, 4, gl.FLOAT, false, 0, 0); // 配置属性1使用colorBuffer
```

## 实际应用场景

### 场景 1: 动态网格更新

```javascript
// 实时变形的网格（如水面波动）
function updateWaterMesh(gl, buffer, time) {
    const vertices = generateWaterVertices(time);

    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferSubData(gl.ARRAY_BUFFER, 0, vertices); // 更新顶点数据

    // 顶点属性配置保持不变，只需重新绘制
    gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
}

// ✅ updateWaterMesh 完整使用示例
function exampleUpdateWaterMesh() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 水面网格参数
    const gridSize = 50;
    const gridSpacing = 0.1;
    const vertexCount = gridSize * gridSize;

    // 生成初始水面顶点
    function generateWaterVertices(time) {
        const vertices = new Float32Array(vertexCount * 6); // 位置(3) + 法线(3)
        let index = 0;

        for (let x = 0; x < gridSize; x++) {
            for (let z = 0; z < gridSize; z++) {
                const worldX = (x - gridSize / 2) * gridSpacing;
                const worldZ = (z - gridSize / 2) * gridSpacing;

                // 使用多个正弦波创建水面效果
                const wave1 = Math.sin(worldX * 2 + time * 2) * 0.1;
                const wave2 = Math.sin(worldZ * 1.5 + time * 1.5) * 0.08;
                const wave3 = Math.sin((worldX + worldZ) * 3 + time * 3) * 0.05;
                const y = wave1 + wave2 + wave3;

                // 计算法线（用于光照）
                const epsilon = 0.01;
                const dydx = (Math.sin((worldX + epsilon) * 2 + time * 2) * 0.1 + Math.sin((worldX + epsilon + worldZ) * 3 + time * 3) * 0.05 - (wave1 + wave3)) / epsilon;
                const dydz = (Math.sin((worldZ + epsilon) * 1.5 + time * 1.5) * 0.08 + Math.sin((worldX + (worldZ + epsilon)) * 3 + time * 3) * 0.05 - (wave2 + wave3)) / epsilon;

                const normal = normalize([-dydx, 1, -dydz]);

                // 顶点位置
                vertices[index++] = worldX;
                vertices[index++] = y;
                vertices[index++] = worldZ;

                // 顶点法线
                vertices[index++] = normal[0];
                vertices[index++] = normal[1];
                vertices[index++] = normal[2];
            }
        }

        return vertices;
    }

    // 辅助函数：向量归一化
    function normalize(vec) {
        const length = Math.sqrt(vec[0] * vec[0] + vec[1] * vec[1] + vec[2] * vec[2]);
        return [vec[0] / length, vec[1] / length, vec[2] / length];
    }

    // 生成索引（用于绘制三角形）
    function generateWaterIndices() {
        const indices = [];
        for (let x = 0; x < gridSize - 1; x++) {
            for (let z = 0; z < gridSize - 1; z++) {
                const topLeft = x * gridSize + z;
                const topRight = topLeft + 1;
                const bottomLeft = (x + 1) * gridSize + z;
                const bottomRight = bottomLeft + 1;

                // 第一个三角形
                indices.push(topLeft, bottomLeft, topRight);
                // 第二个三角形
                indices.push(topRight, bottomLeft, bottomRight);
            }
        }
        return new Uint16Array(indices);
    }

    // 创建缓冲区
    const initialVertices = generateWaterVertices(0);
    const waterBuffer = new VertexBuffer(gl, initialVertices, gl.DYNAMIC_DRAW);

    const indices = generateWaterIndices();
    const indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);

    // 配置属性
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const normalLocation = gl.getAttribLocation(program, 'a_normal');

    function setupAttributes() {
        waterBuffer.bind();
        const stride = 6 * 4; // 6个float * 4字节

        // 位置属性
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

        // 法线属性
        gl.enableVertexAttribArray(normalLocation);
        gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 12);
    }

    // 改进的updateWaterMesh函数
    function updateWaterMeshImproved(gl, buffer, time) {
        const vertices = generateWaterVertices(time);

        // 更新缓冲区数据
        buffer.update(vertices);
    }

    let startTime = Date.now();

    function animate() {
        const currentTime = (Date.now() - startTime) / 1000; // 转换为秒

        // 更新水面网格
        updateWaterMeshImproved(gl, waterBuffer, currentTime);

        // 渲染
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        gl.enable(gl.DEPTH_TEST);
        gl.useProgram(program);

        setupAttributes();
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);

        // 绘制水面
        gl.drawElements(gl.TRIANGLES, indices.length, gl.UNSIGNED_SHORT, 0);

        requestAnimationFrame(animate);
    }

    animate();
}

// ✅ updateWaterMesh 优化版本示例
function exampleOptimizedWaterMesh() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 水面网格管理器
    class WaterMeshManager {
        constructor(gl, gridSize = 32, gridSpacing = 0.1) {
            this.gl = gl;
            this.gridSize = gridSize;
            this.gridSpacing = gridSpacing;
            this.vertexCount = gridSize * gridSize;

            // 创建缓冲区
            this.vertexBuffer = new VertexBuffer(gl, new Float32Array(this.vertexCount * 6), gl.DYNAMIC_DRAW);

            // 创建索引缓冲区
            this.indices = this.generateIndices();
            this.indexBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, this.indices, gl.STATIC_DRAW);

            // 波浪参数
            this.waveParams = [
                { amplitude: 0.1, frequency: 2.0, speed: 2.0, direction: [1, 0] },
                { amplitude: 0.08, frequency: 1.5, speed: 1.5, direction: [0, 1] },
                { amplitude: 0.05, frequency: 3.0, speed: 3.0, direction: [1, 1] },
                { amplitude: 0.03, frequency: 4.0, speed: 2.5, direction: [-1, 1] },
            ];
        }

        generateIndices() {
            const indices = [];
            for (let x = 0; x < this.gridSize - 1; x++) {
                for (let z = 0; z < this.gridSize - 1; z++) {
                    const topLeft = x * this.gridSize + z;
                    const topRight = topLeft + 1;
                    const bottomLeft = (x + 1) * this.gridSize + z;
                    const bottomRight = bottomLeft + 1;

                    indices.push(topLeft, bottomLeft, topRight);
                    indices.push(topRight, bottomLeft, bottomRight);
                }
            }
            return new Uint16Array(indices);
        }

        calculateWaveHeight(x, z, time) {
            let height = 0;
            let dydx = 0;
            let dydz = 0;

            for (const wave of this.waveParams) {
                const phase = wave.direction[0] * x + wave.direction[1] * z;
                const waveValue = Math.sin(phase * wave.frequency + time * wave.speed);

                height += wave.amplitude * waveValue;

                // 计算偏导数用于法线计算
                const derivative = wave.amplitude * wave.frequency * Math.cos(phase * wave.frequency + time * wave.speed);
                dydx += derivative * wave.direction[0];
                dydz += derivative * wave.direction[1];
            }

            return { height, dydx, dydz };
        }

        update(time) {
            const vertices = new Float32Array(this.vertexCount * 6);
            let index = 0;

            for (let x = 0; x < this.gridSize; x++) {
                for (let z = 0; z < this.gridSize; z++) {
                    const worldX = (x - this.gridSize / 2) * this.gridSpacing;
                    const worldZ = (z - this.gridSize / 2) * this.gridSpacing;

                    const { height, dydx, dydz } = this.calculateWaveHeight(worldX, worldZ, time);

                    // 计算法线
                    const normal = this.normalize([-dydx, 1, -dydz]);

                    // 顶点位置
                    vertices[index++] = worldX;
                    vertices[index++] = height;
                    vertices[index++] = worldZ;

                    // 顶点法线
                    vertices[index++] = normal[0];
                    vertices[index++] = normal[1];
                    vertices[index++] = normal[2];
                }
            }

            this.vertexBuffer.update(vertices);
        }

        normalize(vec) {
            const length = Math.sqrt(vec[0] * vec[0] + vec[1] * vec[1] + vec[2] * vec[2]);
            return length > 0 ? [vec[0] / length, vec[1] / length, vec[2] / length] : [0, 1, 0];
        }

        render(program) {
            gl.useProgram(program);

            // 设置属性
            this.vertexBuffer.bind();
            const stride = 6 * 4;

            const positionLocation = gl.getAttribLocation(program, 'a_position');
            const normalLocation = gl.getAttribLocation(program, 'a_normal');

            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

            gl.enableVertexAttribArray(normalLocation);
            gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 12);

            // 绘制
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.drawElements(gl.TRIANGLES, this.indices.length, gl.UNSIGNED_SHORT, 0);
        }

        cleanup() {
            this.vertexBuffer.delete();
            gl.deleteBuffer(this.indexBuffer);
        }
    }

    // 使用水面网格管理器
    const waterMesh = new WaterMeshManager(gl, 64, 0.05);
    let startTime = Date.now();

    function animate() {
        const currentTime = (Date.now() - startTime) / 1000;

        // 更新水面
        waterMesh.update(currentTime);

        // 渲染
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        gl.enable(gl.DEPTH_TEST);

        waterMesh.render(program);

        requestAnimationFrame(animate);
    }

    animate();

    // 清理资源
    // waterMesh.cleanup();
}

// 调用示例
// exampleUpdateWaterMesh();
// exampleOptimizedWaterMesh();
```

### 场景 2: 多 LOD 渲染

```javascript
// 根据距离选择不同细节级别的网格
function renderLOD(gl, object, cameraDistance) {
    let vao;
    if (cameraDistance < 10) {
        vao = object.highDetailVAO; // 高细节网格
    } else if (cameraDistance < 50) {
        vao = object.mediumDetailVAO; // 中等细节网格
    } else {
        vao = object.lowDetailVAO; // 低细节网格
    }

    gl.bindVertexArray(vao);
    gl.drawElements(gl.TRIANGLES, object.indexCount, gl.UNSIGNED_SHORT, 0);
}

// ✅ renderLOD 完整使用示例
function exampleRenderLOD() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // LOD级别定义
    const LOD_LEVELS = {
        HIGH: { distance: 10, subdivisions: 32 },
        MEDIUM: { distance: 50, subdivisions: 16 },
        LOW: { distance: 100, subdivisions: 8 },
    };

    // 生成球体几何体（不同细节级别）
    function generateSphere(subdivisions, radius = 1.0) {
        const vertices = [];
        const indices = [];

        // 生成顶点
        for (let lat = 0; lat <= subdivisions; lat++) {
            const theta = (lat * Math.PI) / subdivisions;
            const sinTheta = Math.sin(theta);
            const cosTheta = Math.cos(theta);

            for (let lon = 0; lon <= subdivisions; lon++) {
                const phi = (lon * 2 * Math.PI) / subdivisions;
                const sinPhi = Math.sin(phi);
                const cosPhi = Math.cos(phi);

                const x = cosPhi * sinTheta;
                const y = cosTheta;
                const z = sinPhi * sinTheta;

                // 位置
                vertices.push(x * radius, y * radius, z * radius);
                // 法线（球体的法线就是归一化的位置）
                vertices.push(x, y, z);
                // 纹理坐标
                vertices.push(lon / subdivisions, lat / subdivisions);
            }
        }

        // 生成索引
        for (let lat = 0; lat < subdivisions; lat++) {
            for (let lon = 0; lon < subdivisions; lon++) {
                const first = lat * (subdivisions + 1) + lon;
                const second = first + subdivisions + 1;

                // 第一个三角形
                indices.push(first, second, first + 1);
                // 第二个三角形
                indices.push(second, second + 1, first + 1);
            }
        }

        return {
            vertices: new Float32Array(vertices),
            indices: new Uint16Array(indices),
        };
    }

    // 创建VAO
    function createVAO(gl, program, geometry) {
        const vao = gl.createVertexArray();
        gl.bindVertexArray(vao);

        // 创建顶点缓冲区
        const vertexBuffer = new VertexBuffer(gl, geometry.vertices);

        // 创建索引缓冲区
        const indexBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, geometry.indices, gl.STATIC_DRAW);

        // 配置属性
        const stride = 8 * 4; // 位置(3) + 法线(3) + 纹理坐标(2)

        const positionLocation = gl.getAttribLocation(program, 'a_position');
        const normalLocation = gl.getAttribLocation(program, 'a_normal');
        const texCoordLocation = gl.getAttribLocation(program, 'a_texCoord');

        vertexBuffer.bind();

        // 位置属性
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

        // 法线属性
        gl.enableVertexAttribArray(normalLocation);
        gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 12);

        // 纹理坐标属性
        gl.enableVertexAttribArray(texCoordLocation);
        gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, stride, 24);

        gl.bindVertexArray(null);

        return {
            vao: vao,
            indexCount: geometry.indices.length,
            vertexBuffer: vertexBuffer,
            indexBuffer: indexBuffer,
        };
    }

    // 创建LOD对象
    function createLODObject(gl, program, position) {
        const highDetailGeometry = generateSphere(LOD_LEVELS.HIGH.subdivisions);
        const mediumDetailGeometry = generateSphere(LOD_LEVELS.MEDIUM.subdivisions);
        const lowDetailGeometry = generateSphere(LOD_LEVELS.LOW.subdivisions);

        return {
            position: position,
            highDetailVAO: createVAO(gl, program, highDetailGeometry),
            mediumDetailVAO: createVAO(gl, program, mediumDetailGeometry),
            lowDetailVAO: createVAO(gl, program, lowDetailGeometry),
            currentLOD: 'HIGH',
        };
    }

    // 改进的renderLOD函数
    function renderLODImproved(gl, program, object, cameraPosition) {
        // 计算距离
        const dx = object.position[0] - cameraPosition[0];
        const dy = object.position[1] - cameraPosition[1];
        const dz = object.position[2] - cameraPosition[2];
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

        // 选择LOD级别
        let lodData;
        let newLOD;

        if (distance < LOD_LEVELS.HIGH.distance) {
            lodData = object.highDetailVAO;
            newLOD = 'HIGH';
        } else if (distance < LOD_LEVELS.MEDIUM.distance) {
            lodData = object.mediumDetailVAO;
            newLOD = 'MEDIUM';
        } else {
            lodData = object.lowDetailVAO;
            newLOD = 'LOW';
        }

        // 记录LOD变化（用于调试）
        if (object.currentLOD !== newLOD) {
            console.log(`LOD changed from ${object.currentLOD} to ${newLOD} at distance ${distance.toFixed(2)}`);
            object.currentLOD = newLOD;
        }

        // 渲染
        gl.useProgram(program);
        gl.bindVertexArray(lodData.vao);
        gl.drawElements(gl.TRIANGLES, lodData.indexCount, gl.UNSIGNED_SHORT, 0);
        gl.bindVertexArray(null);

        return newLOD;
    }

    // 创建多个LOD对象
    const objects = [
        createLODObject(gl, program, [0, 0, -5]),
        createLODObject(gl, program, [3, 0, -10]),
        createLODObject(gl, program, [-3, 0, -15]),
        createLODObject(gl, program, [0, 3, -25]),
        createLODObject(gl, program, [5, -2, -40]),
    ];

    // 相机控制
    let cameraPosition = [0, 0, 0];
    let cameraSpeed = 0.1;

    // 键盘控制
    const keys = {};
    document.addEventListener('keydown', (e) => (keys[e.key] = true));
    document.addEventListener('keyup', (e) => (keys[e.key] = false));

    function updateCamera() {
        if (keys['w'] || keys['W']) cameraPosition[2] -= cameraSpeed;
        if (keys['s'] || keys['S']) cameraPosition[2] += cameraSpeed;
        if (keys['a'] || keys['A']) cameraPosition[0] -= cameraSpeed;
        if (keys['d'] || keys['D']) cameraPosition[0] += cameraSpeed;
        if (keys['q'] || keys['Q']) cameraPosition[1] += cameraSpeed;
        if (keys['e'] || keys['E']) cameraPosition[1] -= cameraSpeed;
    }

    // 渲染循环
    function render() {
        updateCamera();

        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        gl.enable(gl.DEPTH_TEST);

        // 渲染所有对象
        objects.forEach((object) => {
            renderLODImproved(gl, program, object, cameraPosition);
        });

        requestAnimationFrame(render);
    }

    render();

    // 显示控制说明
    console.log('LOD Demo Controls:');
    console.log('WASD - Move camera horizontally');
    console.log('QE - Move camera vertically');
    console.log('Watch console for LOD level changes');
}

// ✅ renderLOD 高级LOD管理示例
function exampleAdvancedLODManagement() {
    const gl = getWebGLContext();
    const program = createShaderProgram(gl, vertexShader, fragmentShader);

    // 高级LOD管理器
    class LODManager {
        constructor(gl, program) {
            this.gl = gl;
            this.program = program;
            this.objects = [];
            this.lodStats = {
                high: 0,
                medium: 0,
                low: 0,
                culled: 0,
            };

            // LOD配置
            this.lodConfig = {
                levels: [
                    { name: 'HIGH', maxDistance: 15, subdivisions: 32 },
                    { name: 'MEDIUM', maxDistance: 40, subdivisions: 16 },
                    { name: 'LOW', maxDistance: 80, subdivisions: 8 },
                ],
                cullDistance: 100,
                hysteresis: 0.1, // 防止LOD频繁切换的滞后因子
            };
        }

        addObject(position, scale = 1.0) {
            const object = {
                id: this.objects.length,
                position: position,
                scale: scale,
                lodLevels: [],
                currentLOD: -1,
                lastDistance: 0,
                visible: true,
            };

            // 为每个LOD级别创建几何体
            this.lodConfig.levels.forEach((level, index) => {
                const geometry = this.generateSphere(level.subdivisions, scale);
                const vaoData = this.createVAO(geometry);
                object.lodLevels.push({
                    ...level,
                    ...vaoData,
                });
            });

            this.objects.push(object);
            return object.id;
        }

        generateSphere(subdivisions, radius) {
            // 简化的球体生成（与之前相同）
            const vertices = [];
            const indices = [];

            for (let lat = 0; lat <= subdivisions; lat++) {
                const theta = (lat * Math.PI) / subdivisions;
                const sinTheta = Math.sin(theta);
                const cosTheta = Math.cos(theta);

                for (let lon = 0; lon <= subdivisions; lon++) {
                    const phi = (lon * 2 * Math.PI) / subdivisions;
                    const sinPhi = Math.sin(phi);
                    const cosPhi = Math.cos(phi);

                    const x = cosPhi * sinTheta;
                    const y = cosTheta;
                    const z = sinPhi * sinTheta;

                    vertices.push(x * radius, y * radius, z * radius);
                    vertices.push(x, y, z);
                    vertices.push(lon / subdivisions, lat / subdivisions);
                }
            }

            for (let lat = 0; lat < subdivisions; lat++) {
                for (let lon = 0; lon < subdivisions; lon++) {
                    const first = lat * (subdivisions + 1) + lon;
                    const second = first + subdivisions + 1;

                    indices.push(first, second, first + 1);
                    indices.push(second, second + 1, first + 1);
                }
            }

            return {
                vertices: new Float32Array(vertices),
                indices: new Uint16Array(indices),
            };
        }

        createVAO(geometry) {
            const vao = gl.createVertexArray();
            gl.bindVertexArray(vao);

            const vertexBuffer = new VertexBuffer(gl, geometry.vertices);
            const indexBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
            gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, geometry.indices, gl.STATIC_DRAW);

            const stride = 8 * 4;
            const positionLocation = gl.getAttribLocation(this.program, 'a_position');
            const normalLocation = gl.getAttribLocation(this.program, 'a_normal');
            const texCoordLocation = gl.getAttribLocation(this.program, 'a_texCoord');

            vertexBuffer.bind();

            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, stride, 0);

            gl.enableVertexAttribArray(normalLocation);
            gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, stride, 12);

            gl.enableVertexAttribArray(texCoordLocation);
            gl.vertexAttribPointer(texCoordLocation, 2, gl.FLOAT, false, stride, 24);

            gl.bindVertexArray(null);

            return {
                vao: vao,
                indexCount: geometry.indices.length,
                vertexBuffer: vertexBuffer,
                indexBuffer: indexBuffer,
            };
        }

        selectLOD(object, distance) {
            // 应用滞后因子防止频繁切换
            const hysteresis = this.lodConfig.hysteresis;
            const adjustedDistance = object.lastDistance > distance ? distance * (1 + hysteresis) : distance * (1 - hysteresis);

            for (let i = 0; i < this.lodConfig.levels.length; i++) {
                if (adjustedDistance <= this.lodConfig.levels[i].maxDistance) {
                    return i;
                }
            }

            return -1; // 超出渲染距离，应该被剔除
        }

        render(cameraPosition) {
            // 重置统计
            this.lodStats = { high: 0, medium: 0, low: 0, culled: 0 };

            gl.useProgram(this.program);

            this.objects.forEach((object) => {
                // 计算距离
                const dx = object.position[0] - cameraPosition[0];
                const dy = object.position[1] - cameraPosition[1];
                const dz = object.position[2] - cameraPosition[2];
                const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);

                // 视锥剔除检查（简化版）
                if (distance > this.lodConfig.cullDistance) {
                    object.visible = false;
                    this.lodStats.culled++;
                    return;
                }

                // 选择LOD级别
                const lodIndex = this.selectLOD(object, distance);

                if (lodIndex === -1) {
                    object.visible = false;
                    this.lodStats.culled++;
                    return;
                }

                object.visible = true;
                object.currentLOD = lodIndex;
                object.lastDistance = distance;

                // 更新统计
                const lodName = this.lodConfig.levels[lodIndex].name.toLowerCase();
                this.lodStats[lodName]++;

                // 渲染
                const lodLevel = object.lodLevels[lodIndex];
                gl.bindVertexArray(lodLevel.vao);
                gl.drawElements(gl.TRIANGLES, lodLevel.indexCount, gl.UNSIGNED_SHORT, 0);
            });

            gl.bindVertexArray(null);
        }

        getStats() {
            return { ...this.lodStats };
        }

        cleanup() {
            this.objects.forEach((object) => {
                object.lodLevels.forEach((level) => {
                    level.vertexBuffer.delete();
                    gl.deleteBuffer(level.indexBuffer);
                    gl.deleteVertexArray(level.vao);
                });
            });
            this.objects = [];
        }
    }

    // 使用高级LOD管理器
    const lodManager = new LODManager(gl, program);

    // 添加多个对象
    for (let i = 0; i < 20; i++) {
        const x = (Math.random() - 0.5) * 100;
        const y = (Math.random() - 0.5) * 20;
        const z = -Math.random() * 100 - 10;
        const scale = 0.5 + Math.random() * 1.5;

        lodManager.addObject([x, y, z], scale);
    }

    let cameraPosition = [0, 0, 0];
    let lastStatsUpdate = 0;

    function render(timestamp) {
        // 更新相机（简化的移动）
        cameraPosition[2] -= 0.05;
        if (cameraPosition[2] < -150) cameraPosition[2] = 10;

        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        gl.enable(gl.DEPTH_TEST);

        // 渲染所有对象
        lodManager.render(cameraPosition);

        // 每秒更新一次统计信息
        if (timestamp - lastStatsUpdate > 1000) {
            const stats = lodManager.getStats();
            console.log('LOD Stats:', stats);
            lastStatsUpdate = timestamp;
        }

        requestAnimationFrame(render);
    }

    render(0);
}

// 调用示例
// exampleRenderLOD();
// exampleAdvancedLODManagement();
```

### 场景 3: 实例化渲染

```javascript
// 渲染大量相似对象（如草地、树木）
function setupInstancedRendering(gl, program) {
    // 基础几何体属性（每个顶点）
    setupAttribute(gl, program, 'a_position', baseGeometryBuffer, 3);
    setupAttribute(gl, program, 'a_normal', baseNormalBuffer, 3);

    // 实例属性（每个实例）
    setupAttribute(gl, program, 'a_instancePosition', instancePositionBuffer, 3);
    setupAttribute(gl, program, 'a_instanceScale', instanceScaleBuffer, 1);

    // 设置实例属性的除数
    gl.vertexAttribDivisor(gl.getAttribLocation(program, 'a_instancePosition'), 1);
    gl.vertexAttribDivisor(gl.getAttribLocation(program, 'a_instanceScale'), 1);
}

// ✅ setupInstancedRendering 完整使用示例
function exampleSetupInstancedRendering() {
    const gl = getWebGLContext();

    // 实例化渲染着色器
    const vertexShaderSource = `#version 300 es
        in vec3 a_position;
        in vec3 a_normal;
        in vec3 a_instancePosition;  // 每个实例的位置
        in float a_instanceScale;    // 每个实例的缩放
        in vec3 a_instanceColor;     // 每个实例的颜色

        uniform mat4 u_viewMatrix;
        uniform mat4 u_projectionMatrix;

        out vec3 v_normal;
        out vec3 v_color;

        void main() {
            // 应用实例变换
            vec3 scaledPosition = a_position * a_instanceScale;
            vec3 worldPosition = scaledPosition + a_instancePosition;

            gl_Position = u_projectionMatrix * u_viewMatrix * vec4(worldPosition, 1.0);

            v_normal = a_normal;
            v_color = a_instanceColor;
        }`;

    const fragmentShaderSource = `#version 300 es
        precision mediump float;

        in vec3 v_normal;
        in vec3 v_color;

        out vec4 fragColor;

        void main() {
            // 简单的光照计算
            vec3 lightDir = normalize(vec3(1.0, 1.0, 1.0));
            float lightIntensity = max(dot(normalize(v_normal), lightDir), 0.2);

            fragColor = vec4(v_color * lightIntensity, 1.0);
        }`;

    const program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);

    // 创建基础几何体（立方体）
    function createCubeGeometry() {
        const positions = new Float32Array([
            // 前面
            -0.5, -0.5, 0.5, 0.5, -0.5, 0.5, 0.5, 0.5, 0.5, -0.5, 0.5, 0.5,
            // 后面
            -0.5, -0.5, -0.5, -0.5, 0.5, -0.5, 0.5, 0.5, -0.5, 0.5, -0.5, -0.5,
            // 顶面
            -0.5, 0.5, -0.5, -0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, -0.5,
            // 底面
            -0.5, -0.5, -0.5, 0.5, -0.5, -0.5, 0.5, -0.5, 0.5, -0.5, -0.5, 0.5,
            // 右面
            0.5, -0.5, -0.5, 0.5, 0.5, -0.5, 0.5, 0.5, 0.5, 0.5, -0.5, 0.5,
            // 左面
            -0.5, -0.5, -0.5, -0.5, -0.5, 0.5, -0.5, 0.5, 0.5, -0.5, 0.5, -0.5,
        ]);

        const normals = new Float32Array([
            // 前面
            0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1,
            // 后面
            0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1,
            // 顶面
            0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0,
            // 底面
            0, -1, 0, 0, -1, 0, 0, -1, 0, 0, -1, 0,
            // 右面
            1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0,
            // 左面
            -1, 0, 0, -1, 0, 0, -1, 0, 0, -1, 0, 0,
        ]);

        const indices = new Uint16Array([
            0,
            1,
            2,
            0,
            2,
            3, // 前面
            4,
            5,
            6,
            4,
            6,
            7, // 后面
            8,
            9,
            10,
            8,
            10,
            11, // 顶面
            12,
            13,
            14,
            12,
            14,
            15, // 底面
            16,
            17,
            18,
            16,
            18,
            19, // 右面
            20,
            21,
            22,
            20,
            22,
            23, // 左面
        ]);

        return { positions, normals, indices };
    }

    // 生成实例数据
    function generateInstanceData(count) {
        const positions = new Float32Array(count * 3);
        const scales = new Float32Array(count);
        const colors = new Float32Array(count * 3);

        for (let i = 0; i < count; i++) {
            // 随机位置
            positions[i * 3] = (Math.random() - 0.5) * 20; // x
            positions[i * 3 + 1] = (Math.random() - 0.5) * 20; // y
            positions[i * 3 + 2] = (Math.random() - 0.5) * 20; // z

            // 随机缩放
            scales[i] = 0.5 + Math.random() * 1.5;

            // 随机颜色
            colors[i * 3] = Math.random(); // r
            colors[i * 3 + 1] = Math.random(); // g
            colors[i * 3 + 2] = Math.random(); // b
        }

        return { positions, scales, colors };
    }

    // 创建几何体和实例数据
    const cubeGeometry = createCubeGeometry();
    const instanceCount = 1000;
    const instanceData = generateInstanceData(instanceCount);

    // 创建缓冲区
    const positionBuffer = new VertexBuffer(gl, cubeGeometry.positions);
    const normalBuffer = new VertexBuffer(gl, cubeGeometry.normals);

    const indexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
    gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, cubeGeometry.indices, gl.STATIC_DRAW);

    // 实例数据缓冲区
    const instancePositionBuffer = new VertexBuffer(gl, instanceData.positions);
    const instanceScaleBuffer = new VertexBuffer(gl, instanceData.scales);
    const instanceColorBuffer = new VertexBuffer(gl, instanceData.colors);

    // 改进的setupInstancedRendering函数
    function setupInstancedRenderingImproved(gl, program) {
        // 获取属性位置
        const positionLocation = gl.getAttribLocation(program, 'a_position');
        const normalLocation = gl.getAttribLocation(program, 'a_normal');
        const instancePositionLocation = gl.getAttribLocation(program, 'a_instancePosition');
        const instanceScaleLocation = gl.getAttribLocation(program, 'a_instanceScale');
        const instanceColorLocation = gl.getAttribLocation(program, 'a_instanceColor');

        // 设置基础几何体属性（每个顶点）
        positionBuffer.bind();
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

        normalBuffer.bind();
        gl.enableVertexAttribArray(normalLocation);
        gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, 0, 0);

        // 设置实例属性（每个实例）
        instancePositionBuffer.bind();
        gl.enableVertexAttribArray(instancePositionLocation);
        gl.vertexAttribPointer(instancePositionLocation, 3, gl.FLOAT, false, 0, 0);
        gl.vertexAttribDivisor(instancePositionLocation, 1); // 每个实例更新一次

        instanceScaleBuffer.bind();
        gl.enableVertexAttribArray(instanceScaleLocation);
        gl.vertexAttribPointer(instanceScaleLocation, 1, gl.FLOAT, false, 0, 0);
        gl.vertexAttribDivisor(instanceScaleLocation, 1);

        instanceColorBuffer.bind();
        gl.enableVertexAttribArray(instanceColorLocation);
        gl.vertexAttribPointer(instanceColorLocation, 3, gl.FLOAT, false, 0, 0);
        gl.vertexAttribDivisor(instanceColorLocation, 1);
    }

    // 创建VAO封装所有状态
    const vao = gl.createVertexArray();
    gl.bindVertexArray(vao);

    setupInstancedRenderingImproved(gl, program);
    gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);

    gl.bindVertexArray(null);

    // 渲染函数
    function render() {
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        gl.enable(gl.DEPTH_TEST);

        gl.useProgram(program);

        // 设置uniform（视图和投影矩阵）
        // 这里应该设置实际的矩阵，示例中省略

        gl.bindVertexArray(vao);

        // 实例化绘制
        gl.drawElementsInstanced(gl.TRIANGLES, cubeGeometry.indices.length, gl.UNSIGNED_SHORT, 0, instanceCount);

        gl.bindVertexArray(null);
    }

    render();
}

// ✅ setupInstancedRendering 动态实例化示例
function exampleDynamicInstancedRendering() {
    const gl = getWebGLContext();
    const program = createInstancedProgram(); // 假设已创建

    // 动态实例化管理器
    class InstancedRenderManager {
        constructor(gl, program, baseGeometry, maxInstances = 10000) {
            this.gl = gl;
            this.program = program;
            this.maxInstances = maxInstances;
            this.currentInstanceCount = 0;

            // 创建基础几何体缓冲区
            this.positionBuffer = new VertexBuffer(gl, baseGeometry.positions);
            this.normalBuffer = new VertexBuffer(gl, baseGeometry.normals);

            this.indexBuffer = gl.createBuffer();
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, baseGeometry.indices, gl.STATIC_DRAW);
            this.indexCount = baseGeometry.indices.length;

            // 创建实例数据缓冲区（预分配最大容量）
            this.instancePositionBuffer = new VertexBuffer(gl, new Float32Array(maxInstances * 3), gl.DYNAMIC_DRAW);
            this.instanceScaleBuffer = new VertexBuffer(gl, new Float32Array(maxInstances), gl.DYNAMIC_DRAW);
            this.instanceColorBuffer = new VertexBuffer(gl, new Float32Array(maxInstances * 3), gl.DYNAMIC_DRAW);

            // 创建VAO
            this.vao = gl.createVertexArray();
            this.setupVAO();

            // 实例数据数组
            this.instances = [];
        }

        setupVAO() {
            gl.bindVertexArray(this.vao);

            const positionLocation = gl.getAttribLocation(this.program, 'a_position');
            const normalLocation = gl.getAttribLocation(this.program, 'a_normal');
            const instancePositionLocation = gl.getAttribLocation(this.program, 'a_instancePosition');
            const instanceScaleLocation = gl.getAttribLocation(this.program, 'a_instanceScale');
            const instanceColorLocation = gl.getAttribLocation(this.program, 'a_instanceColor');

            // 基础几何体属性
            this.positionBuffer.bind();
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);

            this.normalBuffer.bind();
            gl.enableVertexAttribArray(normalLocation);
            gl.vertexAttribPointer(normalLocation, 3, gl.FLOAT, false, 0, 0);

            // 实例属性
            this.instancePositionBuffer.bind();
            gl.enableVertexAttribArray(instancePositionLocation);
            gl.vertexAttribPointer(instancePositionLocation, 3, gl.FLOAT, false, 0, 0);
            gl.vertexAttribDivisor(instancePositionLocation, 1);

            this.instanceScaleBuffer.bind();
            gl.enableVertexAttribArray(instanceScaleLocation);
            gl.vertexAttribPointer(instanceScaleLocation, 1, gl.FLOAT, false, 0, 0);
            gl.vertexAttribDivisor(instanceScaleLocation, 1);

            this.instanceColorBuffer.bind();
            gl.enableVertexAttribArray(instanceColorLocation);
            gl.vertexAttribPointer(instanceColorLocation, 3, gl.FLOAT, false, 0, 0);
            gl.vertexAttribDivisor(instanceColorLocation, 1);

            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
            gl.bindVertexArray(null);
        }

        addInstance(position, scale, color) {
            if (this.currentInstanceCount >= this.maxInstances) {
                console.warn('Maximum instance count reached');
                return false;
            }

            this.instances.push({ position, scale, color });
            this.currentInstanceCount++;
            return true;
        }

        removeInstance(index) {
            if (index >= 0 && index < this.instances.length) {
                this.instances.splice(index, 1);
                this.currentInstanceCount--;
                return true;
            }
            return false;
        }

        updateInstanceData() {
            if (this.instances.length === 0) return;

            const positions = new Float32Array(this.instances.length * 3);
            const scales = new Float32Array(this.instances.length);
            const colors = new Float32Array(this.instances.length * 3);

            this.instances.forEach((instance, i) => {
                positions[i * 3] = instance.position[0];
                positions[i * 3 + 1] = instance.position[1];
                positions[i * 3 + 2] = instance.position[2];

                scales[i] = instance.scale;

                colors[i * 3] = instance.color[0];
                colors[i * 3 + 1] = instance.color[1];
                colors[i * 3 + 2] = instance.color[2];
            });

            // 更新缓冲区
            this.instancePositionBuffer.update(positions);
            this.instanceScaleBuffer.update(scales);
            this.instanceColorBuffer.update(colors);
        }

        render() {
            if (this.currentInstanceCount === 0) return;

            this.updateInstanceData();

            gl.useProgram(this.program);
            gl.bindVertexArray(this.vao);

            gl.drawElementsInstanced(gl.TRIANGLES, this.indexCount, gl.UNSIGNED_SHORT, 0, this.currentInstanceCount);

            gl.bindVertexArray(null);
        }

        cleanup() {
            this.positionBuffer.delete();
            this.normalBuffer.delete();
            this.instancePositionBuffer.delete();
            this.instanceScaleBuffer.delete();
            this.instanceColorBuffer.delete();
            gl.deleteBuffer(this.indexBuffer);
            gl.deleteVertexArray(this.vao);
        }
    }

    // 使用动态实例化管理器
    const cubeGeometry = createCubeGeometry(); // 假设已定义
    const instanceManager = new InstancedRenderManager(gl, program, cubeGeometry, 5000);

    // 动态添加实例
    function addRandomInstance() {
        const position = [(Math.random() - 0.5) * 20, (Math.random() - 0.5) * 20, (Math.random() - 0.5) * 20];
        const scale = 0.5 + Math.random() * 1.5;
        const color = [Math.random(), Math.random(), Math.random()];

        instanceManager.addInstance(position, scale, color);
    }

    // 初始添加一些实例
    for (let i = 0; i < 100; i++) {
        addRandomInstance();
    }

    // 动画循环
    function animate() {
        // 随机添加/删除实例
        if (Math.random() < 0.1) {
            addRandomInstance();
        }

        if (Math.random() < 0.05 && instanceManager.currentInstanceCount > 50) {
            const randomIndex = Math.floor(Math.random() * instanceManager.currentInstanceCount);
            instanceManager.removeInstance(randomIndex);
        }

        // 渲染
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);
        gl.enable(gl.DEPTH_TEST);

        instanceManager.render();

        requestAnimationFrame(animate);
    }

    animate();
}

// 调用示例
// exampleSetupInstancedRendering();
// exampleDynamicInstancedRendering();
```

## 总结

理解 WebGL 顶点数据操作的关键在于掌握：

### 核心概念

1. **数据流程**: CPU → GPU 内存 → 顶点属性配置 → 着色器
2. **状态机特性**: WebGL 的全局状态和绑定机制
3. **内存布局**: 分离式 vs 交错式数据组织

### 关键 API

1. **enableVertexAttribArray()**: 启用顶点属性数组
2. **vertexAttribPointer()**: 定义数据解释规则
3. **bindBuffer()**: 设置当前活动缓冲区

### 最佳实践

1. **使用 VAO**: 封装顶点状态，提高性能
2. **错误检查**: 验证属性配置和着色器匹配
3. **内存管理**: 及时清理缓冲区资源
4. **性能优化**: 减少状态切换，使用批量处理

### 常见陷阱

1. 忘记启用顶点属性
2. 缓冲区绑定状态混乱
3. 步长和偏移量计算错误
4. 数据类型不匹配
5. 归一化标志使用错误

通过深入理解这些概念和 API，您将能够高效地处理各种复杂的顶点数据场景，构建出性能优异的 WebGL 应用程序。记住，WebGL 的顶点处理是整个渲染管线的基础，掌握好这部分对于后续的学习和开发都至关重要。
