# WebGL 纹理坐标系统与像素中心偏移详解

## 🎯 核心问题

在 WebGL 纹理采样中，经常需要进行 Y 坐标调整和像素中心偏移，这涉及两个关键概念：

1. **坐标系差异**：WebGL vs 图像坐标系
2. **像素中心偏移**：整数坐标 vs 像素中心坐标

## 📐 坐标系差异详解

### 1. WebGL 坐标系 vs 图像坐标系

```
WebGL坐标系（Y轴向上）:        图像坐标系（Y轴向下）:
(0,1) -------- (1,1)          (0,0) -------- (1,0)
  |              |               |              |
  |              |               |              |
  |              |               |              |
(0,0) -------- (1,0)          (0,1) -------- (1,1)
```

### 2. Y 坐标翻转的必要性

```glsl
// 在片段着色器中常见的Y坐标调整
vec2 uv = gl_FragCoord.xy / u_resolution;
uv.y = 1.0 - uv.y;  // 翻转Y坐标

// 或者在纹理加载时设置
texture.flipY = true;  // 自动翻转Y轴
```

## 🎯 像素中心偏移详解

### 1. 像素坐标 vs 像素中心

```
像素网格示意图：
┌─────┬─────┬─────┬─────┐
│  0  │  1  │  2  │  3  │  ← 像素索引
│ 0.5 │ 1.5 │ 2.5 │ 3.5 │  ← 像素中心
└─────┴─────┴─────┴─────┘
0     1     2     3     4   ← 坐标轴
```

### 2. 为什么需要 +0.5 偏移

```javascript
// ❌ 错误：直接使用整数坐标
const x = 0; // 采样点在像素边界
const y = 0; // 可能导致采样不准确

// ✅ 正确：使用像素中心坐标
const x = 0.5; // 采样点在像素中心
const y = 0.5; // 获得准确的像素值
```

## 🔧 实际应用场景

### 1. 屏幕空间纹理采样

```glsl
precision mediump float;

uniform sampler2D u_texture;
uniform vec2 u_resolution;

void main() {
    // 获取当前片段的屏幕坐标
    vec2 fragCoord = gl_FragCoord.xy;

    // 方法1：直接使用（可能不准确）
    vec2 uv1 = fragCoord / u_resolution;

    // 方法2：添加像素中心偏移（推荐）
    vec2 uv2 = (fragCoord + 0.5) / u_resolution;

    // 方法3：手动计算相邻像素的中心
    vec2 uv3 = (fragCoord + vec2(0.5, 1.5)) / u_resolution;

    gl_FragColor = texture2D(u_texture, uv2);
}
```

### 2. 数据纹理精确采样

```glsl
// 用于计算着色器或数据纹理的精确采样
uniform sampler2D u_dataTexture;
uniform float u_textureSize;

vec4 sampleDataTexture(float index) {
    // 计算纹理坐标（确保采样到像素中心）
    float x = mod(index, u_textureSize) + 0.5;
    float y = floor(index / u_textureSize) + 0.5;

    vec2 uv = vec2(x, y) / u_textureSize;
    return texture2D(u_dataTexture, uv);
}

/*
取值范围详解（以4x4纹理为例，u_textureSize = 4.0）：

index 范围：[0, 15] （线性索引）

计算过程：
- mod(index, 4.0) 范围：[0, 4) → 值：0, 1, 2, 3
- floor(index / 4.0) 范围：[0, 3] → 值：0, 1, 2, 3
- x = mod + 0.5 范围：[0.5, 4.5) → 值：0.5, 1.5, 2.5, 3.5
- y = floor + 0.5 范围：[0.5, 3.5] → 值：0.5, 1.5, 2.5, 3.5
- uv.x = x/4.0 范围：[0.125, 1.125) → 值：0.125, 0.375, 0.625, 0.875
- uv.y = y/4.0 范围：[0.125, 0.875] → 值：0.125, 0.375, 0.625, 0.875

具体映射表：
index=0  → x=0.5, y=0.5 → uv=(0.125, 0.125) → 像素(0,0)中心
index=1  → x=1.5, y=0.5 → uv=(0.375, 0.125) → 像素(1,0)中心
index=4  → x=0.5, y=1.5 → uv=(0.125, 0.375) → 像素(0,1)中心
index=15 → x=3.5, y=3.5 → uv=(0.875, 0.875) → 像素(3,3)中心
*/
```

### 3. 多重采样和滤波

```glsl
// 4x4采样示例
vec4 sample4x4(sampler2D tex, vec2 uv, vec2 texelSize) {
    vec4 result = vec4(0.0);

    // 采样4x4网格的像素中心
    for(int x = 0; x < 4; x++) {
        for(int y = 0; y < 4; y++) {
            vec2 offset = vec2(float(x) + 0.5, float(y) + 0.5) * texelSize;
            result += texture2D(tex, uv + offset);
        }
    }

    return result / 16.0;  // 平均值
}

/*
取值范围详解（以512x512纹理为例，texelSize = 1/512 ≈ 0.00195）：

输入参数：
- uv: 基础纹理坐标 [0.0, 1.0]
- texelSize: (0.00195, 0.00195)

循环变量：
- x: [0, 3] → 整数值：0, 1, 2, 3
- y: [0, 3] → 整数值：0, 1, 2, 3

像素中心偏移计算：
- float(x) + 0.5: [0.5, 3.5] → 值：0.5, 1.5, 2.5, 3.5
- float(y) + 0.5: [0.5, 3.5] → 值：0.5, 1.5, 2.5, 3.5

offset 范围：
- offset.x: [0.5 * 0.00195, 3.5 * 0.00195] = [0.000976, 0.006836]
- offset.y: [0.5 * 0.00195, 3.5 * 0.00195] = [0.000976, 0.006836]

16个采样点的offset值：
┌─────┬─────┬─────────────┬─────────────┐
│  x  │  y  │  offset.x   │  offset.y   │
├─────┼─────┼─────────────┼─────────────┤
│  0  │  0  │  0.000976   │  0.000976   │
│  1  │  0  │  0.002930   │  0.000976   │
│  2  │  0  │  0.004883   │  0.000976   │
│  3  │  0  │  0.006836   │  0.000976   │
│  0  │  1  │  0.000976   │  0.002930   │
│ ... │ ... │     ...     │     ...     │
│  3  │  3  │  0.006836   │  0.006836   │
└─────┴─────┴─────────────┴─────────────┘

最终采样坐标范围：
- 最小采样点: uv + (0.000976, 0.000976)
- 最大采样点: uv + (0.006836, 0.006836)
- 覆盖区域: 4x4像素的中心点区域
*/
```

## 🧮 坐标变换公式

### 1. 屏幕坐标到纹理坐标

```glsl
// 基础变换
vec2 screenToTexture(vec2 screenCoord, vec2 screenSize) {
    // 添加像素中心偏移
    vec2 pixelCenter = screenCoord + 0.5;

    // 归一化到[0,1]范围
    vec2 uv = pixelCenter / screenSize;

    // 可选：翻转Y轴
    uv.y = 1.0 - uv.y;

    return uv;
}
```

### 2. 纹理坐标到像素索引

```glsl
// 纹理坐标转换为精确的像素索引
ivec2 textureToPixel(vec2 uv, vec2 textureSize) {
    // 缩放到纹理尺寸
    vec2 pixelCoord = uv * textureSize;

    // 减去0.5偏移，然后取整
    return ivec2(floor(pixelCoord - 0.5));
}

/*
取值范围详解（以4x4纹理为例，textureSize = (4.0, 4.0)）：

输入参数：
- uv: 纹理坐标 [0.0, 1.0]
- textureSize: (4.0, 4.0)

计算步骤：
1. pixelCoord = uv * textureSize
   - 输入: uv ∈ [0.0, 1.0]
   - 输出: pixelCoord ∈ [0.0, 4.0]

2. pixelCoord - 0.5
   - 输入: pixelCoord ∈ [0.0, 4.0]
   - 输出: (pixelCoord - 0.5) ∈ [-0.5, 3.5]

3. floor(pixelCoord - 0.5)
   - 输入: [-0.5, 3.5]
   - 输出: [-1, 3] (整数)

详细映射表：
┌─────────────┬─────────────┬─────────────┬─────────────┬─────────────┐
│     uv      │ pixelCoord  │ coord-0.5   │   floor()   │  像素索引   │
├─────────────┼─────────────┼─────────────┼─────────────┼─────────────┤
│ (0.0, 0.0)  │ (0.0, 0.0)  │ (-0.5,-0.5) │   (-1,-1)   │   边界外    │
│ (0.125,0.125)│(0.5, 0.5)  │ (0.0, 0.0)  │   (0, 0)    │  像素(0,0)  │
│ (0.375,0.375)│(1.5, 1.5)  │ (1.0, 1.0)  │   (1, 1)    │  像素(1,1)  │
│ (0.625,0.625)│(2.5, 2.5)  │ (2.0, 2.0)  │   (2, 2)    │  像素(2,2)  │
│ (0.875,0.875)│(3.5, 3.5)  │ (3.0, 3.0)  │   (3, 3)    │  像素(3,3)  │
│ (1.0, 1.0)  │ (4.0, 4.0)  │ (3.5, 3.5)  │   (3, 3)    │  像素(3,3)  │
└─────────────┴─────────────┴─────────────┴─────────────┴─────────────┘

关键理解：
• 减去0.5是为了将像素中心坐标转换回像素索引
• 输出范围 [-1, textureSize-1]，其中-1表示边界外
• 有效像素索引范围 [0, textureSize-1]
• 这是像素中心采样的逆向转换
*/
```

## ⚡ 性能优化建议

### 1. 预计算偏移值

```javascript
// 在JavaScript中预计算
const pixelCenterOffset = 0.5;
const texelSize = 1.0 / textureSize;

// 传递给着色器
program.uniforms.u_pixelOffset = { value: pixelCenterOffset };
program.uniforms.u_texelSize = { value: texelSize };
```

### 2. 使用内置函数

```glsl
// 使用texelFetch进行精确采样（WebGL 2.0）
vec4 color = texelFetch(u_texture, ivec2(x, y), 0);

// 而不是手动计算偏移
vec2 uv = (vec2(x, y) + 0.5) / textureSize;
vec4 color = texture2D(u_texture, uv);
```

## 🔍 texelFetch vs texture2D 详细对比

### 核心区别总览

```glsl
// texture2D - 传统纹理采样
vec4 texture2D(sampler2D sampler, vec2 coord);

// texelFetch - 精确像素读取 (WebGL 2.0)
vec4 texelFetch(sampler2D sampler, ivec2 coord, int lod);
```

### 详细对比表

┌─────────────┬─────────────────┬─────────────────┐
│ 特性 │ texture2D │ texelFetch │
├─────────────┼─────────────────┼─────────────────┤
│ 坐标类型 │ vec2 (浮点) │ ivec2 (整数) │
│ 坐标范围 │ [0.0, 1.0] │ [0, size-1] │
│ 过滤方式 │ 应用纹理过滤 │ 无过滤，直接读取 │
│ mipmap 支持 │ 自动选择级别 │ 手动指定级别 │
│ WebGL 版本 │ 1.0 + 2.0 │ 仅 2.0 │
│ 性能 │ 较慢(有插值) │ 较快(直接访问) │
│ 精度 │ 插值结果 │ 精确像素值 │
└─────────────┴─────────────────┴─────────────────┘

### 实际使用示例

```glsl
// 示例1：读取512x512纹理的中心像素
// texture2D方式
vec2 uv = vec2(0.5, 0.5);  // 归一化坐标
vec4 color1 = texture2D(u_texture, uv);

// texelFetch方式
ivec2 pixel = ivec2(256, 256);  // 像素坐标
vec4 color2 = texelFetch(u_texture, pixel, 0);

// 示例2：数据纹理精确读取
// texture2D - 可能有插值误差
vec2 dataUV = (vec2(index % size, index / size) + 0.5) / float(size);
vec4 data1 = texture2D(u_dataTexture, dataUV);

// texelFetch - 精确无误差
ivec2 dataCoord = ivec2(index % size, index / size);
vec4 data2 = texelFetch(u_dataTexture, dataCoord, 0);
```

### 使用场景选择

```glsl
// 使用 texture2D 的场景：
// ✓ 普通纹理渲染
// ✓ 需要过滤效果
// ✓ WebGL 1.0 兼容性
// ✓ 平滑插值采样

// 使用 texelFetch 的场景：
// ✓ 数据纹理读取
// ✓ 计算着色器
// ✓ 精确像素操作
// ✓ 避免插值误差
```

## 🔍 常见问题和解决方案

### 1. 纹理采样模糊

```glsl
// 问题：没有使用像素中心偏移
vec2 uv = gl_FragCoord.xy / u_resolution;

// 解决：添加像素中心偏移
vec2 uv = (gl_FragCoord.xy + 0.5) / u_resolution;
```

### 2. 数据纹理读取错误

```glsl
// 问题：整数坐标直接除以纹理尺寸
float index = 42.0;
vec2 uv = vec2(mod(index, size), floor(index / size)) / size;

// 解决：添加0.5偏移确保采样到像素中心
vec2 uv = (vec2(mod(index, size), floor(index / size)) + 0.5) / size;
```

## 🎨 实际代码示例

### 1. 完整的纹理采样函数

```javascript
// JavaScript端：创建带有正确设置的纹理
class PreciseTexture {
    constructor(gl, data, width, height) {
        this.texture = new Texture(gl, {
            image: data,
            width: width,
            height: height,
            format: gl.RGBA,
            type: gl.UNSIGNED_BYTE,
            flipY: true, // 自动处理Y轴翻转
            unpackAlignment: 4,
            minFilter: gl.NEAREST, // 精确采样
            magFilter: gl.NEAREST,
        });
    }

    // 计算精确的纹理坐标
    getPixelCenterUV(x, y) {
        return {
            u: (x + 0.5) / this.texture.width,
            v: (y + 0.5) / this.texture.height,
        };
    }
}
```

### 2. 着色器中的精确采样

```glsl
// 顶点着色器：传递精确的纹理坐标
attribute vec2 position;
attribute vec2 uv;

varying vec2 v_uv;
varying vec2 v_pixelCoord;

uniform vec2 u_resolution;

void main() {
    gl_Position = vec4(position, 0.0, 1.0);

    // 传递原始UV坐标
    v_uv = uv;

    // 计算像素坐标（用于精确采样）
    v_pixelCoord = uv * u_resolution;
}
```

```glsl
// 片段着色器：多种采样方式
precision mediump float;

uniform sampler2D u_texture;
uniform vec2 u_resolution;
uniform vec2 u_texelSize;  // 1.0 / textureSize

varying vec2 v_uv;
varying vec2 v_pixelCoord;

// 方法1：标准纹理采样
vec4 standardSample() {
    return texture2D(u_texture, v_uv);
}

// 方法2：像素中心精确采样
vec4 pixelCenterSample() {
    vec2 pixelCenter = floor(v_pixelCoord) + 0.5;
    vec2 uv = pixelCenter / u_resolution;
    return texture2D(u_texture, uv);
}

// 方法3：相邻像素采样
vec4 neighborSample(float offsetX, float offsetY) {
    vec2 pixelCoord = floor(v_pixelCoord) + vec2(offsetX, offsetY) + 0.5;
    vec2 uv = pixelCoord / u_resolution;
    return texture2D(u_texture, uv);
}

void main() {
    // 根据需要选择采样方式
    gl_FragColor = pixelCenterSample();

    // 或者进行多重采样
    vec4 center = pixelCenterSample();
    vec4 right = neighborSample(1.0, 0.0);
    vec4 up = neighborSample(0.0, 1.0);
    vec4 diagonal = neighborSample(1.0, 1.0);

    // 平均采样结果
    gl_FragColor = (center + right + up + diagonal) * 0.25;
}
```

## 📝 总结

1. **Y 坐标调整**：解决 WebGL 和图像坐标系的差异
2. **像素中心偏移**：确保采样到正确的像素数据
3. **+0.5 偏移**：将采样点从像素边界移动到像素中心
4. **精确采样**：对于数据纹理和计算着色器尤其重要
5. **性能考虑**：预计算偏移值，使用适当的采样函数

正确理解和应用这些概念，可以避免纹理采样中的常见问题，确保渲染结果的准确性。
