# WebGL 纹理数组使用示例详解

## 核心代码分析

```javascript
if (uniform.value.length && uniform.value[0].texture) {
    const textureUnits = []; // 存储分配的纹理单元索引数组

    // 为数组中的每个纹理分配独立的纹理单元
    uniform.value.forEach((textureObject) => {
        textureUnit = textureUnit + 1; // 递增纹理单元计数器
        textureObject.update(textureUnit); // 绑定纹理到 GL_TEXTURE0 + textureUnit
        textureUnits.push(textureUnit); // 记录纹理单元索引
    });

    // 最终结果示例：textureUnits = [1, 2, 3]
    // 对应 GL_TEXTURE1, GL_TEXTURE2, GL_TEXTURE3
    // 调用 gl.uniform1iv(location, [1, 2, 3])
    return setUniform(this.gl, activeUniform.type, location, textureUnits);
}
```

## 使用场景示例

### 1. 多材质渲染场景

**应用场景**: 一个 3D 模型需要同时使用多种材质纹理

```javascript
// 示例：渲染一个房屋模型，需要墙壁、屋顶、门窗等不同纹理
const houseTextures = [
    new Texture(gl, { image: wallTexture }), // 墙壁纹理
    new Texture(gl, { image: roofTexture }), // 屋顶纹理
    new Texture(gl, { image: doorTexture }), // 门纹理
    new Texture(gl, { image: windowTexture }), // 窗户纹理
];

// 在着色器中定义纹理数组uniform
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: `
        uniform sampler2D u_textures[4];  // 纹理数组
        uniform int u_materialIndex;      // 材质索引
        
        void main() {
            // 根据材质索引选择对应纹理
            vec4 color = texture2D(u_textures[u_materialIndex], vUv);
            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_textures: { value: houseTextures }, // 传入纹理数组
    },
});

// 执行结果：
// textureUnits = [1, 2, 3, 4]
// GL_TEXTURE1 绑定墙壁纹理
// GL_TEXTURE2 绑定屋顶纹理
// GL_TEXTURE3 绑定门纹理
// GL_TEXTURE4 绑定窗户纹理
```

### 2. 地形渲染混合纹理

**应用场景**: 地形渲染中需要混合多种地表纹理

```javascript
// 示例：地形系统需要草地、泥土、石头、雪地纹理
const terrainTextures = [
    new Texture(gl, { image: grassTexture }), // 草地
    new Texture(gl, { image: dirtTexture }), // 泥土
    new Texture(gl, { image: rockTexture }), // 石头
    new Texture(gl, { image: snowTexture }), // 雪地
];

const terrainProgram = new Program(gl, {
    vertex: terrainVertexShader,
    fragment: `
        uniform sampler2D u_terrainTextures[4];
        uniform sampler2D u_blendMap;  // 混合贴图
        
        void main() {
            vec4 blendFactors = texture2D(u_blendMap, vUv);
            
            vec4 grass = texture2D(u_terrainTextures[0], vUv * 16.0);
            vec4 dirt = texture2D(u_terrainTextures[1], vUv * 16.0);
            vec4 rock = texture2D(u_terrainTextures[2], vUv * 16.0);
            vec4 snow = texture2D(u_terrainTextures[3], vUv * 16.0);
            
            // 根据混合因子混合纹理
            gl_FragColor = grass * blendFactors.r + 
                          dirt * blendFactors.g + 
                          rock * blendFactors.b + 
                          snow * blendFactors.a;
        }
    `,
    uniforms: {
        u_terrainTextures: { value: terrainTextures },
    },
});

// 执行结果：
// textureUnits = [1, 2, 3, 4]
// 每个纹理单元绑定不同的地形纹理
```

### 3. 粒子系统纹理动画

**应用场景**: 粒子系统需要使用纹理序列实现动画效果

```javascript
// 示例：火焰粒子效果，使用8帧纹理序列
const flameFrames = [];
for (let i = 0; i < 8; i++) {
    flameFrames.push(
        new Texture(gl, {
            image: flameTextures[i],
        })
    );
}

const particleProgram = new Program(gl, {
    vertex: particleVertexShader,
    fragment: `
        uniform sampler2D u_flameFrames[8];
        uniform float u_time;
        
        void main() {
            // 根据时间计算当前帧
            int frameIndex = int(mod(u_time * 10.0, 8.0));
            
            vec4 color;
            // 使用条件语句选择纹理帧（WebGL 1.0限制）
            if(frameIndex == 0) color = texture2D(u_flameFrames[0], vUv);
            else if(frameIndex == 1) color = texture2D(u_flameFrames[1], vUv);
            // ... 其他帧
            
            gl_FragColor = color * vAlpha;
        }
    `,
    uniforms: {
        u_flameFrames: { value: flameFrames },
    },
});

// 执行结果：
// textureUnits = [1, 2, 3, 4, 5, 6, 7, 8]
// 8个纹理单元分别绑定火焰动画的8帧纹理
```

### 4. UI 系统图标纹理集

**应用场景**: UI 系统需要同时加载多个图标纹理

```javascript
// 示例：游戏UI需要同时显示多种图标
const uiIcons = [
    new Texture(gl, { image: healthIcon }), // 生命值图标
    new Texture(gl, { image: manaIcon }), // 魔法值图标
    new Texture(gl, { image: shieldIcon }), // 护盾图标
    new Texture(gl, { image: swordIcon }), // 武器图标
    new Texture(gl, { image: coinIcon }), // 金币图标
];

const uiProgram = new Program(gl, {
    vertex: uiVertexShader,
    fragment: `
        uniform sampler2D u_iconTextures[5];
        uniform int u_iconType;  // 图标类型索引

        void main() {
            vec4 iconColor;

            // 根据图标类型选择对应纹理
            if(u_iconType == 0) iconColor = texture2D(u_iconTextures[0], vUv);
            else if(u_iconType == 1) iconColor = texture2D(u_iconTextures[1], vUv);
            else if(u_iconType == 2) iconColor = texture2D(u_iconTextures[2], vUv);
            else if(u_iconType == 3) iconColor = texture2D(u_iconTextures[3], vUv);
            else iconColor = texture2D(u_iconTextures[4], vUv);

            gl_FragColor = iconColor;
        }
    `,
    uniforms: {
        u_iconTextures: { value: uiIcons },
    },
});

// 执行结果：
// textureUnits = [1, 2, 3, 4, 5]
// 5个纹理单元分别绑定不同的UI图标纹理
```

### 5. 后处理效果纹理链

**应用场景**: 后处理管线需要多个中间纹理进行效果叠加

```javascript
// 示例：后处理效果链，包含模糊、色调映射等效果
const postProcessTextures = [
    new Texture(gl, {
        width: canvas.width,
        height: canvas.height,
        format: gl.RGBA,
        type: gl.UNSIGNED_BYTE,
    }), // 原始场景纹理
    new Texture(gl, {
        width: canvas.width / 2,
        height: canvas.height / 2,
    }), // 降采样纹理
    new Texture(gl, {
        width: canvas.width / 2,
        height: canvas.height / 2,
    }), // 模糊纹理
];

const postProcessProgram = new Program(gl, {
    vertex: fullscreenVertexShader,
    fragment: `
        uniform sampler2D u_processTextures[3];
        uniform float u_bloomStrength;

        void main() {
            vec4 original = texture2D(u_processTextures[0], vUv);
            vec4 downsampled = texture2D(u_processTextures[1], vUv);
            vec4 blurred = texture2D(u_processTextures[2], vUv);

            // 混合原始图像和模糊效果实现泛光
            vec4 bloom = blurred * u_bloomStrength;
            gl_FragColor = original + bloom;
        }
    `,
    uniforms: {
        u_processTextures: { value: postProcessTextures },
    },
});

// 执行结果：
// textureUnits = [1, 2, 3]
// 3个纹理单元用于后处理效果链
```

## 技术要点总结

### 纹理单元分配机制

1. **递增分配**: `textureUnit = textureUnit + 1` 确保每个纹理获得独立的纹理单元
2. **绑定更新**: `textureObject.update(textureUnit)` 将纹理绑定到指定单元
3. **索引记录**: `textureUnits.push(textureUnit)` 记录分配的单元索引

### 着色器中的使用

```glsl
// 纹理数组声明
uniform sampler2D u_textures[4];

// 动态索引访问（WebGL 2.0）
vec4 color = texture2D(u_textures[dynamicIndex], vUv);

// 静态条件访问（WebGL 1.0兼容）
if(index == 0) color = texture2D(u_textures[0], vUv);
else if(index == 1) color = texture2D(u_textures[1], vUv);
```

### 性能考虑

-   **纹理单元限制**: 大多数设备支持 16-32 个纹理单元
-   **内存管理**: 纹理数组会占用更多 GPU 内存
-   **绑定开销**: 多纹理绑定会增加渲染调用开销

### 最佳实践

1. **合理规划**: 根据实际需求确定纹理数组大小
2. **纹理复用**: 相同纹理避免重复加载
3. **动态管理**: 根据场景动态加载/卸载纹理
4. **格式优化**: 使用合适的纹理格式和压缩

### 常见问题解决

#### 1. 纹理单元不足

```javascript
// 检查设备支持的最大纹理单元数
const maxTextureUnits = gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS);
console.log('最大纹理单元数:', maxTextureUnits);

// 动态调整纹理数组大小
const textureArray = textures.slice(0, Math.min(textures.length, maxTextureUnits - 1));
```

#### 2. WebGL 1.0 动态索引限制

```glsl
// 使用条件分支替代动态索引
vec4 getTextureColor(int index, vec2 uv) {
    if(index == 0) return texture2D(u_textures[0], uv);
    else if(index == 1) return texture2D(u_textures[1], uv);
    else if(index == 2) return texture2D(u_textures[2], uv);
    else return texture2D(u_textures[3], uv);
}
```

#### 3. 纹理加载顺序问题

```javascript
// 确保所有纹理加载完成后再使用
Promise.all(texturePromises).then((loadedTextures) => {
    const program = new Program(gl, {
        uniforms: {
            u_textures: { value: loadedTextures },
        },
    });
});
```
