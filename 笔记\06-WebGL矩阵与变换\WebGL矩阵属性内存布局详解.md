# WebGL 矩阵属性内存布局详解

## 概述

在 WebGL 中，矩阵属性（如 mat2、mat3、mat4）需要特殊处理，因为 WebGL 的顶点属性一次最多只能处理 4 个分量。因此，矩阵必须分解为多个 vec4 属性来传递给着色器。

## 核心计算公式

```javascript
const size = attr.size / numLoc;
const stride = numLoc === 1 ? 0 : numLoc * numLoc * 4;
const offset = numLoc === 1 ? 0 : numLoc * 4;
```

## 详细解释

### 1. size 计算：每个属性位置的分量数

**公式：** `size = attr.size / numLoc`

| 属性类型 | attr.size | numLoc | size | 说明                        |
| -------- | --------- | ------ | ---- | --------------------------- |
| vec3     | 3         | 1      | 3    | 普通向量，一个位置 3 个分量 |
| mat2     | 4         | 2      | 2    | 2x2 矩阵，分解为 2 个 vec2  |
| mat3     | 9         | 3      | 3    | 3x3 矩阵，分解为 3 个 vec3  |
| mat4     | 16        | 4      | 4    | 4x4 矩阵，分解为 4 个 vec4  |

### 2. stride 计算：顶点间距离

**公式：** `stride = numLoc === 1 ? 0 : numLoc * numLoc * 4`

| 属性类型 | numLoc | 计算过程 | stride | 说明                   |
| -------- | ------ | -------- | ------ | ---------------------- |
| vec3     | 1      | 0        | 0      | 使用原始 stride        |
| mat2     | 2      | 2×2×4    | 16     | 跳过 16 字节到下个顶点 |
| mat3     | 3      | 3×3×4    | 36     | 跳过 36 字节到下个顶点 |
| mat4     | 4      | 4×4×4    | 64     | 跳过 64 字节到下个顶点 |

### 3. offset 计算：列间偏移

**公式：** `offset = numLoc === 1 ? 0 : numLoc * 4`

| 属性类型 | numLoc | 计算过程 | offset | 说明             |
| -------- | ------ | -------- | ------ | ---------------- |
| vec3     | 1      | 0        | 0      | 无需偏移         |
| mat2     | 2      | 2×4      | 8      | 每列 8 字节偏移  |
| mat3     | 3      | 3×4      | 12     | 每列 12 字节偏移 |
| mat4     | 4      | 4×4      | 16     | 每列 16 字节偏移 |

## 内存布局可视化

### mat4 矩阵的内存布局示例

```
矩阵数据：
[m00 m01 m02 m03]
[m10 m11 m12 m13]
[m20 m21 m22 m23]
[m30 m31 m32 m33]

在缓冲区中的存储（列主序）：
顶点0: [m00,m10,m20,m30] [m01,m11,m21,m31] [m02,m12,m22,m32] [m03,m13,m23,m33]
       ↑ 位置0(offset=0)  ↑ 位置1(offset=16) ↑ 位置2(offset=32) ↑ 位置3(offset=48)

顶点1: [m00,m10,m20,m30] [m01,m11,m21,m31] [m02,m12,m22,m32] [m03,m13,m23,m33]
       ↑ stride=64字节后
```

### vertexAttribPointer 调用示例

对于 mat4 属性，会进行 4 次 vertexAttribPointer 调用：

```javascript
// 第0列 (i=0)
gl.vertexAttribPointer(location + 0, 4, gl.FLOAT, false, 64, 0);

// 第1列 (i=1)
gl.vertexAttribPointer(location + 1, 4, gl.FLOAT, false, 64, 16);

// 第2列 (i=2)
gl.vertexAttribPointer(location + 2, 4, gl.FLOAT, false, 64, 32);

// 第3列 (i=3)
gl.vertexAttribPointer(location + 3, 4, gl.FLOAT, false, 64, 48);
```

## 实际应用场景

### 场景 1：实例化渲染中的变换矩阵

```javascript
// 每个实例有一个4x4变换矩阵
const instanceMatrices = new Float32Array([
    // 实例0的矩阵 (16个float)
    1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1,
    // 实例1的矩阵 (16个float)
    1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 5, 0, 0, 1,
    // ...更多实例
]);

// 在着色器中声明
// attribute mat4 instanceMatrix; // 占用4个属性位置
```

### 场景 2：骨骼动画中的骨骼矩阵

```javascript
// 每个顶点关联多个骨骼矩阵
const boneMatrices = new Float32Array([
    // 骨骼0矩阵
    1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1,
    // 骨骼1矩阵
    1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1,
    // ...
]);
```

## 关键要点

1. **WebGL 限制**：单个顶点属性最多 4 个分量，矩阵必须分解
2. **列主序存储**：矩阵按列存储，每列作为一个 vec4
3. **连续内存**：所有矩阵数据在缓冲区中连续存储
4. **属性位置消耗**：mat4 消耗 4 个连续的属性位置
5. **字节对齐**：每个 float 占 4 字节，计算时需考虑字节大小

## 实际代码中的应用

在 OGL 框架的 Geometry.js 中，这些计算被用于 bindAttributes 方法：

```javascript
// 计算每个位置的大小、步长和偏移量
const size = attr.size / numLoc; // 每个属性位置的分量数
const stride = numLoc === 1 ? 0 : numLoc * numLoc * 4; // 顶点间距离
const offset = numLoc === 1 ? 0 : numLoc * 4; // 列间偏移

// 为每个位置设置属性指针
for (let i = 0; i < numLoc; i++) {
    this.gl.vertexAttribPointer(
        location + i, // 属性位置递增
        size, // 每个位置的分量数
        attr.type, // 数据类型
        attr.normalized, // 是否归一化
        attr.stride + stride, // 总步长
        attr.offset + i * offset // 总偏移量
    );
    this.gl.enableVertexAttribArray(location + i);
}
```

## 常见问题与解决方案

### 问题 1：属性位置不足

**现象**：mat4 属性无法正确绑定
**原因**：WebGL 有最大属性位置限制（通常 16 个）
**解决**：检查`gl.getParameter(gl.MAX_VERTEX_ATTRIBS)`

### 问题 2：矩阵数据错乱

**现象**：渲染结果不正确
**原因**：矩阵数据没有按列主序排列
**解决**：确保数据按[col0, col1, col2, col3]顺序存储

### 问题 3：实例化渲染失败

**现象**：只渲染一个实例
**原因**：忘记设置 vertexAttribDivisor
**解决**：为每个矩阵列设置 divisor 为 1

## 调试技巧

1. **检查属性位置**：确保有足够的属性位置可用
2. **验证数据对齐**：确保矩阵数据正确对齐
3. **监控内存使用**：大量矩阵数据可能消耗大量 GPU 内存
4. **测试不同矩阵大小**：mat2、mat3、mat4 的处理方式不同
5. **使用调试工具**：WebGL Inspector 等工具可以查看属性绑定状态

## 性能优化建议

1. **批量更新**：避免频繁更新矩阵数据
2. **使用 UBO**：对于大量矩阵，考虑使用 Uniform Buffer Objects
3. **数据压缩**：对于简单变换，可以只传递位置、旋转、缩放参数
4. **LOD 系统**：根据距离使用不同精度的矩阵
