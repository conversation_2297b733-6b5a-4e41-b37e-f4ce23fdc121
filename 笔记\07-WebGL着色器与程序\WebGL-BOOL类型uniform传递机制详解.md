# WebGL BOOL 类型 uniform 传递机制详解

## 核心问题确认

您选中的代码：

```javascript
case 35670: // BOOL - 布尔值
```

**您的理解完全正确！** WebGL 中的布尔值 uniform 确实是通过传递**0 和 1**来实现的。

## WebGL 布尔值处理机制

### 1. setUniform 中的实际处理

```javascript
// 在 setUniform 函数中，BOOL 类型的处理
switch (type) {
    case 35670: // BOOL - 布尔值
    case 5124: // INT - 整数
    case 35678: // SAMPLER_2D - 2D纹理采样器
        // 🔍 关键：布尔值和整数使用相同的 WebGL 函数
        return value.length ? gl.uniform1iv(location, value) : gl.uniform1i(location, value);
    //     ↑ uniform1i 专门用于设置整数值
    //       ↑ 布尔值被转换为整数：true → 1, false → 0
}
```

### 2. JavaScript 布尔值到 WebGL 整数的转换

```javascript
// JavaScript 端的布尔值处理
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: `
        uniform bool u_enableLighting;    // GLSL 中的布尔值
        uniform bool u_useTexture;        // GLSL 中的布尔值
        uniform bool u_showWireframe;     // GLSL 中的布尔值
        
        void main() {
            vec4 color = vec4(1.0);
            
            if(u_enableLighting) {        // GLSL 中的布尔判断
                // 执行光照计算
            }
            
            if(u_useTexture) {
                // 使用纹理
            }
            
            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_enableLighting: { value: true }, // JavaScript 布尔值
        u_useTexture: { value: false }, // JavaScript 布尔值
        u_showWireframe: { value: true }, // JavaScript 布尔值
    },
});

// 内部转换过程：
// JavaScript: true  → WebGL: gl.uniform1i(location, 1)
// JavaScript: false → WebGL: gl.uniform1i(location, 0)
```

## 布尔值转换的技术细节

### 1. JavaScript 到 WebGL 的转换规则

```javascript
// 布尔值转换函数（内部逻辑）
function convertBooleanToInt(value) {
    // JavaScript 的真值转换规则
    if (typeof value === 'boolean') {
        return value ? 1 : 0; // true → 1, false → 0
    }

    // 其他类型的真值判断
    return value ? 1 : 0;
    // 示例：
    // 1, "hello", [], {} → 1
    // 0, "", null, undefined → 0
}

// 实际使用示例
const booleanValues = {
    // 明确的布尔值
    enabled: true, // → 1
    disabled: false, // → 0

    // 真值转换
    nonZero: 42, // → 1
    zero: 0, // → 0
    string: 'hello', // → 1
    emptyString: '', // → 0
    object: {}, // → 1
    nullValue: null, // → 0
};
```

### 2. GLSL 中的布尔值处理

```glsl
// GLSL 着色器中的布尔值使用
uniform bool u_enableFeature;  // 接收 0 或 1

void main() {
    // 方式1：直接条件判断
    if(u_enableFeature) {
        // 当 u_enableFeature = 1 时执行
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);  // 红色
    } else {
        // 当 u_enableFeature = 0 时执行
        gl_FragColor = vec4(0.0, 1.0, 0.0, 1.0);  // 绿色
    }

    // 方式2：作为乘法因子
    vec3 baseColor = vec3(1.0, 1.0, 1.0);
    vec3 featureColor = vec3(1.0, 0.0, 0.0);

    // u_enableFeature 为 1 时混合，为 0 时不混合
    vec3 finalColor = baseColor + featureColor * float(u_enableFeature);

    gl_FragColor = vec4(finalColor, 1.0);
}
```

## 实际应用场景

### 1. 渲染特性开关

```javascript
// 材质系统中的特性开关
const materialProgram = new Program(gl, {
    fragment: `
        uniform bool u_enableNormalMapping;   // 法线贴图开关
        uniform bool u_enableSpecular;        // 镜面反射开关
        uniform bool u_enableShadows;         // 阴影开关
        uniform bool u_enableFog;             // 雾效开关
        
        uniform sampler2D u_diffuseMap;
        uniform sampler2D u_normalMap;
        
        void main() {
            vec4 diffuse = texture2D(u_diffuseMap, vUv);
            vec3 normal = vNormal;
            
            // 条件性启用法线贴图
            if(u_enableNormalMapping) {
                vec3 normalMap = texture2D(u_normalMap, vUv).rgb * 2.0 - 1.0;
                normal = normalize(vTangent * normalMap.x + 
                                 vBitangent * normalMap.y + 
                                 vNormal * normalMap.z);
            }
            
            vec3 color = diffuse.rgb;
            
            // 条件性启用镜面反射
            if(u_enableSpecular) {
                vec3 viewDir = normalize(vViewPosition);
                vec3 reflectDir = reflect(-vLightDirection, normal);
                float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
                color += vec3(spec);
            }
            
            // 条件性启用雾效
            if(u_enableFog) {
                float fogFactor = exp(-vDistance * 0.01);
                color = mix(vec3(0.5, 0.5, 0.5), color, fogFactor);
            }
            
            gl_FragColor = vec4(color, diffuse.a);
        }
    `,
    uniforms: {
        u_enableNormalMapping: { value: true }, // → gl.uniform1i(location, 1)
        u_enableSpecular: { value: false }, // → gl.uniform1i(location, 0)
        u_enableShadows: { value: true }, // → gl.uniform1i(location, 1)
        u_enableFog: { value: false }, // → gl.uniform1i(location, 0)
    },
});

// 动态切换特性
function toggleFeature(featureName, enabled) {
    materialProgram.uniforms[featureName].value = enabled;
    // 内部会调用：gl.uniform1i(location, enabled ? 1 : 0)
}

// 使用示例
toggleFeature('u_enableSpecular', true); // 启用镜面反射
toggleFeature('u_enableFog', false); // 禁用雾效
```

### 2. 调试和可视化开关

```javascript
// 调试渲染模式
const debugProgram = new Program(gl, {
    fragment: `
        uniform bool u_showNormals;      // 显示法线
        uniform bool u_showUVs;          // 显示UV坐标
        uniform bool u_showWireframe;    // 显示线框
        uniform bool u_showDepth;        // 显示深度
        
        void main() {
            vec4 color = vec4(1.0);
            
            if(u_showNormals) {
                // 将法线可视化为颜色
                color.rgb = normalize(vNormal) * 0.5 + 0.5;
            } else if(u_showUVs) {
                // 将UV坐标可视化为颜色
                color.rgb = vec3(vUv, 0.0);
            } else if(u_showDepth) {
                // 将深度可视化为灰度
                float depth = gl_FragCoord.z;
                color.rgb = vec3(depth);
            } else {
                // 正常渲染
                color = texture2D(u_diffuseMap, vUv);
            }
            
            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_showNormals: { value: false }, // → gl.uniform1i(location, 0)
        u_showUVs: { value: false }, // → gl.uniform1i(location, 0)
        u_showWireframe: { value: false }, // → gl.uniform1i(location, 0)
        u_showDepth: { value: false }, // → gl.uniform1i(location, 0)
    },
});

// 调试控制面板
class DebugPanel {
    constructor(program) {
        this.program = program;
        this.createControls();
    }

    createControls() {
        // 创建复选框控件
        const controls = {
            'Show Normals': 'u_showNormals',
            'Show UVs': 'u_showUVs',
            'Show Wireframe': 'u_showWireframe',
            'Show Depth': 'u_showDepth',
        };

        Object.entries(controls).forEach(([label, uniformName]) => {
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.addEventListener('change', (e) => {
                this.program.uniforms[uniformName].value = e.target.checked;
                // 内部转换：checked (boolean) → 1 或 0 (integer)
            });

            const labelElement = document.createElement('label');
            labelElement.textContent = label;
            labelElement.appendChild(checkbox);

            document.body.appendChild(labelElement);
        });
    }
}
```

### 3. 布尔向量的使用

```javascript
// 多个布尔值组合
const multiFeatureProgram = new Program(gl, {
    fragment: `
        uniform bvec3 u_enabledFeatures;  // 布尔向量：[lighting, texture, fog]
        
        void main() {
            vec4 color = vec4(1.0);
            
            // 访问布尔向量的各个分量
            if(u_enabledFeatures.x) {  // lighting
                // 光照计算
                color.rgb *= 0.8;
            }
            
            if(u_enabledFeatures.y) {  // texture
                // 纹理采样
                color *= texture2D(u_diffuseMap, vUv);
            }
            
            if(u_enabledFeatures.z) {  // fog
                // 雾效计算
                float fogFactor = exp(-vDistance * 0.01);
                color.rgb = mix(vec3(0.5), color.rgb, fogFactor);
            }
            
            gl_FragColor = color;
        }
    `,
    uniforms: {
        // 布尔向量：[lighting, texture, fog]
        u_enabledFeatures: { value: [true, false, true] },
        // 内部转换：[true, false, true] → [1, 0, 1]
        // 调用：gl.uniform3iv(location, [1, 0, 1])
    },
});
```

## 技术原理深入分析

### 1. 为什么 WebGL 使用整数表示布尔值？

#### GPU 硬件架构限制

```
GPU 数据类型支持：
┌─────────────────────────────────────┐
│ GPU 原生支持的数据类型               │
├─────────────────────────────────────┤
│ • 32位浮点数 (float)                │
│ • 32位整数 (int)                    │
│ • 16位半精度浮点 (half, 部分GPU)     │
└─────────────────────────────────────┘

GPU 不直接支持：
❌ 独立的布尔类型 (bool)
❌ 8位字节类型 (byte)
❌ 字符串类型 (string)
```

#### WebGL API 设计考虑

```javascript
// WebGL 统一使用整数函数处理布尔值
gl.uniform1i(location, boolValue ? 1 : 0); // 布尔值
gl.uniform1i(location, intValue); // 整数值
gl.uniform1i(location, textureUnit); // 纹理单元索引

// 这种设计的优势：
// 1. API 简化：减少函数数量
// 2. 类型统一：整数和布尔值使用相同处理路径
// 3. 性能优化：避免额外的类型转换开销
```

### 2. 布尔值转换的详细过程

```javascript
// Program.js 中的布尔值处理流程
function processBooleanUniform(uniform) {
    let value = uniform.value;

    // 第一步：JavaScript 真值转换
    if (typeof value === 'boolean') {
        value = value ? 1 : 0;  // true → 1, false → 0
    } else {
        // 其他类型按 JavaScript 真值规则转换
        value = value ? 1 : 0;
    }

    // 第二步：调用 WebGL API
    gl.uniform1i(location, value);

    return value;
}

// 转换示例表
const conversionExamples = {
    // 明确布尔值
    true: 1,
    false: 0,

    // 数字类型
    1: 1,
    0: 0,
    42: 1,
    -1: 1,
    0.0: 0,
    0.1: 1,

    // 字符串类型
    "true": 1,
    "false": 1,  // 注意：非空字符串都是真值
    "": 0,
    " ": 1,

    // 对象类型
    {}: 1,
    []: 1,
    null: 0,
    undefined: 0,

    // 函数类型
    function() {}: 1
};
```

### 3. GLSL 中的布尔值运算

```glsl
// GLSL 着色器中的布尔值操作
uniform bool u_flag1;  // 接收 0 或 1
uniform bool u_flag2;  // 接收 0 或 1

void main() {
    // 布尔运算
    bool result1 = u_flag1 && u_flag2;  // 逻辑与
    bool result2 = u_flag1 || u_flag2;  // 逻辑或
    bool result3 = !u_flag1;            // 逻辑非

    // 条件表达式
    vec3 color = u_flag1 ? vec3(1.0, 0.0, 0.0) : vec3(0.0, 1.0, 0.0);

    // 数学运算中的使用
    float factor = float(u_flag1);  // 布尔值转浮点数：0.0 或 1.0
    vec3 finalColor = baseColor * factor;  // 条件性应用颜色

    gl_FragColor = vec4(finalColor, 1.0);
}
```

### 4. 性能考虑和最佳实践

#### 性能优化技巧

```javascript
// ✅ 高效：使用布尔值进行条件渲染
const program = new Program(gl, {
    fragment: `
        uniform bool u_enableExpensiveEffect;

        void main() {
            vec4 color = texture2D(u_baseTexture, vUv);

            // 条件性执行昂贵的计算
            if(u_enableExpensiveEffect) {
                // 复杂的后处理效果
                color = applyExpensiveEffect(color);
            }

            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_enableExpensiveEffect: { value: false }, // 默认禁用
    },
});

// 动态控制性能
function setQualityLevel(level) {
    const enableExpensive = level > 2; // 高质量时启用
    program.uniforms.u_enableExpensiveEffect.value = enableExpensive;
}
```

#### 避免分支发散

```glsl
// ❌ 可能导致性能问题的分支
uniform bool u_useComplexShading;

void main() {
    if(u_useComplexShading) {
        // 复杂着色分支
        gl_FragColor = complexShading();
    } else {
        // 简单着色分支
        gl_FragColor = simpleShading();
    }
}

// ✅ 更好的方法：使用混合
uniform float u_shadingComplexity;  // 0.0 到 1.0

void main() {
    vec4 simpleResult = simpleShading();
    vec4 complexResult = complexShading();

    // 线性混合，避免分支
    gl_FragColor = mix(simpleResult, complexResult, u_shadingComplexity);
}
```

### 5. 调试和验证

```javascript
// 布尔值 uniform 调试工具
class BooleanUniformDebugger {
    static validateBooleanUniforms(program) {
        console.log('🔍 布尔值 Uniform 状态检查:');

        Object.entries(program.uniforms).forEach(([name, uniform]) => {
            if (this.isBooleanUniform(name, uniform)) {
                const jsValue = uniform.value;
                const glValue = jsValue ? 1 : 0;

                console.log(`${name}:`, {
                    JavaScript值: jsValue,
                    WebGL值: glValue,
                    类型: typeof jsValue,
                    真值: Boolean(jsValue),
                });
            }
        });
    }

    static isBooleanUniform(name, uniform) {
        // 通过命名约定判断是否为布尔值
        return name.includes('enable') || name.includes('use') || name.includes('show') || name.includes('is') || typeof uniform.value === 'boolean';
    }

    static trackBooleanChanges(program) {
        Object.entries(program.uniforms).forEach(([name, uniform]) => {
            if (this.isBooleanUniform(name, uniform)) {
                let previousValue = uniform.value;

                // 监听值变化
                Object.defineProperty(uniform, 'value', {
                    get() {
                        return this._value;
                    },
                    set(newValue) {
                        if (newValue !== previousValue) {
                            console.log(`🔄 ${name} 变化:`, {
                                从: previousValue,
                                到: newValue,
                                WebGL值: newValue ? 1 : 0,
                            });
                            previousValue = newValue;
                        }
                        this._value = newValue;
                    },
                });

                uniform._value = uniform.value;
            }
        });
    }
}

// 使用示例
BooleanUniformDebugger.validateBooleanUniforms(program);
BooleanUniformDebugger.trackBooleanChanges(program);
```

## 总结

WebGL 中 BOOL 类型 uniform 传递 0 和 1 的设计体现了：

1. **硬件适配**：符合 GPU 原生数据类型支持
2. **API 简化**：统一使用整数函数处理
3. **性能优化**：避免额外的类型转换开销
4. **兼容性**：确保跨平台的一致性行为

这种设计让开发者可以在 JavaScript 中自然地使用布尔值，而底层自动处理到 WebGL 整数的转换，既保持了易用性又确保了性能。
