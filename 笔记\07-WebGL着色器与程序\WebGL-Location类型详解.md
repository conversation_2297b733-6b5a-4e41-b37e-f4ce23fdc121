# WebGL Location 类型详解

## 核心概念：两种完全不同的 Location

WebGL 中存在两种本质不同的 location 类型，它们的设计目的、工作机制和性能特性都截然不同。

### 快速对比

| 特性         | 顶点属性 Location        | Uniform Location            |
| ------------ | ------------------------ | --------------------------- |
| **返回类型** | `number` (0,1,2...)      | `WebGLUniformLocation` 对象 |
| **获取方式** | `gl.getAttribLocation()` | `gl.getUniformLocation()`   |
| **本质**     | 配置表索引               | GPU 内存地址句柄            |
| **数据访问** | 间接访问（配置+计算）    | 直接访问（读写内存）        |
| **数据特点** | 大量重复，结构化         | 少量全局，频繁更新          |
| **性能策略** | 配置一次，GPU 并行计算   | 直接存储，快速读写          |

## 第一部分：顶点属性 Location

### 1.1 基本概念

顶点属性 Location 是 GPU 硬件中**顶点属性槽位的编号**，用于配置如何从 VBO 中读取顶点数据。

```javascript
// 获取顶点属性location
const positionLocation = gl.getAttribLocation(program, 'a_position'); // 返回: 0, 1, 2...
const normalLocation = gl.getAttribLocation(program, 'a_normal'); // 返回: 0, 1, 2...
const uvLocation = gl.getAttribLocation(program, 'a_texCoord'); // 返回: 0, 1, 2...

console.log(typeof positionLocation); // "number"
```

### 1.2 工作机制：配置表系统

```javascript
// GPU 内部的顶点属性系统（概念性实现）
class VertexAttributeSystem {
    constructor() {
        // location 是这个配置表的索引
        this.attributeConfigs = new Array(MAX_VERTEX_ATTRIBS); // 通常16个槽位
        this.enabledAttributes = new Set();
    }

    // 配置 location 槽位的数据访问方式
    vertexAttribPointer(location, size, type, normalized, stride, offset) {
        this.attributeConfigs[location] = {
            bufferID: this.currentArrayBuffer, // 当前绑定的VBO
            size: size, // 分量数量
            type: type, // 数据类型
            normalized: normalized, // 是否归一化
            stride: stride, // 步长
            offset: offset, // 偏移量
            enabled: false,
        };
    }

    // 启用属性槽位
    enableVertexAttribArray(location) {
        this.attributeConfigs[location].enabled = true;
        this.enabledAttributes.add(location);
    }

    // 运行时为特定顶点计算数据（GPU并行执行）
    getVertexData(location, vertexIndex) {
        const config = this.attributeConfigs[location];
        if (!config.enabled) return null;

        // 关键：根据配置计算实际内存地址
        const buffer = this.buffers[config.bufferID];
        const actualAddress = buffer.baseAddress + vertexIndex * config.stride + config.offset;

        // 从计算出的地址读取数据
        return this.readGPUMemory(actualAddress, config.size, config.type);
    }
}
```

### 1.3 实际使用流程

```javascript
// 第1步：创建和填充VBO
const positionBuffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
gl.bufferData(gl.ARRAY_BUFFER, positionData, gl.STATIC_DRAW);

// 第2步：配置location槽位（改变"指向"）
gl.vertexAttribPointer(
    positionLocation, // 槽位编号（固定）
    3, // 每个顶点3个分量
    gl.FLOAT, // 数据类型
    false, // 不归一化
    12, // 步长：3 * 4字节
    0 // 偏移量：从头开始
);

// 第3步：启用槽位
gl.enableVertexAttribArray(positionLocation);

// 绘制时：GPU自动为每个顶点计算正确的数据位置
gl.drawArrays(gl.TRIANGLES, 0, vertexCount);
```

### 1.4 为什么使用配置方式？

#### 内存效率

```javascript
// 场景：渲染100万个顶点
// 如果直接存储每个顶点的数据引用：
// 100万个指针 * 8字节 = 8MB 仅用于存储指针

// 使用配置方式：
// 1个配置结构 * 64字节 = 64字节
// GPU运行时计算，内存占用减少99.999%
```

#### 并行计算优势

```javascript
// GPU可以并行为多个顶点计算数据位置
function parallelVertexProcessing() {
    // 同时处理多个顶点（GPU硬件并行）
    parallel_for (vertexID = 0; vertexID < vertexCount; vertexID++) {
        // 每个并行单元独立计算数据地址
        const positionData = calculateVertexData(positionLocation, vertexID);
        const normalData = calculateVertexData(normalLocation, vertexID);

        // 执行顶点着色器
        runVertexShader(vertexID, positionData, normalData);
    }
}
```

## 第二部分：Uniform Location

### 2.1 基本概念

Uniform Location 是指向 GPU 内存中**特定位置的句柄**，用于直接读写全局共享的数据。

```javascript
// 获取uniform location
const timeLocation = gl.getUniformLocation(program, 'u_time');
const matrixLocation = gl.getUniformLocation(program, 'u_modelMatrix');
const colorLocation = gl.getUniformLocation(program, 'u_color');

console.log(typeof timeLocation); // "object"
console.log(timeLocation instanceof WebGLUniformLocation); // true
```

### 2.2 工作机制：直接内存访问

```javascript
// GPU 内部的 uniform 系统（概念性实现）
class UniformSystem {
    constructor() {
        // 预分配的uniform内存区域
        this.uniformMemory = new ArrayBuffer(UNIFORM_BUFFER_SIZE);
        this.uniformLocations = new Map(); // WebGLUniformLocation -> 内存偏移
        this.nextOffset = 0;
    }

    // 程序链接时分配内存位置
    allocateUniformLocation(name, type, size) {
        const location = new WebGLUniformLocation();
        const typeSize = this.getTypeSize(type);
        const totalSize = typeSize * size;

        // 分配内存位置
        this.uniformLocations.set(location, {
            offset: this.nextOffset,
            size: totalSize,
            type: type,
        });

        this.nextOffset += totalSize;
        return location;
    }

    // 直接写入数据到内存位置
    uniform1f(location, value) {
        const info = this.uniformLocations.get(location);
        const view = new Float32Array(this.uniformMemory, info.offset, 1);
        view[0] = value; // 直接内存写入
    }

    uniform3fv(location, value) {
        const info = this.uniformLocations.get(location);
        const view = new Float32Array(this.uniformMemory, info.offset, 3);
        view.set(value); // 直接内存写入
    }

    // 着色器执行时直接读取
    getUniformValue(location) {
        const info = this.uniformLocations.get(location);
        const view = new Float32Array(this.uniformMemory, info.offset, info.size / 4);
        return view; // 直接内存读取
    }
}
```

### 2.3 实际使用流程

```javascript
// 直接设置uniform值（数据传输）
gl.uniform1f(timeLocation, currentTime); // 写入float
gl.uniform3fv(colorLocation, [1.0, 0.5, 0.2]); // 写入vec3
gl.uniformMatrix4fv(matrixLocation, false, modelMatrix); // 写入mat4

// 着色器执行时，直接从固定内存位置读取值
// 无需计算，无需配置，直接访问
```

### 2.4 为什么使用直接存储？

#### 访问效率

```javascript
// Uniform数据特点：
// - 数量少（通常几十个）
// - 全局共享（所有顶点/片元使用相同值）
// - 频繁更新（每帧可能改变）

// 直接存储的优势：
// 1. 零计算开销：直接内存读写
// 2. 缓存友好：数据紧密排列
// 3. 更新高效：一次写入，多次读取
```

#### 内存占用合理

```javascript
// 典型场景的uniform内存占用：
const typicalUniforms = {
    time: 4, // 4字节
    viewMatrix: 64, // 64字节
    projectionMatrix: 64, // 64字节
    lightPosition: 12, // 12字节
    lightColor: 12, // 12字节
    materialProperties: 48, // 48字节
    // 总计约200字节
};

// 相比顶点数据（可能几MB），uniform数据很小
// 直接存储不会造成内存压力
```

## 第三部分：深度对比分析

### 3.1 数据访问模式差异

```javascript
// 顶点属性：一对多访问模式
// 一个配置 -> 多个顶点数据
gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 12, 0);
// GPU为每个顶点计算：
// 顶点0: buffer[0, 1, 2]
// 顶点1: buffer[3, 4, 5]
// 顶点2: buffer[6, 7, 8]
// ...

// Uniform：一对一访问模式
// 一个位置 -> 一个全局值
gl.uniform3f(colorLocation, 1.0, 0.5, 0.2);
// 所有着色器调用都读取相同的值: [1.0, 0.5, 0.2]
```

### 3.2 性能特性对比

```javascript
// 顶点属性性能特点：
// ✅ 内存占用极小（配置 < 1KB）
// ✅ 支持大量数据（GB级别的顶点数据）
// ✅ GPU并行计算友好
// ⚠️ 每次访问需要地址计算（但GPU并行，影响微小）

// Uniform性能特点：
// ✅ 访问速度极快（直接内存读写）
// ✅ 更新效率高（一次写入）
// ✅ 缓存友好（数据紧密排列）
// ⚠️ 内存占用相对较大（但总量仍很小）
```

### 3.3 使用场景对比

```javascript
// 顶点属性适用场景：
// - 顶点位置、法线、纹理坐标
// - 顶点颜色、切线向量
// - 实例化数据（每个实例不同）
// - 任何"每个顶点不同"的数据

// Uniform适用场景：
// - 变换矩阵（模型、视图、投影）
// - 光照参数（光源位置、颜色、强度）
// - 材质属性（漫反射、镜面反射系数）
// - 时间、屏幕分辨率等全局参数
// - 任何"全局共享"的数据
```

## 第四部分：实际代码示例

### 4.1 OGL 框架中的实现

```javascript
// src/core/Program.js 中的不同处理方式

// 顶点属性location处理
this.attributeLocations = new Map();
const numAttribs = this.gl.getProgramParameter(this.program, this.gl.ACTIVE_ATTRIBUTES);
for (let aIndex = 0; aIndex < numAttribs; aIndex++) {
    const attribute = this.gl.getActiveAttrib(this.program, aIndex);
    const location = this.gl.getAttribLocation(this.program, attribute.name);
    // location 是简单的数字
    this.attributeLocations.set(attribute, location);
}

// Uniform location处理
this.uniformLocations = new Map();
let numUniforms = this.gl.getProgramParameter(this.program, this.gl.ACTIVE_UNIFORMS);
for (let uIndex = 0; uIndex < numUniforms; uIndex++) {
    let uniform = this.gl.getActiveUniform(this.program, uIndex);
    // location 是WebGLUniformLocation对象
    this.uniformLocations.set(uniform, this.gl.getUniformLocation(this.program, uniform.name));
}
```

### 4.2 渲染时的不同调用

```javascript
// 顶点属性：配置一次，GPU自动处理
geometry.draw({ program }) {
    // 绑定VAO（包含所有顶点属性配置）
    this.gl.renderer.bindVertexArray(this.VAOs[program.attributeOrder]);

    // 绘制：GPU根据配置自动为每个顶点获取数据
    this.gl.drawElements(mode, this.drawRange.count, this.glType, this.drawRange.start);
}

// Uniform：每次直接设置值
program.use() {
    this.uniformLocations.forEach((location, activeUniform) => {
        // 直接向GPU内存位置写入数据
        setUniform(this.gl, activeUniform.type, location, uniform.value);
    });
}
```

## 总结

WebGL 的两种 location 类型体现了不同数据特性的优化策略：

1. **顶点属性 Location**：配置表索引，适用于大量重复的结构化数据
2. **Uniform Location**：内存地址句柄，适用于少量全局的共享数据

这种设计充分利用了 GPU 硬件特性，在内存效率和访问性能之间达到了最佳平衡。理解这两种 location 的区别，有助于更好地进行 WebGL 性能优化和架构设计。

## 第五部分：常见误解和澄清

### 5.1 误解：Location 都是指针

**错误理解**：

```javascript
// ❌ 错误认知
'所有 location 都是指向 GPU 内存的指针';
```

**正确理解**：

```javascript
// ✅ 正确认知
const vertexLocation = gl.getAttribLocation(program, 'position'); // 数字索引，不是指针
const uniformLocation = gl.getUniformLocation(program, 'time'); // 内存句柄，类似指针

// 顶点属性 location 是配置表的索引号
// Uniform location 才是真正的内存位置句柄
```

### 5.2 误解：可以直接操作 Location

**错误理解**：

```javascript
// ❌ 错误做法
const location = gl.getAttribLocation(program, 'position');
location = location + 1; // 试图修改 location
```

**正确理解**：

```javascript
// ✅ 正确做法
const location = gl.getAttribLocation(program, 'position');
// location 是只读的，由 WebGL 分配
// 只能通过 WebGL API 来配置或使用 location
gl.vertexAttribPointer(location, 3, gl.FLOAT, false, 12, 0);
```

### 5.3 误解：两种 Location 可以互换使用

**错误理解**：

```javascript
// ❌ 错误做法
const vertexLoc = gl.getAttribLocation(program, 'position');
const uniformLoc = gl.getUniformLocation(program, 'time');

gl.uniform1f(vertexLoc, 1.0); // 类型错误！
gl.vertexAttribPointer(uniformLoc, 3, gl.FLOAT, false, 12, 0); // 类型错误！
```

**正确理解**：

```javascript
// ✅ 正确做法
const vertexLoc = gl.getAttribLocation(program, 'position'); // number
const uniformLoc = gl.getUniformLocation(program, 'time'); // WebGLUniformLocation

gl.vertexAttribPointer(vertexLoc, 3, gl.FLOAT, false, 12, 0); // 顶点属性配置
gl.uniform1f(uniformLoc, 1.0); // Uniform 数据设置
```

## 第六部分：性能优化建议

### 6.1 顶点属性优化

```javascript
// ✅ 优化：使用 VAO 缓存顶点属性配置
class OptimizedGeometry {
    constructor(gl) {
        this.gl = gl;
        this.VAOs = {}; // 为不同程序缓存 VAO
    }

    createVAO(program) {
        const vao = this.gl.createVertexArray();
        this.gl.bindVertexArray(vao);

        // 配置所有顶点属性（一次性）
        this.setupVertexAttributes(program);

        this.gl.bindVertexArray(null);
        this.VAOs[program.id] = vao;
        return vao;
    }

    draw(program) {
        // 快速切换到预配置的 VAO
        if (!this.VAOs[program.id]) {
            this.createVAO(program);
        }
        this.gl.bindVertexArray(this.VAOs[program.id]);
        this.gl.drawElements(this.gl.TRIANGLES, this.indexCount, this.gl.UNSIGNED_SHORT, 0);
    }
}

// ❌ 低效：每次都重新配置顶点属性
function inefficientDraw(gl, program, geometry) {
    // 每次绘制都重新配置，性能差
    gl.bindBuffer(gl.ARRAY_BUFFER, geometry.positionBuffer);
    gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 12, 0);
    gl.enableVertexAttribArray(0);

    gl.bindBuffer(gl.ARRAY_BUFFER, geometry.normalBuffer);
    gl.vertexAttribPointer(1, 3, gl.FLOAT, false, 12, 0);
    gl.enableVertexAttribArray(1);

    gl.drawElements(gl.TRIANGLES, geometry.indexCount, gl.UNSIGNED_SHORT, 0);
}
```

### 6.2 Uniform 优化

```javascript
// ✅ 优化：缓存 Uniform 值，避免重复设置
class OptimizedProgram {
    constructor(gl, shaders) {
        this.gl = gl;
        this.program = this.createProgram(shaders);
        this.uniformCache = new Map(); // 缓存当前 uniform 值
        this.uniformLocations = new Map();
    }

    setUniform(name, value) {
        const location = this.uniformLocations.get(name);
        const cachedValue = this.uniformCache.get(location);

        // 只在值实际改变时才调用 WebGL API
        if (!this.valuesEqual(cachedValue, value)) {
            this.gl.uniform1f(location, value);
            this.uniformCache.set(location, value);
        }
    }

    valuesEqual(a, b) {
        if (Array.isArray(a) && Array.isArray(b)) {
            return a.length === b.length && a.every((val, i) => val === b[i]);
        }
        return a === b;
    }
}

// ❌ 低效：重复设置相同的 uniform 值
function inefficientUniformUpdate(gl, program, time) {
    // 即使值没变也重复调用，浪费性能
    gl.uniform1f(program.timeLocation, time);
    gl.uniform1f(program.timeLocation, time); // 重复调用
    gl.uniform1f(program.timeLocation, time); // 重复调用
}
```

### 6.3 内存布局优化

```javascript
// ✅ 优化：交错存储顶点数据
const interleavedData = new Float32Array([
    // 顶点0: position(3) + normal(3) + uv(2)
    x0, y0, z0, nx0, ny0, nz0, u0, v0,
    // 顶点1: position(3) + normal(3) + uv(2)
    x1, y1, z1, nx1, ny1, nz1, u1, v1,
    // ...
]);

// 配置交错属性
gl.bindBuffer(gl.ARRAY_BUFFER, interleavedBuffer);
gl.vertexAttribPointer(0, 3, gl.FLOAT, false, 32, 0);  // position, offset=0
gl.vertexAttribPointer(1, 3, gl.FLOAT, false, 32, 12); // normal, offset=12
gl.vertexAttribPointer(2, 2, gl.FLOAT, false, 32, 24); // uv, offset=24

// ❌ 低效：分离存储（可能导致缓存未命中）
const separatedPositions = new Float32Array([x0, y0, z0, x1, y1, z1, ...]);
const separatedNormals = new Float32Array([nx0, ny0, nz0, nx1, ny1, nz1, ...]);
const separatedUVs = new Float32Array([u0, v0, u1, v1, ...]);
```

## 第七部分：调试和故障排除

### 7.1 常见错误诊断

```javascript
// 错误1：Location 为 -1
const location = gl.getAttribLocation(program, 'a_position');
if (location === -1) {
    console.error('属性未找到，可能原因：');
    console.error('1. 着色器中未声明该属性');
    console.error('2. 属性被链接器优化掉（未使用）');
    console.error('3. 属性名称拼写错误');
}

// 错误2：Uniform Location 为 null
const uniformLoc = gl.getUniformLocation(program, 'u_time');
if (uniformLoc === null) {
    console.error('Uniform 未找到，可能原因：');
    console.error('1. 着色器中未声明该 uniform');
    console.error('2. Uniform 被链接器优化掉（未使用）');
    console.error('3. Uniform 名称拼写错误');
}

// 错误3：类型不匹配
try {
    gl.uniform1f(location, value);
} catch (error) {
    console.error('Uniform 设置失败，检查：');
    console.error('1. location 是否为有效的 WebGLUniformLocation');
    console.error('2. 值的类型是否与着色器中声明的类型匹配');
    console.error('3. 程序是否已正确链接');
}
```

### 7.2 性能分析工具

```javascript
// 性能监控：统计 uniform 更新次数
class UniformProfiler {
    constructor() {
        this.updateCounts = new Map();
        this.redundantUpdates = 0;
    }

    trackUniformUpdate(location, value, cached) {
        const count = this.updateCounts.get(location) || 0;
        this.updateCounts.set(location, count + 1);

        if (cached) {
            this.redundantUpdates++;
        }
    }

    getReport() {
        return {
            totalUpdates: Array.from(this.updateCounts.values()).reduce((a, b) => a + b, 0),
            redundantUpdates: this.redundantUpdates,
            efficiency: 1 - this.redundantUpdates / this.totalUpdates,
        };
    }
}

// 使用示例
const profiler = new UniformProfiler();
// 在 uniform 更新时调用 profiler.trackUniformUpdate()
```

## 第八部分：最佳实践总结

### 8.1 设计原则

1. **顶点属性**：

    - 使用 VAO 缓存配置
    - 优先使用交错数据布局
    - 避免频繁的属性重配置

2. **Uniform**：

    - 实现值缓存机制
    - 批量更新相关的 uniform
    - 使用 Uniform Buffer Objects（WebGL2）

3. **通用原则**：
    - 理解两种 location 的本质区别
    - 根据数据特性选择合适的存储方式
    - 持续监控和优化性能

### 8.2 代码模板

```javascript
// 完整的 Location 管理模板
class WebGLLocationManager {
    constructor(gl, program) {
        this.gl = gl;
        this.program = program;
        this.attributeLocations = new Map();
        this.uniformLocations = new Map();
        this.uniformCache = new Map();

        this.initializeLocations();
    }

    initializeLocations() {
        // 初始化顶点属性 locations
        const numAttribs = this.gl.getProgramParameter(this.program, this.gl.ACTIVE_ATTRIBUTES);
        for (let i = 0; i < numAttribs; i++) {
            const attrib = this.gl.getActiveAttrib(this.program, i);
            const location = this.gl.getAttribLocation(this.program, attrib.name);
            this.attributeLocations.set(attrib.name, location);
        }

        // 初始化 uniform locations
        const numUniforms = this.gl.getProgramParameter(this.program, this.gl.ACTIVE_UNIFORMS);
        for (let i = 0; i < numUniforms; i++) {
            const uniform = this.gl.getActiveUniform(this.program, i);
            const location = this.gl.getUniformLocation(this.program, uniform.name);
            this.uniformLocations.set(uniform.name, location);
        }
    }

    // 安全的 uniform 设置
    setUniform(name, value) {
        const location = this.uniformLocations.get(name);
        if (!location) {
            console.warn(`Uniform ${name} not found`);
            return;
        }

        const cached = this.uniformCache.get(name);
        if (this.valuesEqual(cached, value)) {
            return; // 跳过重复设置
        }

        // 根据类型调用相应的 uniform 函数
        this.callUniformFunction(location, value);
        this.uniformCache.set(name, value);
    }

    // 获取顶点属性 location
    getAttributeLocation(name) {
        return this.attributeLocations.get(name);
    }

    valuesEqual(a, b) {
        // 实现值比较逻辑
        if (a === b) return true;
        if (Array.isArray(a) && Array.isArray(b)) {
            return a.length === b.length && a.every((val, i) => val === b[i]);
        }
        return false;
    }

    callUniformFunction(location, value) {
        // 根据值的类型调用相应的 WebGL uniform 函数
        if (typeof value === 'number') {
            this.gl.uniform1f(location, value);
        } else if (Array.isArray(value)) {
            switch (value.length) {
                case 2:
                    this.gl.uniform2fv(location, value);
                    break;
                case 3:
                    this.gl.uniform3fv(location, value);
                    break;
                case 4:
                    this.gl.uniform4fv(location, value);
                    break;
                case 16:
                    this.gl.uniformMatrix4fv(location, false, value);
                    break;
                default:
                    console.warn('Unsupported uniform array length:', value.length);
            }
        }
    }
}
```

通过这个详细的笔记，您现在应该完全理解了 WebGL 中两种不同类型的 location，以及它们各自的设计原理、使用方法和优化策略。

## 第九部分：Location 数量限制详解

### 9.1 两种 Location 都有数量限制

**重要理解**：顶点属性 Location 和 Uniform Location 都有数量限制，但限制方式和原因完全不同。

### 9.2 顶点属性 Location 的限制

#### 硬件槽位限制

```javascript
// 查询系统支持的最大顶点属性数量
const maxVertexAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
console.log('最大顶点属性数量:', maxVertexAttribs);

// 典型值：
// - 移动设备/低端GPU：8-16 个
// - 桌面设备/中端GPU：16-32 个
// - 高端GPU：32+ 个
// - WebGL规范最低要求：8 个
```

#### 限制原因：硬件槽位固定

```javascript
// GPU 硬件中的顶点属性处理单元数量是固定的
class GPUVertexProcessor {
    constructor() {
        // 硬件槽位数量在制造时就确定了
        this.attributeSlots = new Array(MAX_VERTEX_ATTRIBS); // 例如：16个槽位
        this.slotConfigs = new Array(MAX_VERTEX_ATTRIBS);
    }

    // 每个槽位对应一个硬件处理单元
    // 无法动态增加槽位数量
}
```

#### 超出限制的后果

```javascript
// 测试顶点属性限制
function testVertexAttributeLimit(gl, program) {
    const maxAttribs = gl.getParameter(gl.MAX_VERTEX_ATTRIBS);
    console.log(`系统支持最大 ${maxAttribs} 个顶点属性`);

    // 尝试获取超出限制的属性location
    for (let i = 0; i <= maxAttribs + 2; i++) {
        const location = gl.getAttribLocation(program, `a_attribute${i}`);
        console.log(`属性${i} location:`, location);

        if (location === -1) {
            console.warn(`属性${i} 未找到或超出硬件限制`);
        }
    }
}

// 结果示例（假设maxAttribs=16）：
// 属性0 location: 0
// 属性1 location: 1
// ...
// 属性15 location: 15
// 属性16 location: -1  ← 超出硬件限制
// 属性17 location: -1  ← 超出硬件限制
```

### 9.3 Uniform Location 的限制

#### 多维度限制

```javascript
// Uniform 有多个不同的限制维度
const uniformLimits = {
    // 顶点着色器中的uniform向量数
    maxVertexUniforms: gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS),

    // 片元着色器中的uniform向量数
    maxFragmentUniforms: gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS),

    // 纹理单元数量
    maxTextureUnits: gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS),

    // WebGL2: Uniform缓冲区块数量
    maxUniformBlocks: gl.getParameter(gl.MAX_COMBINED_UNIFORM_BLOCKS), // WebGL2
};

console.log('Uniform限制:', uniformLimits);

// 典型值：
// maxVertexUniforms: 128-1024 个向量
// maxFragmentUniforms: 16-1024 个向量
// maxTextureUnits: 8-32 个
// maxUniformBlocks: 24-36 个 (WebGL2)
```

#### 限制原因：内存大小限制

```javascript
// Uniform 限制基于 GPU 内存大小，不是槽位数量
class GPUUniformMemory {
    constructor() {
        // 预分配的uniform内存区域（固定大小）
        this.vertexUniformMemory = new ArrayBuffer(MAX_VERTEX_UNIFORM_VECTORS * 16); // 每个向量16字节
        this.fragmentUniformMemory = new ArrayBuffer(MAX_FRAGMENT_UNIFORM_VECTORS * 16);
        this.nextVertexOffset = 0;
        this.nextFragmentOffset = 0;
    }

    // 分配uniform内存时检查是否超出限制
    allocateUniform(type, size, stage) {
        const vectorCount = this.calculateVectorCount(type, size);
        const memory = stage === 'vertex' ? this.vertexUniformMemory : this.fragmentUniformMemory;
        const currentOffset = stage === 'vertex' ? this.nextVertexOffset : this.nextFragmentOffset;

        if (currentOffset + vectorCount * 16 > memory.byteLength) {
            throw new Error(`Uniform内存不足，超出${stage}着色器限制`);
        }

        // 分配成功，更新偏移量
        if (stage === 'vertex') {
            this.nextVertexOffset += vectorCount * 16;
        } else {
            this.nextFragmentOffset += vectorCount * 16;
        }
    }
}
```

#### Uniform 向量计算规则

```javascript
// Uniform 限制按"向量"计算，不是按"个数"
function calculateUniformVectors(uniforms) {
    let totalVectors = 0;

    for (const [name, info] of Object.entries(uniforms)) {
        let vectors = 0;

        switch (info.type) {
            case 'float':
            case 'int':
            case 'bool':
                vectors = 1; // 占用1个完整向量（浪费3个分量）
                break;

            case 'vec2':
                vectors = 1; // 占用1个向量（浪费2个分量）
                break;

            case 'vec3':
                vectors = 1; // 占用1个向量（浪费1个分量）
                break;

            case 'vec4':
                vectors = 1; // 正好占用1个向量
                break;

            case 'mat2':
                vectors = 2; // 2列，每列1个向量
                break;

            case 'mat3':
                vectors = 3; // 3列，每列1个向量
                break;

            case 'mat4':
                vectors = 4; // 4列，每列1个向量
                break;

            case 'sampler2D':
            case 'samplerCube':
                vectors = 1; // 纹理采样器占用1个向量
                break;
        }

        // 如果是数组，乘以数组大小
        if (info.size > 1) {
            vectors *= info.size;
        }

        totalVectors += vectors;
        console.log(`${name}: ${vectors} 个向量`);
    }

    return totalVectors;
}

// 使用示例
const exampleUniforms = {
    u_time: { type: 'float', size: 1 }, // 1个向量
    u_color: { type: 'vec3', size: 1 }, // 1个向量
    u_modelMatrix: { type: 'mat4', size: 1 }, // 4个向量
    u_viewMatrix: { type: 'mat4', size: 1 }, // 4个向量
    u_lightPositions: { type: 'vec3', size: 8 }, // 8个向量 (8个光源)
    u_diffuseTexture: { type: 'sampler2D', size: 1 }, // 1个向量
};

const totalVectors = calculateUniformVectors(exampleUniforms);
console.log(`总计使用: ${totalVectors} 个向量`); // 19个向量
```

### 9.4 限制检查和优化策略

#### 运行时限制检查

```javascript
// 完整的限制检查工具
class WebGLLimitChecker {
    constructor(gl) {
        this.gl = gl;
        this.limits = this.queryAllLimits();
    }

    queryAllLimits() {
        return {
            // 顶点属性限制
            maxVertexAttribs: this.gl.getParameter(this.gl.MAX_VERTEX_ATTRIBS),

            // Uniform限制
            maxVertexUniforms: this.gl.getParameter(this.gl.MAX_VERTEX_UNIFORM_VECTORS),
            maxFragmentUniforms: this.gl.getParameter(this.gl.MAX_FRAGMENT_UNIFORM_VECTORS),

            // 纹理限制
            maxTextureUnits: this.gl.getParameter(this.gl.MAX_TEXTURE_IMAGE_UNITS),
            maxVertexTextureUnits: this.gl.getParameter(this.gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS),

            // 其他限制
            maxVaryingVectors: this.gl.getParameter(this.gl.MAX_VARYING_VECTORS),
            maxRenderbufferSize: this.gl.getParameter(this.gl.MAX_RENDERBUFFER_SIZE),
        };
    }

    checkVertexAttributeUsage(program) {
        const activeAttribs = this.gl.getProgramParameter(program, this.gl.ACTIVE_ATTRIBUTES);
        const maxAttribs = this.limits.maxVertexAttribs;

        console.log(`顶点属性使用情况: ${activeAttribs}/${maxAttribs}`);

        if (activeAttribs > maxAttribs) {
            console.error(`顶点属性超出限制！使用了${activeAttribs}个，但最大支持${maxAttribs}个`);
            return false;
        }

        if (activeAttribs / maxAttribs > 0.8) {
            console.warn(`顶点属性使用率较高: ${((activeAttribs / maxAttribs) * 100).toFixed(1)}%`);
        }

        return true;
    }

    checkUniformUsage(program) {
        const activeUniforms = this.gl.getProgramParameter(program, this.gl.ACTIVE_UNIFORMS);
        let vertexVectors = 0;
        let fragmentVectors = 0;

        // 计算实际使用的向量数
        for (let i = 0; i < activeUniforms; i++) {
            const uniform = this.gl.getActiveUniform(program, i);
            const vectors = this.calculateUniformVectors(uniform.type, uniform.size);

            // 简化假设：uniform在两个着色器中都使用
            vertexVectors += vectors;
            fragmentVectors += vectors;
        }

        console.log(`Uniform使用情况:`);
        console.log(`  顶点着色器: ${vertexVectors}/${this.limits.maxVertexUniforms} 向量`);
        console.log(`  片元着色器: ${fragmentVectors}/${this.limits.maxFragmentUniforms} 向量`);

        const vertexOK = vertexVectors <= this.limits.maxVertexUniforms;
        const fragmentOK = fragmentVectors <= this.limits.maxFragmentUniforms;

        if (!vertexOK || !fragmentOK) {
            console.error('Uniform超出限制！');
            return false;
        }

        return true;
    }

    calculateUniformVectors(type, size) {
        const vectorCounts = {
            [this.gl.FLOAT]: 1,
            [this.gl.FLOAT_VEC2]: 1,
            [this.gl.FLOAT_VEC3]: 1,
            [this.gl.FLOAT_VEC4]: 1,
            [this.gl.INT]: 1,
            [this.gl.BOOL]: 1,
            [this.gl.FLOAT_MAT2]: 2,
            [this.gl.FLOAT_MAT3]: 3,
            [this.gl.FLOAT_MAT4]: 4,
            [this.gl.SAMPLER_2D]: 1,
            [this.gl.SAMPLER_CUBE]: 1,
        };

        return (vectorCounts[type] || 1) * size;
    }

    generateReport() {
        console.log('=== WebGL 限制报告 ===');
        console.log('顶点属性限制:', this.limits.maxVertexAttribs);
        console.log('顶点Uniform限制:', this.limits.maxVertexUniforms, '个向量');
        console.log('片元Uniform限制:', this.limits.maxFragmentUniforms, '个向量');
        console.log('纹理单元限制:', this.limits.maxTextureUnits);
        console.log('====================');
    }
}

// 使用示例
const limitChecker = new WebGLLimitChecker(gl);
limitChecker.generateReport();
limitChecker.checkVertexAttributeUsage(program);
limitChecker.checkUniformUsage(program);
```

### 9.5 优化建议

#### 顶点属性优化

```javascript
// ✅ 优化：合并属性减少槽位使用
// 原始方案：使用4个槽位
// attribute vec3 position;    // 槽位0
// attribute vec3 normal;      // 槽位1
// attribute vec2 uv;          // 槽位2
// attribute float materialID; // 槽位3

// 优化方案：使用2个槽位
// attribute vec4 positionAndMaterialID; // 槽位0: xyz=position, w=materialID
// attribute vec4 normalAndUV;           // 槽位1: xyz=normal, w=unused
// attribute vec2 uv;                    // 可以打包到normalAndUV的zw分量中
```

#### Uniform 优化

```javascript
// ✅ 优化：使用Uniform缓冲区对象 (WebGL2)
// 原始方案：每个uniform单独占用向量
// uniform float u_time;        // 1个向量（浪费3个分量）
// uniform float u_deltaTime;   // 1个向量（浪费3个分量）
// uniform float u_frameCount;  // 1个向量（浪费3个分量）

// 优化方案：打包到一个向量
// uniform vec4 u_timeData;     // 1个向量：x=time, y=deltaTime, z=frameCount, w=unused

// 或使用UBO (WebGL2)
// layout(std140) uniform TimeBlock {
//     float time;
//     float deltaTime;
//     float frameCount;
//     float padding;
// };
```

### 总结

1. **顶点属性 Location**：硬件槽位限制（8-32 个）
2. **Uniform Location**：内存大小限制（按向量计算，128-1024 个向量）
3. **两者都需要**：合理规划和优化使用
4. **检查方法**：使用 WebGL 参数查询 API
5. **优化策略**：属性合并、uniform 打包、使用 UBO 等
