# WebGL uniform1f 与 uniform1fv 区别详解

## 核心问题分析

您选中的代码：

```javascript
return value.length ? gl.uniform1fv(location, value) : gl.uniform1f(location, value);
```

这个 `value.length` 判断是为了区分**单个浮点数**和**浮点数数组**两种不同的数据类型，从而调用对应的 WebGL 函数。

## WebGL API 区别

### gl.uniform1f() - 设置单个浮点数

```javascript
gl.uniform1f(location, value);
// 参数：location (位置), value (单个数值)
```

### gl.uniform1fv() - 设置浮点数数组

```javascript
gl.uniform1fv(location, array);
// 参数：location (位置), array (数组或类型化数组)
```

## 实际使用场景示例

### 场景 1：单个浮点数 uniform

**着色器定义：**

```glsl
uniform float u_time;        // 单个时间值
uniform float u_opacity;     // 单个透明度值
uniform float u_scale;       // 单个缩放值
```

**JavaScript 使用：**

```javascript
// 单个浮点数值
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: fragmentShader,
    uniforms: {
        u_time: { value: 1.5 }, // 单个数值
        u_opacity: { value: 0.8 }, // 单个数值
        u_scale: { value: 2.0 }, // 单个数值
    },
});

// 执行流程：
// value = 1.5
// value.length = undefined (数值没有length属性)
// 条件判断：value.length ? false
// 调用：gl.uniform1f(location, 1.5)
```

### 场景 2：浮点数数组 uniform

**着色器定义：**

```glsl
uniform float u_weights[4];     // 浮点数数组
uniform float u_factors[3];     // 混合因子数组
uniform float u_distances[8];   // 距离数组
```

**JavaScript 使用：**

```javascript
// 浮点数数组
const program = new Program(gl, {
    vertex: vertexShader,
    fragment: fragmentShader,
    uniforms: {
        u_weights: { value: [0.25, 0.5, 0.75, 1.0] }, // 数组
        u_factors: { value: [1.0, 0.5, 0.2] }, // 数组
        u_distances: { value: new Float32Array(8) }, // 类型化数组
    },
});

// 执行流程：
// value = [0.25, 0.5, 0.75, 1.0]
// value.length = 4
// 条件判断：value.length ? true
// 调用：gl.uniform1fv(location, [0.25, 0.5, 0.75, 1.0])
```

### 场景 3：动画权重系统

**实际应用：骨骼动画权重**

```javascript
// 骨骼动画中的权重数组
const boneWeights = [0.8, 0.2, 0.0, 0.0]; // 4个骨骼的权重

const skeletalProgram = new Program(gl, {
    vertex: `
        attribute vec4 a_boneWeights;
        uniform float u_boneWeights[4];  // 骨骼权重数组
        
        void main() {
            // 使用权重数组计算顶点位置
            float totalWeight = 0.0;
            for(int i = 0; i < 4; i++) {
                totalWeight += u_boneWeights[i] * a_boneWeights[i];
            }
            // ...
        }
    `,
    fragment: fragmentShader,
    uniforms: {
        u_boneWeights: { value: boneWeights }, // 数组类型
    },
});

// 动画更新时
function updateAnimation(frame) {
    // 更新权重数组
    const newWeights = calculateBoneWeights(frame);
    skeletalProgram.uniforms.u_boneWeights.value = newWeights;

    // 内部执行：
    // value = [0.6, 0.3, 0.1, 0.0]
    // value.length = 4 (true)
    // 调用：gl.uniform1fv(location, newWeights)
}
```

### 场景 4：多光源系统

**实际应用：多个光源的强度数组**

```javascript
// 8个点光源的强度值
const lightIntensities = [1.0, 0.8, 0.6, 0.4, 0.2, 0.1, 0.05, 0.0];

const lightingProgram = new Program(gl, {
    vertex: vertexShader,
    fragment: `
        uniform float u_lightIntensities[8];  // 光源强度数组
        uniform vec3 u_lightPositions[8];     // 光源位置数组
        
        void main() {
            vec3 finalColor = vec3(0.0);
            
            // 遍历所有光源
            for(int i = 0; i < 8; i++) {
                if(u_lightIntensities[i] > 0.0) {
                    // 计算光照贡献
                    vec3 lightDir = normalize(u_lightPositions[i] - vWorldPos);
                    float intensity = u_lightIntensities[i];
                    finalColor += calculateLighting(lightDir, intensity);
                }
            }
            
            gl_FragColor = vec4(finalColor, 1.0);
        }
    `,
    uniforms: {
        u_lightIntensities: { value: lightIntensities }, // 数组类型
        u_lightPositions: { value: lightPositions }, // 向量数组
    },
});

// 动态调整光源强度
function updateLightIntensities(time) {
    lightIntensities.forEach((intensity, index) => {
        // 添加闪烁效果
        lightIntensities[index] = intensity * (0.8 + 0.2 * Math.sin(time + index));
    });

    // 更新uniform数组
    lightingProgram.uniforms.u_lightIntensities.value = lightIntensities;

    // 内部执行：
    // value = [0.95, 0.76, 0.58, ...] (修改后的数组)
    // value.length = 8 (true)
    // 调用：gl.uniform1fv(location, lightIntensities)
}
```

### 场景 5：后处理滤镜权重

**实际应用：图像处理滤镜的权重系数**

```javascript
// 高斯模糊的权重系数
const blurWeights = [0.227027, 0.1945946, 0.1216216, 0.054054, 0.016216];

const blurProgram = new Program(gl, {
    vertex: vertexShader,
    fragment: `
        uniform sampler2D u_texture;
        uniform float u_blurWeights[5];  // 模糊权重数组
        uniform vec2 u_texelSize;
        
        void main() {
            vec4 color = texture2D(u_texture, vUv) * u_blurWeights[0];
            
            // 水平模糊
            for(int i = 1; i < 5; i++) {
                float weight = u_blurWeights[i];
                vec2 offset = vec2(float(i) * u_texelSize.x, 0.0);
                
                color += texture2D(u_texture, vUv + offset) * weight;
                color += texture2D(u_texture, vUv - offset) * weight;
            }
            
            gl_FragColor = color;
        }
    `,
    uniforms: {
        u_blurWeights: { value: blurWeights }, // 数组类型
        u_texelSize: { value: [1.0 / width, 1.0 / height] }, // 向量类型
    },
});

// 动态调整模糊强度
function setBlurStrength(strength) {
    // 根据强度调整权重
    const adjustedWeights = blurWeights.map((w) => w * strength);
    blurProgram.uniforms.u_blurWeights.value = adjustedWeights;

    // 内部执行：
    // value = [0.113514, 0.097297, ...] (调整后的数组)
    // value.length = 5 (true)
    // 调用：gl.uniform1fv(location, adjustedWeights)
}
```

## 技术原理深入分析

### 1. JavaScript 类型检测机制

```javascript
// 数值类型检测
const singleValue = 1.5;
console.log(singleValue.length); // undefined
console.log(Boolean(singleValue.length)); // false

// 数组类型检测
const arrayValue = [1.0, 2.0, 3.0];
console.log(arrayValue.length); // 3
console.log(Boolean(arrayValue.length)); // true

// 类型化数组检测
const typedArray = new Float32Array([1.0, 2.0]);
console.log(typedArray.length); // 2
console.log(Boolean(typedArray.length)); // true
```

### 2. WebGL 函数调用差异

```javascript
// setUniform 函数内部逻辑
function setUniform(gl, type, location, value) {
    switch (type) {
        case 5126: // gl.FLOAT
            if (value.length) {
                // 数组情况：调用数组版本函数
                // 可以处理：[1.0, 2.0, 3.0] 或 Float32Array
                return gl.uniform1fv(location, value);
            } else {
                // 单值情况：调用单值版本函数
                // 可以处理：1.5 或 Number 类型
                return gl.uniform1f(location, value);
            }
    }
}
```

### 3. 性能考虑

```javascript
// 单值设置（高效）
gl.uniform1f(location, 1.5);
// GPU 接收：直接设置单个内存位置

// 数组设置（批量传输）
gl.uniform1fv(location, [1.0, 2.0, 3.0, 4.0]);
// GPU 接收：批量设置连续内存区域
```

## 常见错误和解决方案

### 错误 1：类型不匹配

```javascript
// ❌ 错误：着色器期望数组，但传入单值
// GLSL: uniform float u_weights[4];
// JS: u_weights: { value: 1.0 }  // 应该是数组

// ✅ 正确：
// JS: u_weights: { value: [1.0, 1.0, 1.0, 1.0] }
```

### 错误 2：数组长度不匹配

```javascript
// ❌ 错误：着色器期望4个元素，但只传入2个
// GLSL: uniform float u_factors[4];
// JS: u_factors: { value: [1.0, 0.5] }  // 长度不匹配

// ✅ 正确：
// JS: u_factors: { value: [1.0, 0.5, 0.0, 0.0] }
```

### 错误 3：动态更新问题

```javascript
// ❌ 错误：直接修改数组元素但不重新赋值
program.uniforms.u_weights.value[0] = 0.8; // 不会触发更新

// ✅ 正确：重新赋值整个数组
const newWeights = [...program.uniforms.u_weights.value];
newWeights[0] = 0.8;
program.uniforms.u_weights.value = newWeights;
```

## 最佳实践建议

### 1. 明确数据类型

```javascript
// 单值uniform使用数值
const singleUniforms = {
    u_time: { value: 0.0 },
    u_opacity: { value: 1.0 },
};

// 数组uniform使用数组
const arrayUniforms = {
    u_weights: { value: [1.0, 0.8, 0.6, 0.4] },
    u_factors: { value: new Float32Array(8) },
};
```

### 2. 性能优化

```javascript
// 预分配类型化数组避免重复创建
const lightIntensities = new Float32Array(8);

function updateLights(intensities) {
    // 直接修改现有数组内容
    lightIntensities.set(intensities);
    program.uniforms.u_lightIntensities.value = lightIntensities;
}
```

### 3. 类型安全检查

```javascript
function setUniformSafely(program, name, value) {
    const uniform = program.uniforms[name];
    if (!uniform) {
        console.warn(`Uniform ${name} not found`);
        return;
    }

    // 检查类型匹配
    const isArray = Array.isArray(value) || value.length !== undefined;
    const expectedArray = Array.isArray(uniform.value) || uniform.value.length !== undefined;

    if (isArray !== expectedArray) {
        console.error(`Type mismatch for uniform ${name}`);
        return;
    }

    uniform.value = value;
}
```

## 总结

`value.length` 判断是 WebGL uniform 系统中的关键设计，它：

1. **自动识别数据类型**：区分单值和数组，调用对应的 WebGL 函数
2. **提供灵活性**：同一个 uniform 可以处理不同的数据格式
3. **保证正确性**：避免类型错误导致的渲染问题
4. **优化性能**：根据数据类型选择最适合的传输方式

理解这个机制有助于：

-   正确设置 uniform 值
-   避免常见的类型错误
-   优化渲染性能
-   编写更健壮的 WebGL 代码
