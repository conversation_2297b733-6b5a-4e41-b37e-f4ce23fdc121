# JavaScript 类型化数组详解与 WebGL 应用

## 概述

类型化数组（Typed Arrays）是 JavaScript 中用于处理二进制数据的专门数据结构，在 WebGL、音频处理、图像处理等需要高性能数据操作的场景中发挥着关键作用。与普通 JavaScript 数组不同，类型化数组提供了对底层内存的直接访问，具有固定的数据类型和连续的内存布局。

## 核心概念

### ArrayBuffer - 内存缓冲区

```javascript
// 创建一个16字节的内存缓冲区
const buffer = new ArrayBuffer(16);
console.log(buffer.byteLength); // 16
```

ArrayBuffer 是所有类型化数组的基础，它代表一块固定长度的原始二进制数据缓冲区。ArrayBuffer 本身不能直接读写，需要通过视图（View）来操作。

### DataView - 灵活的数据视图

```javascript
const buffer = new ArrayBuffer(16);
const view = new DataView(buffer);

// 在偏移量0处写入一个32位整数
view.setInt32(0, 42);
// 在偏移量4处写入一个32位浮点数
view.setFloat32(4, 3.14);

// 读取数据
console.log(view.getInt32(0)); // 42
console.log(view.getFloat32(4)); // 3.14159...
```

## 类型化数组类型详解

### 整数类型数组

#### Int8Array - 8 位有符号整数

-   **范围**: -128 到 127
-   **字节大小**: 1 字节/元素
-   **使用场景**: 存储小范围整数、字符数据、像素 alpha 通道

```javascript
const int8 = new Int8Array([127, -128, 0]);
console.log(int8.BYTES_PER_ELEMENT); // 1
```

#### Uint8Array - 8 位无符号整数

-   **范围**: 0 到 255
-   **字节大小**: 1 字节/元素
-   **使用场景**: RGB 像素数据、二进制文件处理、纹理数据

```javascript
const uint8 = new Uint8Array([255, 128, 0]);
// 常用于图像处理
const imageData = new Uint8Array(width * height * 4); // RGBA
```

#### Uint8ClampedArray - 8 位无符号整数（截断）

-   **范围**: 0 到 255（超出范围会被截断）
-   **字节大小**: 1 字节/元素
-   **使用场景**: Canvas ImageData、像素操作

```javascript
const clamped = new Uint8ClampedArray([300, -50, 128]);
console.log(clamped); // [255, 0, 128] - 自动截断
```

#### Int16Array - 16 位有符号整数

-   **范围**: -32,768 到 32,767
-   **字节大小**: 2 字节/元素
-   **使用场景**: 音频采样、顶点索引、中等范围数值

```javascript
const int16 = new Int16Array([32767, -32768, 0]);
```

#### Uint16Array - 16 位无符号整数

-   **范围**: 0 到 65,535
-   **字节大小**: 2 字节/元素
-   **使用场景**: 顶点索引、深度缓冲、高精度像素数据

```javascript
const uint16 = new Uint16Array([65535, 32768, 0]);
```

#### Int32Array - 32 位有符号整数

-   **范围**: -2,147,483,648 到 2,147,483,647
-   **字节大小**: 4 字节/元素
-   **使用场景**: 大整数计算、对象 ID、计数器

```javascript
const int32 = new Int32Array([2147483647, -2147483648]);
```

#### Uint32Array - 32 位无符号整数

-   **范围**: 0 到 4,294,967,295
-   **字节大小**: 4 字节/元素
-   **使用场景**: 大型索引、哈希值、无符号计算

```javascript
const uint32 = new Uint32Array([4294967295, 2147483648]);
```

### 浮点数类型数组

#### Float32Array - 32 位浮点数

-   **精度**: 单精度（约 7 位有效数字）
-   **字节大小**: 4 字节/元素
-   **使用场景**: WebGL 顶点数据、3D 坐标、颜色值、法向量

```javascript
const float32 = new Float32Array([3.14159, -2.71828, 1.41421]);
// WebGL中最常用的数据类型
const vertices = new Float32Array([
    -1.0,
    -1.0,
    0.0, // 顶点1
    1.0,
    -1.0,
    0.0, // 顶点2
    0.0,
    1.0,
    0.0, // 顶点3
]);
```

#### Float64Array - 64 位浮点数

-   **精度**: 双精度（约 15-17 位有效数字）
-   **字节大小**: 8 字节/元素
-   **使用场景**: 高精度计算、科学计算、金融计算

```javascript
const float64 = new Float64Array([Math.PI, Math.E, Math.SQRT2]);
```

### BigInt 类型数组

#### BigInt64Array - 64 位有符号大整数

-   **范围**: -2^63 到 2^63-1
-   **字节大小**: 8 字节/元素
-   **使用场景**: 大整数运算、时间戳、加密计算

```javascript
const bigInt64 = new BigInt64Array([9007199254740991n, -9007199254740991n]);
```

#### BigUint64Array - 64 位无符号大整数

-   **范围**: 0 到 2^64-1
-   **字节大小**: 8 字节/元素
-   **使用场景**: 大型无符号整数、哈希计算

```javascript
const bigUint64 = new BigUint64Array([18446744073709551615n]);
```

## WebGL 中的应用场景

### 顶点数据存储

```javascript
// 三角形顶点坐标 (x, y, z)
const vertices = new Float32Array([-0.5, -0.5, 0.0, 0.5, -0.5, 0.0, 0.0, 0.5, 0.0]);

// 顶点颜色 (r, g, b, a)
const colors = new Float32Array([
    1.0,
    0.0,
    0.0,
    1.0, // 红色
    0.0,
    1.0,
    0.0,
    1.0, // 绿色
    0.0,
    0.0,
    1.0,
    1.0, // 蓝色
]);
```

### 索引数据

```javascript
// 使用索引减少重复顶点
const indices = new Uint16Array([
    0,
    1,
    2, // 第一个三角形
    2,
    3,
    0, // 第二个三角形
]);
```

### 纹理数据

```javascript
// 创建2x2的RGBA纹理
const textureData = new Uint8Array([
    255,
    0,
    0,
    255, // 红色像素
    0,
    255,
    0,
    255, // 绿色像素
    0,
    0,
    255,
    255, // 蓝色像素
    255,
    255,
    0,
    255, // 黄色像素
]);
```

## 类型化数组 vs 普通数组：深度对比

### 核心差异对比表

| 特性         | 普通数组 (Array)           | 类型化数组 (Typed Array)       |
| ------------ | -------------------------- | ------------------------------ |
| **数据类型** | 动态类型，可存储任意值     | 固定类型，只能存储特定数值类型 |
| **内存布局** | 非连续，每个元素是对象引用 | 连续内存块，直接存储数值       |
| **内存占用** | 每个元素约 24-32 字节开销  | 每个元素仅占用类型大小         |
| **访问速度** | 较慢，需要对象查找         | 快速，直接内存访问             |
| **长度**     | 动态可变                   | 固定长度，创建后不可改变       |
| **方法支持** | 丰富的数组方法             | 有限的方法集合                 |
| **垃圾回收** | 频繁 GC，影响性能          | 较少 GC 压力                   |
| **跨平台**   | 一致性好                   | 受字节序影响                   |

### 详细差异分析

#### 1. 内存结构差异

```javascript
// 普通数组 - 每个元素都是对象引用
const normalArray = [1, 2, 3, 4];
// 内存结构：[ref1] -> {value: 1}, [ref2] -> {value: 2}, ...
// 每个数字对象占用约24-32字节

// 类型化数组 - 连续的原始数据
const typedArray = new Int32Array([1, 2, 3, 4]);
// 内存结构：[01 00 00 00][02 00 00 00][03 00 00 00][04 00 00 00]
// 每个整数占用4字节
```

#### 2. 性能差异实测

```javascript
// 性能测试函数
function performanceTest() {
    const size = 1000000;

    // 普通数组测试
    console.time('普通数组创建');
    const normalArray = new Array(size);
    for (let i = 0; i < size; i++) {
        normalArray[i] = Math.random();
    }
    console.timeEnd('普通数组创建');

    console.time('普通数组计算');
    for (let i = 0; i < normalArray.length; i++) {
        normalArray[i] *= 2;
    }
    console.timeEnd('普通数组计算');

    // 类型化数组测试
    console.time('类型化数组创建');
    const typedArray = new Float32Array(size);
    for (let i = 0; i < size; i++) {
        typedArray[i] = Math.random();
    }
    console.timeEnd('类型化数组创建');

    console.time('类型化数组计算');
    for (let i = 0; i < typedArray.length; i++) {
        typedArray[i] *= 2;
    }
    console.timeEnd('类型化数组计算');
}

// 典型结果：类型化数组比普通数组快2-10倍
```

#### 3. 功能差异

```javascript
// 普通数组 - 丰富的方法
const arr = [1, 2, 3, 4, 5];
arr.push(6); // 动态添加
arr.pop(); // 动态删除
arr.map((x) => x * 2); // 函数式操作
arr.filter((x) => x > 2); // 过滤
arr.reduce((a, b) => a + b); // 归约
arr.sort(); // 排序

// 类型化数组 - 有限的方法
const typedArr = new Int32Array([1, 2, 3, 4, 5]);
// typedArr.push(6);           // ❌ 不支持
// typedArr.pop();             // ❌ 不支持
typedArr.map((x) => x * 2); // ✅ 支持（ES2015+）
typedArr.filter((x) => x > 2); // ✅ 支持（ES2015+）
typedArr.reduce((a, b) => a + b); // ✅ 支持（ES2015+）
typedArr.sort(); // ✅ 支持
```

#### 4. 类型安全差异

```javascript
// 普通数组 - 类型不安全
const arr = [1, 'hello', {}, null, undefined];
arr[0] = '现在是字符串'; // 完全合法

// 类型化数组 - 类型安全
const typedArr = new Int32Array(5);
typedArr[0] = 42; // ✅ 正常
typedArr[1] = 3.14; // ⚠️ 自动截断为3
typedArr[2] = 'hello'; // ⚠️ 转换为0
typedArr[3] = null; // ⚠️ 转换为0
typedArr[4] = undefined; // ⚠️ 转换为0
console.log(typedArr); // [42, 3, 0, 0, 0]
```

## 使用场景选择指南

### 🎯 选择类型化数组的场景

#### 1. WebGL/图形编程

```javascript
// 顶点数据 - 大量浮点数计算
const vertices = new Float32Array([
    -1.0,
    -1.0,
    0.0, // 顶点坐标
    1.0,
    -1.0,
    0.0,
    0.0,
    1.0,
    0.0,
]);

// 索引数据 - 整数索引
const indices = new Uint16Array([0, 1, 2]);

// 纹理数据 - 像素值
const textureData = new Uint8Array(width * height * 4);
```

#### 2. 音频处理

```javascript
// 音频采样数据
const audioBuffer = new Float32Array(sampleRate * duration);

// 频谱分析
const frequencyData = new Uint8Array(analyser.frequencyBinCount);
analyser.getByteFrequencyData(frequencyData);
```

#### 3. 图像处理

```javascript
// Canvas像素数据
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
// imageData.data 是 Uint8ClampedArray

// 图像滤镜处理
function applyFilter(imageData) {
    const data = imageData.data; // Uint8ClampedArray
    for (let i = 0; i < data.length; i += 4) {
        data[i] = Math.min(255, data[i] * 1.2); // R
        data[i + 1] = Math.min(255, data[i + 1] * 1.2); // G
        data[i + 2] = Math.min(255, data[i + 2] * 1.2); // B
        // data[i + 3] 是 alpha，保持不变
    }
}
```

#### 4. 科学计算/数值计算

```javascript
// 大规模数值计算
const matrix = new Float64Array(1000 * 1000);
const vector = new Float64Array(1000);

// 矩阵运算
function matrixMultiply(a, b, result, size) {
    for (let i = 0; i < size; i++) {
        for (let j = 0; j < size; j++) {
            let sum = 0;
            for (let k = 0; k < size; k++) {
                sum += a[i * size + k] * b[k * size + j];
            }
            result[i * size + j] = sum;
        }
    }
}
```

#### 5. 网络数据传输

```javascript
// 二进制协议数据
const packet = new ArrayBuffer(1024);
const header = new Uint32Array(packet, 0, 4); // 前16字节作为头部
const payload = new Uint8Array(packet, 16); // 剩余作为载荷

// WebSocket二进制数据
websocket.binaryType = 'arraybuffer';
websocket.onmessage = function (event) {
    const data = new Uint8Array(event.data);
    // 处理二进制数据
};
```

#### 6. 游戏开发

```javascript
// 粒子系统
class ParticleSystem {
    constructor(maxParticles) {
        // 位置 (x, y, z)
        this.positions = new Float32Array(maxParticles * 3);
        // 速度 (vx, vy, vz)
        this.velocities = new Float32Array(maxParticles * 3);
        // 生命周期
        this.lifetimes = new Float32Array(maxParticles);
    }

    update(deltaTime) {
        for (let i = 0; i < this.positions.length; i += 3) {
            this.positions[i] += this.velocities[i] * deltaTime;
            this.positions[i + 1] += this.velocities[i + 1] * deltaTime;
            this.positions[i + 2] += this.velocities[i + 2] * deltaTime;
        }
    }
}
```

### 🎯 选择普通数组的场景

#### 1. 通用数据处理

```javascript
// 混合数据类型
const userData = [
    { id: 1, name: '张三', age: 25 },
    { id: 2, name: '李四', age: 30 },
    { id: 3, name: '王五', age: 28 },
];

// 复杂数据操作
const adults = userData.filter((user) => user.age >= 18);
const names = userData.map((user) => user.name);
```

#### 2. 动态数据集合

```javascript
// 购物车 - 需要动态添加/删除
const shoppingCart = [];
shoppingCart.push({ product: '手机', price: 3999 });
shoppingCart.push({ product: '耳机', price: 299 });
shoppingCart.splice(0, 1); // 删除第一项
```

#### 3. 函数式编程

```javascript
// 链式操作
const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const result = numbers
    .filter((n) => n % 2 === 0)
    .map((n) => n * n)
    .reduce((sum, n) => sum + n, 0);
```

#### 4. 字符串和复杂对象处理

```javascript
// 字符串数组
const words = ['hello', 'world', 'javascript'];
const upperWords = words.map((word) => word.toUpperCase());

// 嵌套对象
const menuItems = [
    {
        category: '主食',
        items: [
            { name: '米饭', price: 3 },
            { name: '面条', price: 8 },
        ],
    },
];
```

### 🎯 性能考量决策树

```
是否需要处理大量数值数据？
├─ 是 → 数据类型是否固定？
│   ├─ 是 → 是否需要与WebGL/Canvas/音频API交互？
│   │   ├─ 是 → 使用类型化数组 ✅
│   │   └─ 否 → 数据量是否超过10万个元素？
│   │       ├─ 是 → 使用类型化数组 ✅
│   │       └─ 否 → 考虑普通数组
│   └─ 否 → 使用普通数组 ✅
└─ 否 → 是否需要复杂的数组操作方法？
    ├─ 是 → 使用普通数组 ✅
    └─ 否 → 根据具体需求选择
```

### 🎯 混合使用策略

有时候可以结合两者的优势：

```javascript
// 数据处理阶段使用普通数组
const rawData = [
    { x: 1, y: 2, z: 3 },
    { x: 4, y: 5, z: 6 },
    { x: 7, y: 8, z: 9 },
];

// 过滤和变换
const processedData = rawData.filter((point) => point.x > 0).map((point) => ({ ...point, length: Math.sqrt(point.x ** 2 + point.y ** 2 + point.z ** 2) }));

// 转换为类型化数组用于WebGL
const vertices = new Float32Array(processedData.flatMap((point) => [point.x, point.y, point.z]));

// 传递给WebGL
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
```

## 性能优化建议

### 1. 预分配内存

```javascript
// ❌ 避免频繁创建
function badExample() {
    for (let i = 0; i < 1000; i++) {
        const temp = new Float32Array([Math.random(), Math.random()]);
        // 处理temp...
    }
}

// ✅ 预分配重用
function goodExample() {
    const reusableBuffer = new Float32Array(2);
    for (let i = 0; i < 1000; i++) {
        reusableBuffer[0] = Math.random();
        reusableBuffer[1] = Math.random();
        // 处理reusableBuffer...
    }
}
```

### 2. 批量操作

```javascript
// ❌ 逐个设置
const arr = new Float32Array(1000);
for (let i = 0; i < 1000; i++) {
    arr[i] = i;
}

// ✅ 批量设置
const source = new Float32Array(1000);
for (let i = 0; i < 1000; i++) {
    source[i] = i;
}
const arr = new Float32Array(source); // 一次性复制
```

### 3. 避免不必要的类型转换

```javascript
// ❌ 频繁转换
function inefficient(data) {
    const normalArray = Array.from(data); // 转换为普通数组
    const processed = normalArray.map((x) => x * 2);
    return new Float32Array(processed); // 再转换回类型化数组
}

// ✅ 直接操作
function efficient(data) {
    for (let i = 0; i < data.length; i++) {
        data[i] *= 2;
    }
    return data;
}
```

## 数组间转换技巧

### 普通数组 ↔ 类型化数组转换

```javascript
// 普通数组 → 类型化数组
const normalArray = [1, 2, 3, 4, 5];
const typedArray = new Float32Array(normalArray);

// 类型化数组 → 普通数组
const backToNormal = Array.from(typedArray);
// 或者使用扩展运算符
const backToNormal2 = [...typedArray];

// 性能对比：直接构造 > Array.from > 扩展运算符
```

### 不同类型化数组间转换

```javascript
// Int32Array → Float32Array
const intArray = new Int32Array([1, 2, 3, 4]);
const floatArray = new Float32Array(intArray);

// 共享内存的转换（零拷贝）
const buffer = new ArrayBuffer(16);
const int32View = new Int32Array(buffer);
const float32View = new Float32Array(buffer);

int32View[0] = 1065353216; // 1.0的IEEE 754表示
console.log(float32View[0]); // 1.0
```

### 字符串与类型化数组转换

```javascript
// 字符串 → Uint8Array (UTF-8编码)
function stringToUint8Array(str) {
    const encoder = new TextEncoder();
    return encoder.encode(str);
}

// Uint8Array → 字符串 (UTF-8解码)
function uint8ArrayToString(uint8Array) {
    const decoder = new TextDecoder();
    return decoder.decode(uint8Array);
}

// 示例
const text = 'Hello, 世界!';
const bytes = stringToUint8Array(text);
const restored = uint8ArrayToString(bytes);
console.log(restored); // "Hello, 世界!"
```

## 实际应用案例深度解析

### 案例 1：WebGL 纹理加载优化

```javascript
class TextureLoader {
    static async loadTexture(url) {
        const response = await fetch(url);
        const arrayBuffer = await response.arrayBuffer();

        // 直接从ArrayBuffer创建视图，避免额外复制
        const uint8View = new Uint8Array(arrayBuffer);

        // 解析图像头部信息（以PNG为例）
        const width = this.readUint32BE(uint8View, 16);
        const height = this.readUint32BE(uint8View, 20);

        // 提取像素数据
        const pixelData = this.extractPixelData(uint8View);

        return {
            width,
            height,
            data: pixelData, // 已经是Uint8Array，可直接用于WebGL
        };
    }

    static readUint32BE(buffer, offset) {
        return (buffer[offset] << 24) | (buffer[offset + 1] << 16) | (buffer[offset + 2] << 8) | buffer[offset + 3];
    }
}
```

### 案例 2：音频可视化频谱分析

```javascript
class AudioVisualizer {
    constructor(audioContext) {
        this.audioContext = audioContext;
        this.analyser = audioContext.createAnalyser();
        this.analyser.fftSize = 2048;

        // 预分配缓冲区
        this.frequencyData = new Uint8Array(this.analyser.frequencyBinCount);
        this.timeData = new Uint8Array(this.analyser.fftSize);

        // WebGL顶点数据
        this.vertices = new Float32Array(this.analyser.frequencyBinCount * 6);
    }

    updateVisualization() {
        // 获取频域数据
        this.analyser.getByteFrequencyData(this.frequencyData);

        // 转换为WebGL顶点数据
        for (let i = 0; i < this.frequencyData.length; i++) {
            const x = (i / this.frequencyData.length) * 2 - 1;
            const height = (this.frequencyData[i] / 255) * 2;

            // 每个频率条用两个三角形（6个顶点）
            const baseIndex = i * 6;
            this.vertices[baseIndex] = x;
            this.vertices[baseIndex + 1] = 0;
            this.vertices[baseIndex + 2] = x + 0.01;
            this.vertices[baseIndex + 3] = 0;
            this.vertices[baseIndex + 4] = x;
            this.vertices[baseIndex + 5] = height;
        }

        // 更新WebGL缓冲区
        gl.bufferSubData(gl.ARRAY_BUFFER, 0, this.vertices);
    }
}
```

### 案例 3：图像处理滤镜链

```javascript
class ImageProcessor {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.imageData = null;
        this.originalData = null;
    }

    loadImage(imageElement) {
        this.ctx.drawImage(imageElement, 0, 0);
        this.imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        // 保存原始数据副本
        this.originalData = new Uint8ClampedArray(this.imageData.data);
    }

    // 亮度调整滤镜
    brightness(factor) {
        const data = this.imageData.data;
        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, data[i] * factor); // R
            data[i + 1] = Math.min(255, data[i + 1] * factor); // G
            data[i + 2] = Math.min(255, data[i + 2] * factor); // B
            // Alpha通道保持不变
        }
        return this;
    }

    // 对比度调整
    contrast(factor) {
        const data = this.imageData.data;
        const intercept = 128 * (1 - factor);

        for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.max(0, Math.min(255, data[i] * factor + intercept));
            data[i + 1] = Math.max(0, Math.min(255, data[i + 1] * factor + intercept));
            data[i + 2] = Math.max(0, Math.min(255, data[i + 2] * factor + intercept));
        }
        return this;
    }

    // 高斯模糊（简化版）
    blur(radius = 1) {
        const data = this.imageData.data;
        const width = this.canvas.width;
        const height = this.canvas.height;
        const output = new Uint8ClampedArray(data.length);

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                let r = 0,
                    g = 0,
                    b = 0,
                    a = 0,
                    count = 0;

                for (let dy = -radius; dy <= radius; dy++) {
                    for (let dx = -radius; dx <= radius; dx++) {
                        const ny = y + dy;
                        const nx = x + dx;

                        if (ny >= 0 && ny < height && nx >= 0 && nx < width) {
                            const idx = (ny * width + nx) * 4;
                            r += data[idx];
                            g += data[idx + 1];
                            b += data[idx + 2];
                            a += data[idx + 3];
                            count++;
                        }
                    }
                }

                const idx = (y * width + x) * 4;
                output[idx] = r / count;
                output[idx + 1] = g / count;
                output[idx + 2] = b / count;
                output[idx + 3] = a / count;
            }
        }

        this.imageData.data.set(output);
        return this;
    }

    // 重置到原始状态
    reset() {
        this.imageData.data.set(this.originalData);
        return this;
    }

    // 应用所有滤镜并渲染
    render() {
        this.ctx.putImageData(this.imageData, 0, 0);
        return this;
    }
}

// 使用示例
const processor = new ImageProcessor(canvas);
processor.loadImage(img).brightness(1.2).contrast(1.1).blur(2).render();
```

### 案例 4：3D 粒子系统优化

```javascript
class OptimizedParticleSystem {
    constructor(maxParticles = 10000) {
        this.maxParticles = maxParticles;

        // 使用结构化数组（SoA - Structure of Arrays）
        // 而不是数组结构（AoS - Array of Structures）
        this.positions = new Float32Array(maxParticles * 3); // x, y, z
        this.velocities = new Float32Array(maxParticles * 3); // vx, vy, vz
        this.colors = new Float32Array(maxParticles * 4); // r, g, b, a
        this.lifetimes = new Float32Array(maxParticles); // 生命周期
        this.sizes = new Float32Array(maxParticles); // 粒子大小

        // 活跃粒子索引池
        this.activeParticles = new Uint32Array(maxParticles);
        this.freeParticles = new Uint32Array(maxParticles);

        // 初始化索引池
        for (let i = 0; i < maxParticles; i++) {
            this.freeParticles[i] = i;
        }

        this.activeCount = 0;
        this.freeCount = maxParticles;

        // WebGL缓冲区数据
        this.vertexData = new Float32Array(maxParticles * 4 * 7); // 每个粒子4个顶点，每个顶点7个属性
    }

    emit(position, velocity, color, lifetime, size) {
        if (this.freeCount === 0) return false;

        // 从空闲池中获取索引
        const index = this.freeParticles[--this.freeCount];
        this.activeParticles[this.activeCount++] = index;

        // 设置粒子属性
        const pos3 = index * 3;
        const col4 = index * 4;

        this.positions[pos3] = position.x;
        this.positions[pos3 + 1] = position.y;
        this.positions[pos3 + 2] = position.z;

        this.velocities[pos3] = velocity.x;
        this.velocities[pos3 + 1] = velocity.y;
        this.velocities[pos3 + 2] = velocity.z;

        this.colors[col4] = color.r;
        this.colors[col4 + 1] = color.g;
        this.colors[col4 + 2] = color.b;
        this.colors[col4 + 3] = color.a;

        this.lifetimes[index] = lifetime;
        this.sizes[index] = size;

        return true;
    }

    update(deltaTime) {
        let writeIndex = 0;

        // 更新所有活跃粒子
        for (let i = 0; i < this.activeCount; i++) {
            const index = this.activeParticles[i];
            const pos3 = index * 3;

            // 更新生命周期
            this.lifetimes[index] -= deltaTime;

            if (this.lifetimes[index] > 0) {
                // 粒子仍然活跃，更新位置
                this.positions[pos3] += this.velocities[pos3] * deltaTime;
                this.positions[pos3 + 1] += this.velocities[pos3 + 1] * deltaTime;
                this.positions[pos3 + 2] += this.velocities[pos3 + 2] * deltaTime;

                // 应用重力
                this.velocities[pos3 + 1] -= 9.8 * deltaTime;

                // 保持在活跃列表中
                this.activeParticles[writeIndex++] = index;
            } else {
                // 粒子死亡，回收到空闲池
                this.freeParticles[this.freeCount++] = index;
            }
        }

        this.activeCount = writeIndex;
    }

    // 生成WebGL顶点数据
    generateVertexData() {
        let vertexIndex = 0;

        for (let i = 0; i < this.activeCount; i++) {
            const index = this.activeParticles[i];
            const pos3 = index * 3;
            const col4 = index * 4;

            const x = this.positions[pos3];
            const y = this.positions[pos3 + 1];
            const z = this.positions[pos3 + 2];
            const size = this.sizes[index];

            const r = this.colors[col4];
            const g = this.colors[col4 + 1];
            const b = this.colors[col4 + 2];
            const a = this.colors[col4 + 3];

            // 生成四边形的四个顶点
            const halfSize = size * 0.5;
            const vertices = [
                [-halfSize, -halfSize],
                [halfSize, -halfSize],
                [halfSize, halfSize],
                [-halfSize, halfSize],
            ];

            for (const [dx, dy] of vertices) {
                this.vertexData[vertexIndex++] = x + dx;
                this.vertexData[vertexIndex++] = y + dy;
                this.vertexData[vertexIndex++] = z;
                this.vertexData[vertexIndex++] = r;
                this.vertexData[vertexIndex++] = g;
                this.vertexData[vertexIndex++] = b;
                this.vertexData[vertexIndex++] = a;
            }
        }

        return this.vertexData.subarray(0, vertexIndex);
    }
}
```

## 常用操作方法

### 创建方式

```javascript
// 1. 指定长度
const arr1 = new Float32Array(10);

// 2. 从普通数组
const arr2 = new Float32Array([1, 2, 3, 4]);

// 3. 从ArrayBuffer
const buffer = new ArrayBuffer(16);
const arr3 = new Float32Array(buffer);

// 4. 从另一个类型化数组
const arr4 = new Float32Array(arr2);

// 5. 从ArrayBuffer的特定区域
const arr5 = new Float32Array(buffer, 4, 2); // 从偏移4开始，长度为2
```

### 数据操作

```javascript
const arr = new Float32Array([1, 2, 3, 4, 5]);

// 基本属性
console.log(arr.length); // 5
console.log(arr.byteLength); // 20 (5 * 4字节)
console.log(arr.byteOffset); // 0
console.log(arr.BYTES_PER_ELEMENT); // 4

// 子数组（共享内存）
const subArray = arr.subarray(1, 4); // [2, 3, 4]
subArray[0] = 10; // 修改会影响原数组
console.log(arr); // [1, 10, 3, 4, 5]

// 复制数据
const copy = new Float32Array(arr);
arr.set([100, 200], 2); // 从索引2开始设置值 [1, 10, 100, 200, 5]

// 填充
arr.fill(0); // 全部填充为0
arr.fill(1, 2, 4); // 从索引2到4填充为1

// 查找
const index = arr.indexOf(1);
const hasValue = arr.includes(1);

// 迭代
arr.forEach((value, index) => console.log(index, value));
for (const value of arr) console.log(value);
```

### 高级操作

```javascript
// 排序
const unsorted = new Float32Array([3.14, 1.41, 2.71, 0.57]);
unsorted.sort(); // 原地排序
console.log(unsorted); // [0.57, 1.41, 2.71, 3.14]

// 自定义排序
unsorted.sort((a, b) => b - a); // 降序

// 函数式方法（ES2015+）
const numbers = new Int32Array([1, 2, 3, 4, 5]);
const doubled = numbers.map((x) => x * 2);
const evens = numbers.filter((x) => x % 2 === 0);
const sum = numbers.reduce((acc, val) => acc + val, 0);

// 类型转换
const floats = new Float32Array([1.1, 2.9, 3.7]);
const ints = new Int32Array(floats); // [1, 2, 3] - 自动截断
```

## 调试和诊断技巧

### 内存使用分析

```javascript
function analyzeTypedArray(arr) {
    console.log(`类型: ${arr.constructor.name}`);
    console.log(`长度: ${arr.length} 元素`);
    console.log(`内存占用: ${arr.byteLength} 字节`);
    console.log(`每元素字节: ${arr.BYTES_PER_ELEMENT}`);
    console.log(`缓冲区大小: ${arr.buffer.byteLength} 字节`);
    console.log(`偏移量: ${arr.byteOffset} 字节`);

    // 检查是否是视图
    if (arr.byteOffset > 0 || arr.byteLength < arr.buffer.byteLength) {
        console.log('⚠️ 这是一个ArrayBuffer的视图');
    }
}

// 使用示例
const buffer = new ArrayBuffer(100);
const view = new Float32Array(buffer, 20, 10);
analyzeTypedArray(view);
```

### 性能监控

```javascript
class TypedArrayProfiler {
    static profile(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`${name}: ${(end - start).toFixed(2)}ms`);
        return result;
    }

    static memoryUsage() {
        if (performance.memory) {
            const { usedJSHeapSize, totalJSHeapSize } = performance.memory;
            console.log(`内存使用: ${(usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
            console.log(`总内存: ${(totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
        }
    }

    static compareArrayTypes(size = 1000000) {
        console.log(`比较数组类型性能 (${size} 元素):`);

        // 普通数组
        this.profile('普通数组创建', () => {
            const arr = new Array(size);
            for (let i = 0; i < size; i++) arr[i] = Math.random();
            return arr;
        });

        // Float32Array
        this.profile('Float32Array创建', () => {
            const arr = new Float32Array(size);
            for (let i = 0; i < size; i++) arr[i] = Math.random();
            return arr;
        });

        // 内存使用情况
        this.memoryUsage();
    }
}
```

## 兼容性和最佳实践

### 浏览器兼容性检查

```javascript
function checkTypedArraySupport() {
    const support = {
        ArrayBuffer: typeof ArrayBuffer !== 'undefined',
        Int8Array: typeof Int8Array !== 'undefined',
        Uint8Array: typeof Uint8Array !== 'undefined',
        Uint8ClampedArray: typeof Uint8ClampedArray !== 'undefined',
        Int16Array: typeof Int16Array !== 'undefined',
        Uint16Array: typeof Uint16Array !== 'undefined',
        Int32Array: typeof Int32Array !== 'undefined',
        Uint32Array: typeof Uint32Array !== 'undefined',
        Float32Array: typeof Float32Array !== 'undefined',
        Float64Array: typeof Float64Array !== 'undefined',
        BigInt64Array: typeof BigInt64Array !== 'undefined',
        BigUint64Array: typeof BigUint64Array !== 'undefined',
        DataView: typeof DataView !== 'undefined',
    };

    console.log('类型化数组支持情况:', support);
    return support;
}

// Polyfill示例（简化版）
if (!window.Float32Array) {
    window.Float32Array = function (data) {
        if (typeof data === 'number') {
            this.length = data;
            for (let i = 0; i < data; i++) this[i] = 0;
        } else if (Array.isArray(data)) {
            this.length = data.length;
            for (let i = 0; i < data.length; i++) this[i] = +data[i];
        }
    };
}
```

### 错误处理和边界检查

```javascript
class SafeTypedArray {
    constructor(type, size) {
        try {
            this.array = new type(size);
            this.type = type.name;
        } catch (error) {
            console.error(`创建${type.name}失败:`, error);
            throw error;
        }
    }

    safeSet(index, value) {
        if (index < 0 || index >= this.array.length) {
            throw new RangeError(`索引${index}超出范围[0, ${this.array.length - 1}]`);
        }

        // 类型检查
        if (typeof value !== 'number') {
            console.warn(`值${value}不是数字，将被转换`);
        }

        this.array[index] = value;

        // 检查是否发生了意外的类型转换
        if (this.array[index] !== value) {
            console.warn(`值从${value}转换为${this.array[index]}`);
        }
    }

    safeGet(index) {
        if (index < 0 || index >= this.array.length) {
            throw new RangeError(`索引${index}超出范围[0, ${this.array.length - 1}]`);
        }
        return this.array[index];
    }
}

// 使用示例
const safeArray = new SafeTypedArray(Int8Array, 10);
safeArray.safeSet(0, 127); // 正常
safeArray.safeSet(1, 128); // 警告：值被截断为-128
// safeArray.safeSet(10, 1); // 抛出RangeError
```

## 总结与选择建议

### 快速选择指南

| 需求场景       | 推荐类型                     | 理由               |
| -------------- | ---------------------------- | ------------------ |
| WebGL 顶点坐标 | Float32Array                 | GPU 友好，精度足够 |
| WebGL 顶点索引 | Uint16Array/Uint32Array      | 根据顶点数量选择   |
| 图像像素数据   | Uint8Array/Uint8ClampedArray | 像素值范围 0-255   |
| 音频采样       | Float32Array                 | 音频 API 标准格式  |
| 科学计算       | Float64Array                 | 高精度需求         |
| 大整数 ID      | BigInt64Array                | 超出 32 位范围     |
| 网络协议       | Uint8Array                   | 字节级操作         |
| 通用数据处理   | Array                        | 灵活性最高         |

### 性能优化清单

✅ **推荐做法**:

-   预分配固定大小的缓冲区
-   使用合适的数据类型（不要过度精确）
-   批量操作而非逐个元素操作
-   重用 ArrayBuffer 避免频繁分配
-   使用 subarray()而非 slice()来避免复制
-   在 WebGL 中直接使用类型化数组

❌ **避免做法**:

-   频繁创建和销毁大型类型化数组
-   在类型化数组和普通数组间频繁转换
-   使用过大的数据类型（如用 Float64 代替 Float32）
-   忽略字节序问题（跨平台时）
-   在不需要时使用类型化数组

### 学习路径建议

1. **基础阶段**: 理解 ArrayBuffer、DataView 和基本类型化数组
2. **应用阶段**: 在 WebGL 项目中实践顶点数据处理
3. **优化阶段**: 学习内存管理和性能优化技巧
4. **高级阶段**: 掌握复杂场景如粒子系统、音频处理等

类型化数组是现代 JavaScript 中处理二进制数据和高性能计算的重要工具。掌握其特性和使用场景，能够显著提升 Web 应用在图形、音频、图像处理等领域的性能表现。在 WebGL 开发中，熟练使用类型化数组更是必不可少的技能。
const arr2 = new Float32Array([1, 2, 3, 4]);

// 3. 从 ArrayBuffer
const buffer = new ArrayBuffer(16);
const arr3 = new Float32Array(buffer);

// 4. 从另一个类型化数组
const arr4 = new Float32Array(arr2);

````

### 数据操作

```javascript
const arr = new Float32Array([1, 2, 3, 4, 5]);

// 基本操作
console.log(arr.length); // 5
console.log(arr.byteLength); // 20 (5 * 4字节)
console.log(arr.byteOffset); // 0

// 子数组
const subArray = arr.subarray(1, 4); // [2, 3, 4]

// 复制数据
const copy = new Float32Array(arr);
arr.set([10, 20], 2); // 从索引2开始设置值

// 填充
arr.fill(0); // 全部填充为0
````

## 最佳实践

### 1. 选择合适的类型

```javascript
// 顶点坐标 - 使用Float32Array
const positions = new Float32Array(vertexData);

// 顶点索引 - 根据顶点数量选择
const indices = vertexCount < 65536 ? new Uint16Array(indexData) : new Uint32Array(indexData);

// 像素数据 - 使用Uint8Array
const pixels = new Uint8Array(imageData);
```

### 2. 内存管理

```javascript
// 预分配大小以避免重新分配
const maxVertices = 10000;
const vertexBuffer = new Float32Array(maxVertices * 3);

// 使用子数组避免复制
const currentVertices = vertexBuffer.subarray(0, actualVertexCount * 3);
```

### 3. WebGL 缓冲区绑定

```javascript
// 创建并绑定缓冲区
const buffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
```

## 注意事项

1. **字节序**: 类型化数组使用平台的字节序，在跨平台数据交换时需要注意
2. **边界检查**: 访问超出范围的索引会返回 undefined，不会抛出错误
3. **类型转换**: 赋值时会自动进行类型转换和截断
4. **共享内存**: 多个视图可以共享同一个 ArrayBuffer，修改会相互影响

## 总结

类型化数组是 JavaScript 中处理二进制数据的强大工具，特别适合 WebGL、音频处理等需要高性能数据操作的场景。选择合适的类型化数组类型，合理管理内存，可以显著提升应用程序的性能。在 WebGL 开发中，熟练掌握类型化数组的使用是必不可少的技能。
