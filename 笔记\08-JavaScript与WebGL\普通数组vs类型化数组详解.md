# 普通 JavaScript 数组 vs 类型化数组详解

## 核心差异总结

### 普通数组（Array）

-   **每个元素**：24-32 字节（对象引用 + 数字对象）
-   **GPU 传输**：❌ 不能直接传输，需要转换
-   **内存布局**：非连续，存储对象引用
-   **类型**：动态类型，可存储任意值

### 类型化数组（TypedArray）

-   **每个元素**：1-8 字节（原始数据）
-   **GPU 传输**：✅ 可以直接传输
-   **内存布局**：连续的二进制数据
-   **类型**：固定类型，只能存储特定数值

## 详细内存占用分析

### 普通数组的内存结构

```javascript
// 普通数组：每个数字都是一个JavaScript对象
const normalArray = [1.0, 2.0, 3.0, 4.0];

// 内存结构（简化）：
// normalArray -> [ref1, ref2, ref3, ref4]
//                  ↓     ↓     ↓     ↓
//               {value:1.0} {value:2.0} {value:3.0} {value:4.0}
//               (24-32字节) (24-32字节) (24-32字节) (24-32字节)

// 总内存：4个引用(32字节) + 4个数字对象(96-128字节) = 128-160字节
```

### 类型化数组的内存结构

```javascript
// 类型化数组：连续的二进制数据
const typedArray = new Float32Array([1.0, 2.0, 3.0, 4.0]);

// 内存结构：
// typedArray -> ArrayBuffer: [00 00 80 3F][00 00 00 40][00 00 40 40][00 00 80 40]
//                            (4字节)     (4字节)     (4字节)     (4字节)

// 总内存：16字节 + 少量对象开销 ≈ 20字节
```

## 内存效率对比实例

### 存储 1000 个浮点数的内存占用

```javascript
// 普通数组
const normalArray = new Array(1000);
for (let i = 0; i < 1000; i++) {
    normalArray[i] = Math.random();
}
// 内存占用：约 25-30KB

// 类型化数组
const typedArray = new Float32Array(1000);
for (let i = 0; i < 1000; i++) {
    typedArray[i] = Math.random();
}
// 内存占用：约 4KB

// 内存节省：85-87%
```

## GPU 传输能力对比

### 普通数组：不能直接传输

```javascript
const positions = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]; // 普通数组

// ❌ 这样做会报错
// gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);
// TypeError: Failed to execute 'bufferData' on 'WebGLRenderingContext'

// ✅ 必须先转换为类型化数组
const typedPositions = new Float32Array(positions);
gl.bufferData(gl.ARRAY_BUFFER, typedPositions, gl.STATIC_DRAW);
```

### 类型化数组：可以直接传输

```javascript
const positions = new Float32Array([1.0, 2.0, 3.0, 4.0, 5.0, 6.0]);

// ✅ 直接传输到GPU
gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);
```

## 为什么普通数组不能直接传输到 GPU？

### 1. 数据格式不兼容

```javascript
// 普通数组的内部表示
const arr = [1, 2, 3];
// JavaScript引擎内部：
// arr[0] -> Number对象 -> IEEE 754双精度 -> 8字节
// arr[1] -> Number对象 -> IEEE 754双精度 -> 8字节
// arr[2] -> Number对象 -> IEEE 754双精度 -> 8字节

// GPU期望的格式：
// 连续的二进制数据：[01 00 00 00][02 00 00 00][03 00 00 00]
```

### 2. 内存布局不连续

```javascript
// 普通数组：引用分散在内存中
const normalArray = [1, 2, 3];
// 内存地址可能是：0x1000, 0x2000, 0x3000（不连续）

// 类型化数组：数据连续存储
const typedArray = new Int32Array([1, 2, 3]);
// 内存地址：0x1000, 0x1004, 0x1008（连续）
```

## 转换方法和性能影响

### 普通数组转类型化数组

```javascript
// 方法1：构造函数转换（推荐）
const normalArray = [1, 2, 3, 4, 5];
const typedArray = new Float32Array(normalArray);

// 方法2：手动复制
const typedArray2 = new Float32Array(normalArray.length);
for (let i = 0; i < normalArray.length; i++) {
    typedArray2[i] = normalArray[i];
}

// 方法3：使用from方法
const typedArray3 = Float32Array.from(normalArray);
```

### 性能测试对比

```javascript
function performanceTest() {
    const size = 100000;

    // 测试1：普通数组创建和转换
    console.time('普通数组创建');
    const normalArray = new Array(size);
    for (let i = 0; i < size; i++) {
        normalArray[i] = Math.random();
    }
    console.timeEnd('普通数组创建');

    console.time('转换为类型化数组');
    const converted = new Float32Array(normalArray);
    console.timeEnd('转换为类型化数组');

    // 测试2：直接创建类型化数组
    console.time('直接创建类型化数组');
    const typedArray = new Float32Array(size);
    for (let i = 0; i < size; i++) {
        typedArray[i] = Math.random();
    }
    console.timeEnd('直接创建类型化数组');
}

// 典型结果：
// 普通数组创建: 15-25ms
// 转换为类型化数组: 5-10ms
// 直接创建类型化数组: 3-8ms
```

## 实际应用场景对比

### 数据处理阶段：使用普通数组

```javascript
// 复杂的数据处理，需要灵活性
const vertices = [];
const normals = [];
const uvs = [];

// 复杂的几何体生成算法
for (let i = 0; i < complexity; i++) {
    const vertex = calculateVertex(i);
    const normal = calculateNormal(vertex);
    const uv = calculateUV(vertex);

    vertices.push(vertex.x, vertex.y, vertex.z);
    normals.push(normal.x, normal.y, normal.z);
    uvs.push(uv.u, uv.v);
}

// 可以使用数组的高级方法
const filteredVertices = vertices.filter((v, i) => i % 3 === 0 && v > 0);
const transformedVertices = vertices.map((v) => v * scale);
```

### GPU 传输阶段：转换为类型化数组

```javascript
// 转换为GPU可用的格式
const vertexBuffer = new Float32Array(vertices);
const normalBuffer = new Float32Array(normals);
const uvBuffer = new Float32Array(uvs);

// 传输到GPU
gl.bufferData(gl.ARRAY_BUFFER, vertexBuffer, gl.STATIC_DRAW);
```

## 最佳实践建议

### 1. 数据处理阶段

```javascript
// ✅ 使用普通数组进行复杂操作
const processedData = rawData
    .filter((item) => item.isValid)
    .map((item) => transformItem(item))
    .sort((a, b) => a.priority - b.priority);
```

### 2. GPU 传输阶段

```javascript
// ✅ 转换为类型化数组传输
const gpuData = new Float32Array(processedData.flatMap((item) => [item.x, item.y, item.z]));
gl.bufferData(gl.ARRAY_BUFFER, gpuData, gl.STATIC_DRAW);
```

### 3. 性能优化策略

```javascript
// ✅ 预分配类型化数组避免转换开销
class VertexBuffer {
    constructor(maxVertices) {
        this.data = new Float32Array(maxVertices * 3);
        this.count = 0;
    }

    addVertex(x, y, z) {
        const index = this.count * 3;
        this.data[index] = x;
        this.data[index + 1] = y;
        this.data[index + 2] = z;
        this.count++;
    }

    upload() {
        // 只传输实际使用的部分
        const actualData = this.data.subarray(0, this.count * 3);
        gl.bufferData(gl.ARRAY_BUFFER, actualData, gl.STATIC_DRAW);
    }
}
```

## 总结

| 特性         | 普通数组               | 类型化数组         |
| ------------ | ---------------------- | ------------------ |
| **内存效率** | 低（25-30 倍开销）     | 高（原始数据大小） |
| **GPU 传输** | 不支持（需转换）       | 直接支持           |
| **数据操作** | 灵活（filter, map 等） | 基础（索引访问）   |
| **类型安全** | 动态类型               | 固定类型           |
| **适用阶段** | 数据处理               | GPU 传输           |

**关键理解：**

-   普通数组适合数据处理阶段的复杂操作
-   类型化数组是 GPU 传输的必需格式
-   合理的工作流程是：普通数组处理 → 转换 → 类型化数组传输
