<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VAO vs 传统方式对比示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .method {
            flex: 1;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        
        .method.traditional {
            border-color: #ff6b6b;
        }
        
        .method.vao {
            border-color: #4ecdc4;
        }
        
        .method h3 {
            margin-top: 0;
            text-align: center;
        }
        
        .traditional h3 {
            color: #ff6b6b;
        }
        
        .vao h3 {
            color: #4ecdc4;
        }
        
        canvas {
            border: 2px solid #333;
            display: block;
            margin: 10px auto;
            background-color: #000;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .performance {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        
        .pros-cons {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        
        .pros, .cons {
            flex: 1;
            padding: 10px;
            border-radius: 4px;
        }
        
        .pros {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .cons {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>VAO vs 传统顶点属性设置对比</h1>
        
        <div class="comparison">
            <div class="method traditional">
                <h3>🔄 传统方式</h3>
                <canvas id="traditionalCanvas" width="300" height="200"></canvas>
                
                <div class="code-block">
// 每次渲染都需要设置
positionBuffer.bind();
gl.enableVertexAttribArray(posLoc);
gl.vertexAttribPointer(posLoc, 3, gl.FLOAT, false, 0, 0);

colorBuffer.bind();
gl.enableVertexAttribArray(colorLoc);
gl.vertexAttribPointer(colorLoc, 4, gl.FLOAT, false, 0, 0);

gl.drawArrays(gl.TRIANGLES, 0, 3);
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <h4>✅ 优点</h4>
                        <ul>
                            <li>简单直接</li>
                            <li>兼容性好</li>
                            <li>不需要额外对象</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <h4>❌ 缺点</h4>
                        <ul>
                            <li>每次都要重新设置</li>
                            <li>状态切换开销大</li>
                            <li>代码重复</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="method vao">
                <h3>🚀 VAO方式</h3>
                <canvas id="vaoCanvas" width="300" height="200"></canvas>
                
                <div class="code-block">
// 一次性配置VAO
const vao = gl.createVertexArray();
gl.bindVertexArray(vao);
// ... 设置所有属性 ...
gl.bindVertexArray(null);

// 渲染时只需要
gl.bindVertexArray(vao);
gl.drawArrays(gl.TRIANGLES, 0, 3);
                </div>
                
                <div class="pros-cons">
                    <div class="pros">
                        <h4>✅ 优点</h4>
                        <ul>
                            <li>性能更好</li>
                            <li>状态封装</li>
                            <li>代码简洁</li>
                        </ul>
                    </div>
                    <div class="cons">
                        <h4>❌ 缺点</h4>
                        <ul>
                            <li>需要WebGL2或扩展</li>
                            <li>额外内存开销</li>
                            <li>概念稍复杂</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="controls">
            <button id="startTest">开始性能测试</button>
            <button id="stopTest">停止测试</button>
        </div>
        
        <div class="performance">
            <h3>📊 性能对比结果</h3>
            <div id="performanceResults">
                点击"开始性能测试"查看两种方式的性能差异...
            </div>
        </div>
        
        <div class="performance">
            <h3>🔍 详细分析</h3>
            <h4>状态切换次数对比：</h4>
            <ul>
                <li><strong>传统方式</strong>：每次渲染需要 4-6 次 WebGL 调用</li>
                <li><strong>VAO方式</strong>：每次渲染只需要 1-2 次 WebGL 调用</li>
            </ul>
            
            <h4>内存使用对比：</h4>
            <ul>
                <li><strong>传统方式</strong>：无额外内存开销</li>
                <li><strong>VAO方式</strong>：每个VAO约占用几KB GPU内存</li>
            </ul>
            
            <h4>适用场景：</h4>
            <ul>
                <li><strong>传统方式</strong>：简单场景、兼容性要求高</li>
                <li><strong>VAO方式</strong>：复杂场景、多对象渲染、性能敏感</li>
            </ul>
        </div>
    </div>

    <script>
        // 顶点着色器（支持位置和颜色）
        const vertexShaderSource = `
            attribute vec3 a_position;
            attribute vec4 a_color;
            varying vec4 v_color;
            void main() {
                gl_Position = vec4(a_position, 1.0);
                v_color = a_color;
            }
        `;

        // 片段着色器
        const fragmentShaderSource = `
            precision mediump float;
            varying vec4 v_color;
            void main() {
                gl_FragColor = v_color;
            }
        `;

        // 创建着色器
        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            
            if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
                gl.deleteShader(shader);
                return null;
            }
            
            return shader;
        }

        // 创建着色器程序
        function createShaderProgram(gl, vertexSource, fragmentSource) {
            const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexSource);
            const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentSource);
            
            const program = gl.createProgram();
            gl.attachShader(program, vertexShader);
            gl.attachShader(program, fragmentShader);
            gl.linkProgram(program);
            
            if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                console.error('程序链接错误:', gl.getProgramInfoLog(program));
                return null;
            }
            
            return program;
        }

        // 传统方式渲染器
        class TraditionalRenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.gl = canvas.getContext('webgl');
                this.init();
            }

            init() {
                const gl = this.gl;
                
                // 创建着色器程序
                this.program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);
                
                // 顶点数据
                const positions = new Float32Array([
                    0.0, 0.5, 0.0,
                    -0.5, -0.5, 0.0,
                    0.5, -0.5, 0.0
                ]);
                
                const colors = new Float32Array([
                    1.0, 0.0, 0.0, 1.0,  // 红色
                    0.0, 1.0, 0.0, 1.0,  // 绿色
                    0.0, 0.0, 1.0, 1.0   // 蓝色
                ]);
                
                // 创建缓冲区
                this.positionBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);
                
                this.colorBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);
                
                // 获取属性位置
                this.positionLocation = gl.getAttribLocation(this.program, 'a_position');
                this.colorLocation = gl.getAttribLocation(this.program, 'a_color');
                
                // 设置视口
                gl.viewport(0, 0, canvas.width, canvas.height);
                gl.clearColor(0.1, 0.1, 0.1, 1.0);
                
                this.render();
            }

            render() {
                const gl = this.gl;
                
                gl.clear(gl.COLOR_BUFFER_BIT);
                gl.useProgram(this.program);
                
                // 🔄 传统方式：每次都要重新设置所有属性
                // 设置位置属性
                gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
                gl.enableVertexAttribArray(this.positionLocation);
                gl.vertexAttribPointer(this.positionLocation, 3, gl.FLOAT, false, 0, 0);
                
                // 设置颜色属性
                gl.bindBuffer(gl.ARRAY_BUFFER, this.colorBuffer);
                gl.enableVertexAttribArray(this.colorLocation);
                gl.vertexAttribPointer(this.colorLocation, 4, gl.FLOAT, false, 0, 0);
                
                gl.drawArrays(gl.TRIANGLES, 0, 3);
            }

            // 性能测试方法
            performanceTest(iterations = 1000) {
                const startTime = performance.now();
                
                for (let i = 0; i < iterations; i++) {
                    this.render();
                }
                
                const endTime = performance.now();
                return endTime - startTime;
            }
        }

        // VAO方式渲染器
        class VAORenderer {
            constructor(canvas) {
                this.canvas = canvas;
                this.gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
                
                // 检查VAO支持
                if (!this.gl.createVertexArray) {
                    const ext = this.gl.getExtension('OES_vertex_array_object');
                    if (ext) {
                        this.gl.createVertexArray = ext.createVertexArrayOES.bind(ext);
                        this.gl.bindVertexArray = ext.bindVertexArrayOES.bind(ext);
                        this.gl.deleteVertexArray = ext.deleteVertexArrayOES.bind(ext);
                    } else {
                        console.warn('VAO not supported, falling back to traditional method');
                        return;
                    }
                }
                
                this.init();
            }

            init() {
                const gl = this.gl;
                
                // 创建着色器程序
                this.program = createShaderProgram(gl, vertexShaderSource, fragmentShaderSource);
                
                // 顶点数据
                const positions = new Float32Array([
                    0.0, 0.5, 0.0,
                    -0.5, -0.5, 0.0,
                    0.5, -0.5, 0.0
                ]);
                
                const colors = new Float32Array([
                    1.0, 0.0, 0.0, 1.0,  // 红色
                    0.0, 1.0, 0.0, 1.0,  // 绿色
                    0.0, 0.0, 1.0, 1.0   // 蓝色
                ]);
                
                // 🚀 VAO方式：一次性配置所有状态
                this.vao = gl.createVertexArray();
                gl.bindVertexArray(this.vao);
                
                // 创建并配置位置缓冲区
                const positionBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);
                
                const positionLocation = gl.getAttribLocation(this.program, 'a_position');
                gl.enableVertexAttribArray(positionLocation);
                gl.vertexAttribPointer(positionLocation, 3, gl.FLOAT, false, 0, 0);
                
                // 创建并配置颜色缓冲区
                const colorBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);
                
                const colorLocation = gl.getAttribLocation(this.program, 'a_color');
                gl.enableVertexAttribArray(colorLocation);
                gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);
                
                // 解绑VAO
                gl.bindVertexArray(null);
                
                // 设置视口
                gl.viewport(0, 0, canvas.width, canvas.height);
                gl.clearColor(0.1, 0.1, 0.1, 1.0);
                
                this.render();
            }

            render() {
                const gl = this.gl;
                
                gl.clear(gl.COLOR_BUFFER_BIT);
                gl.useProgram(this.program);
                
                // 🚀 VAO方式：只需要一行代码就恢复所有状态
                gl.bindVertexArray(this.vao);
                gl.drawArrays(gl.TRIANGLES, 0, 3);
            }

            // 性能测试方法
            performanceTest(iterations = 1000) {
                const startTime = performance.now();
                
                for (let i = 0; i < iterations; i++) {
                    this.render();
                }
                
                const endTime = performance.now();
                return endTime - startTime;
            }
        }

        // 初始化示例
        let traditionalRenderer, vaoRenderer;
        let isTestRunning = false;

        window.addEventListener('load', () => {
            const traditionalCanvas = document.getElementById('traditionalCanvas');
            const vaoCanvas = document.getElementById('vaoCanvas');
            
            traditionalRenderer = new TraditionalRenderer(traditionalCanvas);
            vaoRenderer = new VAORenderer(vaoCanvas);
            
            // 设置性能测试按钮
            document.getElementById('startTest').addEventListener('click', startPerformanceTest);
            document.getElementById('stopTest').addEventListener('click', stopPerformanceTest);
        });

        function startPerformanceTest() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            const resultsElement = document.getElementById('performanceResults');
            resultsElement.innerHTML = '🔄 正在进行性能测试...';
            
            // 使用setTimeout避免阻塞UI
            setTimeout(() => {
                const iterations = 1000;
                
                const traditionalTime = traditionalRenderer.performanceTest(iterations);
                const vaoTime = vaoRenderer ? vaoRenderer.performanceTest(iterations) : 0;
                
                const improvement = vaoTime > 0 ? ((traditionalTime - vaoTime) / traditionalTime * 100).toFixed(1) : 0;
                
                resultsElement.innerHTML = `
                    <h4>测试结果 (${iterations} 次渲染):</h4>
                    <p><strong>传统方式</strong>: ${traditionalTime.toFixed(2)} ms</p>
                    <p><strong>VAO方式</strong>: ${vaoTime.toFixed(2)} ms</p>
                    <p><strong>性能提升</strong>: ${improvement}%</p>
                    <p><em>注意：实际性能差异在复杂场景中更明显</em></p>
                `;
                
                isTestRunning = false;
            }, 100);
        }

        function stopPerformanceTest() {
            isTestRunning = false;
        }
    </script>
</body>
</html>
