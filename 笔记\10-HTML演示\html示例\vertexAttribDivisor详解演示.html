<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>vertexAttribDivisor 详解演示</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
            }
            .demo-section {
                background: white;
                margin: 20px 0;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .demo-title {
                color: #333;
                border-bottom: 2px solid #4caf50;
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            canvas {
                border: 1px solid #ddd;
                margin: 10px;
                background: #f9f9f9;
            }
            .controls {
                margin: 15px 0;
            }
            button {
                background: #4caf50;
                color: white;
                border: none;
                padding: 8px 16px;
                margin: 5px;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background: #45a049;
            }
            .explanation {
                background: #e8f5e8;
                padding: 15px;
                border-left: 4px solid #4caf50;
                margin: 15px 0;
            }
            .code-block {
                background: #f4f4f4;
                padding: 15px;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                margin: 10px 0;
                overflow-x: auto;
            }
            .highlight {
                background: #ffeb3b;
                padding: 2px 4px;
                border-radius: 2px;
            }
            .grid {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 20px;
                margin: 20px 0;
            }
            .demo-card {
                background: white;
                padding: 15px;
                border-radius: 8px;
                border: 2px solid #ddd;
            }
            .demo-card.active {
                border-color: #4caf50;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>WebGL vertexAttribDivisor 详解演示</h1>

            <div id="webgl-info" class="explanation" style="background: #e3f2fd; border-left-color: #2196f3">
                <h3>🔧 WebGL 支持状态</h3>
                <p id="webgl-status">正在检测WebGL支持...</p>
            </div>

            <div class="explanation">
                <h3>🎯 核心概念</h3>
                <p><strong>vertexAttribDivisor</strong> 控制属性数据在实例化渲染中的"共享规则"：</p>
                <ul>
                    <li><span class="highlight">divisor = 0</span>: 每个顶点使用不同值（普通顶点属性）</li>
                    <li><span class="highlight">divisor = 1</span>: 每个实例使用不同值（实例属性）</li>
                    <li><span class="highlight">divisor = n</span>: 每n个实例共享一个值（分组属性）</li>
                </ul>
            </div>

            <div class="grid">
                <div class="demo-card" id="demo0">
                    <h3>🔹 Divisor = 0 (顶点模式)</h3>
                    <canvas id="canvas0" width="300" height="200"></canvas>
                    <div class="code-block">gl.vertexAttribDivisor(positionLocation, 0); // 每个顶点使用不同的位置数据 // 所有实例形状相同</div>
                    <button onclick="runDemo(0)">运行演示</button>
                </div>

                <div class="demo-card" id="demo1">
                    <h3>🔹 Divisor = 1 (实例模式)</h3>
                    <canvas id="canvas1" width="300" height="200"></canvas>
                    <div class="code-block">gl.vertexAttribDivisor(colorLocation, 1); // 每个实例使用不同的颜色 // 同一实例内所有顶点共享颜色</div>
                    <button onclick="runDemo(1)">运行演示</button>
                </div>

                <div class="demo-card" id="demo2">
                    <h3>🔹 Divisor = 2 (分组模式)</h3>
                    <canvas id="canvas2" width="300" height="200"></canvas>
                    <div class="code-block">gl.vertexAttribDivisor(materialLocation, 2); // 每2个实例共享一个材质 // 实例0,1用材质0，实例2,3用材质1</div>
                    <button onclick="runDemo(2)">运行演示</button>
                </div>
            </div>

            <div class="demo-section">
                <h2 class="demo-title">📊 数据流向对比</h2>
                <div class="explanation">
                    <h4>假设绘制6个三角形实例，每个三角形3个顶点：</h4>
                    <div class="code-block">
                        gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, 6);

                        <strong>Divisor = 0 (顶点属性 - 位置):</strong>
                        实例0: 顶点0→pos[0], 顶点1→pos[1], 顶点2→pos[2] 实例1: 顶点0→pos[0], 顶点1→pos[1], 顶点2→pos[2] (重复) 实例2: 顶点0→pos[0], 顶点1→pos[1], 顶点2→pos[2] (重复)
                        ...所有实例形状相同

                        <strong>Divisor = 1 (实例属性 - 颜色):</strong>
                        实例0: 所有顶点→color[0] 实例1: 所有顶点→color[1] 实例2: 所有顶点→color[2] 实例3: 所有顶点→color[3] 实例4: 所有顶点→color[4] 实例5: 所有顶点→color[5]

                        <strong>Divisor = 2 (分组属性 - 材质):</strong>
                        实例0,1: 所有顶点→material[0] 实例2,3: 所有顶点→material[1] 实例4,5: 所有顶点→material[2]
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h2 class="demo-title">🧮 属性索引计算公式</h2>
                <div class="explanation">
                    <div class="code-block">
                        <strong>通用公式:</strong>
                        属性索引 = Math.floor(当前实例编号 / divisor)

                        <strong>具体示例:</strong>
                        divisor = 1: 实例0→属性[0], 实例1→属性[1], 实例2→属性[2]... divisor = 2: 实例0,1→属性[0], 实例2,3→属性[1], 实例4,5→属性[2]... divisor = 3: 实例0,1,2→属性[0], 实例3,4,5→属性[1],
                        实例6,7,8→属性[2]...
                    </div>
                </div>
            </div>

            <div class="controls">
                <button onclick="runAllDemos()">🚀 运行所有演示</button>
                <button onclick="clearAllCanvas()">🧹 清空画布</button>
            </div>
        </div>

        <script>
            // WebGL 着色器代码
            const vertexShaderSource = `
            attribute vec2 a_position;
            attribute vec3 a_color;
            attribute float a_instanceOffset;
            
            varying vec3 v_color;
            
            void main() {
                vec2 position = a_position;
                position.x += a_instanceOffset;
                gl_Position = vec4(position, 0.0, 1.0);
                v_color = a_color;
            }
        `;

            const fragmentShaderSource = `
            precision mediump float;
            varying vec3 v_color;
            
            void main() {
                gl_FragColor = vec4(v_color, 1.0);
            }
        `;

            // 创建着色器
            function createShader(gl, type, source) {
                const shader = gl.createShader(type);
                gl.shaderSource(shader, source);
                gl.compileShader(shader);

                if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
                    console.error('着色器编译错误:', gl.getShaderInfoLog(shader));
                    gl.deleteShader(shader);
                    return null;
                }

                return shader;
            }

            // 创建程序
            function createProgram(gl, vertexShader, fragmentShader) {
                const program = gl.createProgram();
                gl.attachShader(program, vertexShader);
                gl.attachShader(program, fragmentShader);
                gl.linkProgram(program);

                if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
                    console.error('程序链接错误:', gl.getProgramInfoLog(program));
                    gl.deleteProgram(program);
                    return null;
                }

                return program;
            }

            // 运行演示
            function runDemo(demoType) {
                const canvas = document.getElementById(`canvas${demoType}`);
                // 尝试获取WebGL2上下文，如果失败则尝试WebGL1 + 扩展
                let gl = canvas.getContext('webgl2');
                let isWebGL2 = true;

                if (!gl) {
                    gl = canvas.getContext('webgl');
                    isWebGL2 = false;
                    if (!gl) {
                        alert('WebGL 不支持');
                        return;
                    }

                    // 检查并启用实例化渲染扩展
                    const instancedExt = gl.getExtension('ANGLE_instanced_arrays');
                    if (!instancedExt) {
                        alert('您的浏览器不支持实例化渲染扩展');
                        return;
                    }

                    // 为WebGL1添加实例化函数
                    gl.vertexAttribDivisor = instancedExt.vertexAttribDivisorANGLE.bind(instancedExt);
                    gl.drawArraysInstanced = instancedExt.drawArraysInstancedANGLE.bind(instancedExt);
                }

                // 清空画布
                gl.clearColor(0.9, 0.9, 0.9, 1.0);
                gl.clear(gl.COLOR_BUFFER_BIT);

                // 创建着色器和程序
                const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
                const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
                const program = createProgram(gl, vertexShader, fragmentShader);

                // 获取属性位置
                const positionLocation = gl.getAttribLocation(program, 'a_position');
                const colorLocation = gl.getAttribLocation(program, 'a_color');
                const offsetLocation = gl.getAttribLocation(program, 'a_instanceOffset');

                // 三角形顶点数据
                const positions = new Float32Array([
                    0.0,
                    0.1, // 顶点1
                    -0.05,
                    -0.1, // 顶点2
                    0.05,
                    -0.1, // 顶点3
                ]);

                // 实例偏移数据
                const offsets = new Float32Array([-0.6, -0.2, 0.2, 0.6]);

                // 颜色数据
                const colors = new Float32Array([
                    1.0,
                    0.0,
                    0.0, // 红色
                    0.0,
                    1.0,
                    0.0, // 绿色
                    0.0,
                    0.0,
                    1.0, // 蓝色
                    1.0,
                    1.0,
                    0.0, // 黄色
                    1.0,
                    0.0,
                    1.0, // 紫色
                    0.0,
                    1.0,
                    1.0, // 青色
                ]);

                // 创建缓冲区
                const positionBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, positions, gl.STATIC_DRAW);

                const offsetBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, offsetBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, offsets, gl.STATIC_DRAW);

                const colorBuffer = gl.createBuffer();
                gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
                gl.bufferData(gl.ARRAY_BUFFER, colors, gl.STATIC_DRAW);

                // 使用程序
                gl.useProgram(program);

                // 设置位置属性 (divisor = 0)
                gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
                gl.enableVertexAttribArray(positionLocation);
                gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);
                gl.vertexAttribDivisor(positionLocation, 0);

                // 设置偏移属性 (divisor = 1)
                gl.bindBuffer(gl.ARRAY_BUFFER, offsetBuffer);
                gl.enableVertexAttribArray(offsetLocation);
                gl.vertexAttribPointer(offsetLocation, 1, gl.FLOAT, false, 0, 0);
                gl.vertexAttribDivisor(offsetLocation, 1);

                // 设置颜色属性 (根据演示类型设置不同的divisor)
                gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
                gl.enableVertexAttribArray(colorLocation);
                gl.vertexAttribPointer(colorLocation, 3, gl.FLOAT, false, 0, 0);

                let divisor, instanceCount;
                switch (demoType) {
                    case 0: // divisor = 0 演示
                        divisor = 0;
                        instanceCount = 4;
                        break;
                    case 1: // divisor = 1 演示
                        divisor = 1;
                        instanceCount = 4;
                        break;
                    case 2: // divisor = 2 演示
                        divisor = 2;
                        instanceCount = 4;
                        break;
                }

                gl.vertexAttribDivisor(colorLocation, divisor);

                // 绘制实例
                gl.drawArraysInstanced(gl.TRIANGLES, 0, 3, instanceCount);

                // 高亮当前演示卡片
                document.querySelectorAll('.demo-card').forEach((card) => card.classList.remove('active'));
                document.getElementById(`demo${demoType}`).classList.add('active');
            }

            function runAllDemos() {
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => runDemo(i), i * 500);
                }
            }

            function clearAllCanvas() {
                for (let i = 0; i < 3; i++) {
                    const canvas = document.getElementById(`canvas${i}`);
                    const gl = canvas.getContext('webgl');
                    if (gl) {
                        gl.clearColor(0.9, 0.9, 0.9, 1.0);
                        gl.clear(gl.COLOR_BUFFER_BIT);
                    }
                }
                document.querySelectorAll('.demo-card').forEach((card) => card.classList.remove('active'));
            }

            // 检测WebGL支持状态
            function checkWebGLSupport() {
                const testCanvas = document.createElement('canvas');
                let gl2 = testCanvas.getContext('webgl2');
                let gl1 = testCanvas.getContext('webgl');
                let statusText = '';

                if (gl2) {
                    statusText = '✅ WebGL2 支持 - vertexAttribDivisor 原生支持';
                } else if (gl1) {
                    const instancedExt = gl1.getExtension('ANGLE_instanced_arrays');
                    if (instancedExt) {
                        statusText = '✅ WebGL1 + ANGLE_instanced_arrays 扩展支持';
                    } else {
                        statusText = '❌ WebGL1 支持，但缺少实例化渲染扩展';
                    }
                } else {
                    statusText = '❌ WebGL 不支持';
                }

                document.getElementById('webgl-status').textContent = statusText;
            }

            // 页面加载完成后检测WebGL支持并运行演示
            window.onload = function () {
                checkWebGLSupport();
                setTimeout(runAllDemos, 1000);
            };
        </script>
    </body>
</html>
