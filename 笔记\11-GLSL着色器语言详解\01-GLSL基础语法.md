# GLSL 基础语法详解

## 概述

GLSL (OpenGL Shading Language) 是专门为图形渲染管线设计的着色器编程语言。它基于 C 语言语法，但针对并行计算和向量运算进行了优化。GLSL 在 GPU 上并行执行，为 3D 图形渲染提供了强大的可编程能力。

### GLSL 的特点

-   **并行执行**: 在 GPU 的多个处理单元上同时执行
-   **向量化**: 内置对向量和矩阵的原生支持
-   **硬件优化**: 针对图形硬件特性进行了优化
-   **类型安全**: 强类型系统，编译时类型检查
-   **版本兼容**: 支持多个版本，向后兼容

### GLSL 版本对应关系

| GLSL 版本 | OpenGL 版本 | WebGL 版本 | 主要特性               |
| --------- | ----------- | ---------- | ---------------------- |
| 1.00      | ES 2.0      | WebGL 1.0  | 基础着色器功能         |
| 3.00      | ES 3.0      | WebGL 2.0  | 更多内置函数、整数支持 |
| 1.10      | 2.0         | -          | 桌面版基础功能         |
| 1.20      | 2.1         | -          | 改进的矩阵操作         |

## 1. 基本数据类型

### 1.1 标量类型

#### 浮点数类型 (float)

```glsl
float a = 1.0;              // 基本浮点数
float b = 3.14159;          // 圆周率
float c = 1.5e-3;           // 科学计数法: 0.0015
float d = .5;               // 省略前导零: 0.5
float e = 2.;               // 省略小数部分: 2.0

// 精度限定符 (移动设备重要)
lowp float lowPrecision;    // 低精度 (-2^8 到 2^8)
mediump float medPrecision; // 中精度 (-2^14 到 2^14)
highp float highPrecision;  // 高精度 (-2^62 到 2^62)
```

#### 整数类型 (int) - WebGL 2.0

```glsl
int count = 10;             // 基本整数
int index = 0;              // 数组索引
int negative = -5;          // 负整数
int hex = 0xFF;             // 十六进制: 255
int octal = 077;            // 八进制: 63

// 无符号整数 (WebGL 2.0)
uint positiveOnly = 100u;   // 无符号整数
```

#### 布尔类型 (bool)

```glsl
bool isVisible = true;      // 真值
bool isEnabled = false;     // 假值
bool result = (a > b);      // 比较结果
bool combined = isVisible && isEnabled; // 逻辑运算
```

### 1.2 向量类型

#### 浮点向量

```glsl
// 2D向量 (vec2)
vec2 position = vec2(1.0, 2.0);         // 位置坐标
vec2 texCoord = vec2(0.5, 0.5);         // 纹理坐标
vec2 scale = vec2(2.0);                 // 统一缩放: vec2(2.0, 2.0)
vec2 velocity = vec2(1.0, -0.5);        // 速度向量

// 3D向量 (vec3)
vec3 color = vec3(1.0, 0.0, 0.0);       // RGB红色
vec3 normal = vec3(0.0, 1.0, 0.0);      // Y轴法向量
vec3 worldPos = vec3(10.0, 5.0, -2.0);  // 世界坐标
vec3 lightDir = vec3(-1.0, -1.0, 0.0);  // 光照方向

// 4D向量 (vec4)
vec4 vertex = vec4(1.0, 2.0, 3.0, 1.0);     // 齐次坐标
vec4 finalColor = vec4(1.0, 0.5, 0.2, 1.0); // RGBA颜色
vec4 clipSpace = vec4(0.0, 0.0, 0.0, 1.0);  // 裁剪空间坐标
```

#### 整数向量 (WebGL 2.0)

```glsl
ivec2 imageSize = ivec2(1024, 768);     // 图像尺寸
ivec3 voxelCoord = ivec3(10, 20, 30);   // 体素坐标
ivec4 indices = ivec4(0, 1, 2, 3);      // 索引数组

// 无符号整数向量
uvec2 textureSize = uvec2(512u, 512u);  // 纹理尺寸
uvec3 gridCoord = uvec3(5u, 10u, 15u);  // 网格坐标
```

#### 布尔向量

```glsl
bvec2 comparison2D = bvec2(true, false);    // 2D比较结果
bvec3 comparison3D = bvec3(false, true, true); // 3D比较结果
bvec4 mask = bvec4(true, false, false, true);  // 4D掩码
```

### 1.3 矩阵类型

#### 浮点矩阵

```glsl
// 2x2矩阵 - 2D变换
mat2 rotation2D = mat2(
    cos(angle), -sin(angle),    // 第一列
    sin(angle),  cos(angle)     // 第二列
);
mat2 scale2D = mat2(2.0, 0.0, 0.0, 3.0);   // 缩放矩阵

// 3x3矩阵 - 2D变换 + 平移或3D旋转
mat3 identity3 = mat3(1.0);                 // 单位矩阵
mat3 transform2D = mat3(
    2.0, 0.0, 0.0,  // 缩放X
    0.0, 2.0, 0.0,  // 缩放Y
    1.0, 1.0, 1.0   // 平移XY + 齐次坐标
);

// 4x4矩阵 - 完整3D变换
mat4 modelMatrix = mat4(1.0);               // 模型矩阵
mat4 viewMatrix = mat4(1.0);                // 视图矩阵
mat4 projectionMatrix = mat4(1.0);          // 投影矩阵

// 自定义4x4矩阵
mat4 customMatrix = mat4(
    1.0, 0.0, 0.0, 0.0,  // 第一列
    0.0, 1.0, 0.0, 0.0,  // 第二列
    0.0, 0.0, 1.0, 0.0,  // 第三列
    0.0, 0.0, 0.0, 1.0   // 第四列
);
```

### 1.4 采样器类型

#### 纹理采样器

```glsl
// 2D纹理采样器
sampler2D diffuseTexture;      // 漫反射纹理
sampler2D normalMap;           // 法线贴图
sampler2D shadowMap;           // 阴影贴图

// 立方体纹理采样器
samplerCube environmentMap;    // 环境贴图
samplerCube skybox;            // 天空盒

// 3D纹理采样器 (WebGL 2.0)
sampler3D volumeTexture;       // 体积纹理
sampler2DArray textureArray;   // 纹理数组
```

#### 整数纹理采样器 (WebGL 2.0)

```glsl
isampler2D integerTexture;     // 整数纹理
usampler2D unsignedTexture;    // 无符号整数纹理
```

### 1.5 数组类型

#### 数组声明和初始化

```glsl
// 固定大小数组
float values[4];                        // 声明4个浮点数数组
int indices[3] = int[3](0, 1, 2);      // 声明并初始化
vec3 positions[2] = vec3[2](
    vec3(0.0, 0.0, 0.0),
    vec3(1.0, 1.0, 1.0)
);

// 多维数组 (WebGL 2.0)
float matrix[4][4];                     // 4x4浮点数组
vec2 grid[10][10];                      // 10x10向量网格
```

## 2. 向量分量访问

### 2.1 坐标分量访问

GLSL 提供了多种方式来访问向量的分量，可以根据使用场景选择最合适的命名方式。

```glsl
vec4 position = vec4(1.0, 2.0, 3.0, 4.0);

// 使用 x, y, z, w (位置坐标)
float x = position.x;  // 1.0 - X坐标
float y = position.y;  // 2.0 - Y坐标
float z = position.z;  // 3.0 - Z坐标
float w = position.w;  // 4.0 - W坐标(齐次坐标)

// 使用 r, g, b, a (颜色分量)
vec4 color = vec4(0.8, 0.6, 0.4, 1.0);
float red   = color.r;  // 0.8 - 红色分量
float green = color.g;  // 0.6 - 绿色分量
float blue  = color.b;  // 0.4 - 蓝色分量
float alpha = color.a;  // 1.0 - 透明度分量

// 使用 s, t, p, q (纹理坐标)
vec4 texCoord = vec4(0.5, 0.5, 0.0, 1.0);
float s = texCoord.s;  // 0.5 - S纹理坐标
float t = texCoord.t;  // 0.5 - T纹理坐标
float p = texCoord.p;  // 0.0 - P纹理坐标(3D)
float q = texCoord.q;  // 1.0 - Q纹理坐标(齐次)
```

### 2.2 向量重组 (Swizzling)

向量重组是 GLSL 的强大特性，允许以任意顺序重新排列向量分量。

```glsl
vec4 original = vec4(1.0, 2.0, 3.0, 4.0);

// 基本重组
vec2 xy = original.xy;          // vec2(1.0, 2.0)
vec2 yx = original.yx;          // vec2(2.0, 1.0)
vec3 xyz = original.xyz;        // vec3(1.0, 2.0, 3.0)
vec3 zyx = original.zyx;        // vec3(3.0, 2.0, 1.0)

// 重复分量
vec3 xxx = original.xxx;        // vec3(1.0, 1.0, 1.0)
vec4 xyxy = original.xyxy;      // vec4(1.0, 2.0, 1.0, 2.0)

// 颜色分量重组
vec4 color = vec4(0.8, 0.6, 0.4, 1.0);
vec3 rgb = color.rgb;           // vec3(0.8, 0.6, 0.4)
vec3 bgr = color.bgr;           // vec3(0.4, 0.6, 0.8)
vec4 argb = color.argb;         // vec4(1.0, 0.8, 0.6, 0.4)

// 纹理坐标重组
vec4 texCoord = vec4(0.5, 0.5, 0.0, 1.0);
vec2 st = texCoord.st;          // vec2(0.5, 0.5)
vec2 ts = texCoord.ts;          // vec2(0.5, 0.5)

// 混合不同命名方式 (不推荐，但语法允许)
// vec2 mixed = original.xg;    // 错误！不能混合x和g
```

### 2.3 向量分量赋值

```glsl
vec4 position = vec4(0.0);

// 单个分量赋值
position.x = 1.0;
position.y = 2.0;
position.z = 3.0;
position.w = 4.0;

// 多个分量同时赋值
position.xy = vec2(5.0, 6.0);      // 设置x=5.0, y=6.0
position.zw = vec2(7.0, 8.0);      // 设置z=7.0, w=8.0
position.xyz = vec3(1.0, 2.0, 3.0); // 设置前三个分量

// 重组赋值
vec4 color = vec4(0.0);
color.rgb = vec3(1.0, 0.5, 0.2);   // 设置RGB
color.a = 1.0;                      // 设置透明度
color.bgr = vec3(0.1, 0.2, 0.3);   // 反向设置RGB
```

## 3. 运算符

### 3.1 算术运算符

GLSL 支持标准的算术运算符，可以用于标量、向量和矩阵运算。

```glsl
// 标量运算
float a = 5.0;
float b = 3.0;

float sum = a + b;        // 8.0 - 加法
float diff = a - b;       // 2.0 - 减法
float product = a * b;    // 15.0 - 乘法
float quotient = a / b;   // 1.666... - 除法

// 向量运算 (分量级运算)
vec3 v1 = vec3(1.0, 2.0, 3.0);
vec3 v2 = vec3(4.0, 5.0, 6.0);
vec3 vSum = v1 + v2;      // vec3(5.0, 7.0, 9.0)
vec3 vDiff = v1 - v2;     // vec3(-3.0, -3.0, -3.0)
vec3 vMult = v1 * v2;     // vec3(4.0, 10.0, 18.0)
vec3 vDiv = v1 / v2;      // vec3(0.25, 0.4, 0.5)

// 向量与标量运算
vec3 scaled = v1 * 2.0;   // vec3(2.0, 4.0, 6.0)
vec3 offset = v1 + 1.0;   // vec3(2.0, 3.0, 4.0)

// 矩阵运算
mat3 m1 = mat3(1.0);
mat3 m2 = mat3(2.0);
mat3 mSum = m1 + m2;      // 矩阵加法
mat3 mMult = m1 * m2;     // 矩阵乘法

// 矩阵与向量运算
vec3 transformed = m1 * v1; // 矩阵变换向量
```

### 3.2 比较运算符

比较运算符用于标量值的比较，返回布尔值。

```glsl
float x = 5.0;
float y = 3.0;

bool greater = x > y;         // true - 大于
bool less = x < y;            // false - 小于
bool equal = x == y;          // false - 等于
bool notEqual = x != y;       // true - 不等于
bool greaterEqual = x >= y;   // true - 大于等于
bool lessEqual = x <= y;      // false - 小于等于

// 向量比较 (使用内置函数)
vec3 v1 = vec3(1.0, 2.0, 3.0);
vec3 v2 = vec3(2.0, 1.0, 3.0);
bvec3 greaterThan = greaterThan(v1, v2);  // bvec3(false, true, false)
bvec3 lessThan = lessThan(v1, v2);        // bvec3(true, false, false)
bvec3 equalTo = equal(v1, v2);            // bvec3(false, false, true)
```

### 3.3 逻辑运算符

```glsl
bool a = true;
bool b = false;

bool andResult = a && b;      // false - 逻辑与
bool orResult = a || b;       // true - 逻辑或
bool notResult = !a;          // false - 逻辑非

// 短路求值
bool shortCircuit = false && (1.0/0.0 > 0.0); // false，不会执行除零运算

// 向量逻辑运算 (使用内置函数)
bvec3 mask1 = bvec3(true, false, true);
bvec3 mask2 = bvec3(false, true, true);
bool anyTrue = any(mask1);    // true - 任意分量为真
bool allTrue = all(mask1);    // false - 所有分量为真
bvec3 notMask = not(mask1);   // bvec3(false, true, false)
```

### 3.4 赋值运算符

```glsl
float value = 10.0;

// 基本赋值
value = 5.0;                  // 赋值

// 复合赋值运算符
value += 3.0;                 // value = value + 3.0 = 8.0
value -= 2.0;                 // value = value - 2.0 = 6.0
value *= 2.0;                 // value = value * 2.0 = 12.0
value /= 3.0;                 // value = value / 3.0 = 4.0

// 向量赋值
vec3 position = vec3(0.0);
position += vec3(1.0, 2.0, 3.0);  // vec3(1.0, 2.0, 3.0)
position *= 2.0;                   // vec3(2.0, 4.0, 6.0)

// 矩阵赋值
mat4 transform = mat4(1.0);
transform *= mat4(2.0);            // 矩阵乘法赋值
```

### 3.5 三元运算符

```glsl
float a = 5.0;
float b = 3.0;

// 条件表达式
float max = (a > b) ? a : b;      // 5.0
float min = (a < b) ? a : b;      // 3.0

// 向量条件选择
vec3 color1 = vec3(1.0, 0.0, 0.0);  // 红色
vec3 color2 = vec3(0.0, 1.0, 0.0);  // 绿色
bool useRed = true;
vec3 finalColor = useRed ? color1 : color2;  // 红色

// 复杂条件
float intensity = 0.7;
vec3 result = (intensity > 0.5) ?
              vec3(intensity) :
              vec3(0.0, 0.0, intensity);
```

## 4. 控制流语句

### 4.1 条件语句

```glsl
float intensity = 0.8;

if (intensity > 0.5) {
    // 高亮度处理
    gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);
} else if (intensity > 0.2) {
    // 中等亮度处理
    gl_FragColor = vec4(0.5, 0.5, 0.5, 1.0);
} else {
    // 低亮度处理
    gl_FragColor = vec4(0.1, 0.1, 0.1, 1.0);
}
```

### 4.2 循环语句

```glsl
// for循环
vec3 totalColor = vec3(0.0);
for (int i = 0; i < 3; i++) {
    totalColor += sampleColor(i);
}

// while循环
int count = 0;
while (count < maxIterations && !converged) {
    // 迭代计算
    count++;
}
```

## 5. 函数定义

### 5.1 基本函数定义

```glsl
// 计算两点距离
float distance2D(vec2 p1, vec2 p2) {
    vec2 diff = p1 - p2;
    return sqrt(diff.x * diff.x + diff.y * diff.y);
}

// 颜色混合函数
vec3 blendColors(vec3 color1, vec3 color2, float factor) {
    return mix(color1, color2, factor);
}

// 法向量归一化
vec3 normalizeVector(vec3 input) {
    float length = sqrt(dot(input, input));
    return input / length;
}
```

### 5.2 函数参数修饰符

```glsl
// in: 输入参数（默认）
void processInput(in vec3 inputColor) {
    // inputColor只能读取，不能修改
}

// out: 输出参数
void getTransformedPosition(in vec3 position, out vec4 result) {
    result = projectionMatrix * viewMatrix * vec4(position, 1.0);
}

// inout: 输入输出参数
void modifyColor(inout vec3 color) {
    color = color * 2.0;  // 可以修改传入的参数
}
```

## 6. 预处理指令

### 6.1 版本声明

```glsl
#version 300 es  // WebGL 2.0
#version 100     // WebGL 1.0
```

### 6.2 精度声明

```glsl
precision highp float;   // 高精度浮点数
precision mediump float; // 中等精度浮点数
precision lowp float;    // 低精度浮点数
```

### 6.3 宏定义

```glsl
#define PI 3.14159265359
#define MAX_LIGHTS 8
#define ENABLE_SHADOWS

#ifdef ENABLE_SHADOWS
    // 阴影相关代码
#endif
```

## 7. 限定符 (Qualifiers)

### 7.1 存储限定符

```glsl
// 顶点着色器
attribute vec3 a_position;  // 顶点属性（WebGL 1.0）
in vec3 a_position;         // 顶点属性（WebGL 2.0）

uniform mat4 u_modelMatrix; // 统一变量
varying vec2 v_texCoord;    // 变化变量（WebGL 1.0）
out vec2 v_texCoord;        // 输出变量（WebGL 2.0）

// 片段着色器
varying vec2 v_texCoord;    // 变化变量（WebGL 1.0）
in vec2 v_texCoord;         // 输入变量（WebGL 2.0）

uniform sampler2D u_texture; // 纹理采样器
```

### 7.2 参数限定符

```glsl
const float PI = 3.14159;   // 常量
```

## 8. 注释

```glsl
// 单行注释

/*
 * 多行注释
 * 可以跨越多行
 */
```

## 总结

GLSL 的基础语法为着色器编程提供了强大而简洁的表达方式。理解这些基础概念是编写高效着色器程序的关键。下一节我们将深入探讨 GLSL 的内置函数和高级特性。
