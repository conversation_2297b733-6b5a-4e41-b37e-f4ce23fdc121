# GLSL顶点着色器详解

## 概述
顶点着色器是图形渲染管线的第一个可编程阶段，负责处理每个顶点的变换、光照计算和数据传递。每个顶点都会独立地执行一次顶点着色器程序。

## 1. 顶点着色器的作用

### 1.1 主要职责
- **顶点变换**：将顶点从模型空间变换到裁剪空间
- **属性计算**：计算法向量、纹理坐标等顶点属性
- **光照计算**：执行逐顶点光照计算（Gouraud着色）
- **数据传递**：向片段着色器传递插值数据

### 1.2 输入和输出
```glsl
// 输入：顶点属性
attribute vec3 a_position;    // 顶点位置
attribute vec3 a_normal;      // 顶点法向量
attribute vec2 a_texCoord;    // 纹理坐标

// 输入：统一变量
uniform mat4 u_modelMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_projectionMatrix;

// 输出：内置变量
gl_Position;                  // 必须设置的裁剪空间位置
gl_PointSize;                 // 点的大小（点渲染时）

// 输出：自定义变量
varying vec2 v_texCoord;      // 传递给片段着色器
varying vec3 v_normal;
```

## 2. 坐标变换

### 2.1 基础变换流程
```glsl
attribute vec3 a_position;

uniform mat4 u_modelMatrix;      // 模型矩阵：模型空间 → 世界空间
uniform mat4 u_viewMatrix;       // 视图矩阵：世界空间 → 视图空间
uniform mat4 u_projectionMatrix; // 投影矩阵：视图空间 → 裁剪空间

void main() {
    // 方法1：分步变换
    vec4 worldPosition = u_modelMatrix * vec4(a_position, 1.0);
    vec4 viewPosition = u_viewMatrix * worldPosition;
    gl_Position = u_projectionMatrix * viewPosition;
    
    // 方法2：组合变换（更高效）
    mat4 mvpMatrix = u_projectionMatrix * u_viewMatrix * u_modelMatrix;
    gl_Position = mvpMatrix * vec4(a_position, 1.0);
}
```

### 2.2 常用变换矩阵
```glsl
// MVP矩阵组合
uniform mat4 u_mvpMatrix;        // 预计算的MVP矩阵

// 法向量变换矩阵
uniform mat3 u_normalMatrix;     // 法向量变换矩阵（模型矩阵的逆转置）

// 模型视图矩阵
uniform mat4 u_modelViewMatrix;  // 模型视图组合矩阵

void main() {
    // 位置变换
    gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
    
    // 法向量变换
    vec3 transformedNormal = normalize(u_normalMatrix * a_normal);
}
```

## 3. 顶点属性处理

### 3.1 基础属性
```glsl
// 位置属性
attribute vec3 a_position;
attribute vec4 a_position4;      // 齐次坐标

// 法向量属性
attribute vec3 a_normal;

// 纹理坐标
attribute vec2 a_texCoord;
attribute vec2 a_texCoord2;      // 第二套纹理坐标

// 颜色属性
attribute vec3 a_color;
attribute vec4 a_color4;         // 带透明度的颜色

// 切线和副切线（法线贴图用）
attribute vec3 a_tangent;
attribute vec3 a_bitangent;
```

### 3.2 实例化属性
```glsl
// 基础顶点属性
attribute vec3 a_position;
attribute vec3 a_normal;

// 实例化属性（每个实例一个值）
attribute vec3 a_instancePosition;   // 实例位置偏移
attribute vec3 a_instanceScale;      // 实例缩放
attribute vec4 a_instanceRotation;   // 实例旋转（四元数）
attribute vec3 a_instanceColor;      // 实例颜色

void main() {
    // 应用实例变换
    vec3 scaledPosition = a_position * a_instanceScale;
    
    // 应用旋转（四元数旋转）
    vec3 rotatedPosition = rotateByQuaternion(scaledPosition, a_instanceRotation);
    
    // 应用位置偏移
    vec3 finalPosition = rotatedPosition + a_instancePosition;
    
    gl_Position = u_mvpMatrix * vec4(finalPosition, 1.0);
}

// 四元数旋转函数
vec3 rotateByQuaternion(vec3 v, vec4 q) {
    vec3 qvec = q.xyz;
    vec3 uv = cross(qvec, v);
    vec3 uuv = cross(qvec, uv);
    return v + 2.0 * (uv * q.w + uuv);
}
```

## 4. 光照计算

### 4.1 逐顶点光照（Gouraud着色）
```glsl
attribute vec3 a_position;
attribute vec3 a_normal;

uniform mat4 u_mvpMatrix;
uniform mat3 u_normalMatrix;
uniform vec3 u_lightPosition;
uniform vec3 u_lightColor;
uniform vec3 u_ambientColor;
uniform float u_shininess;

varying vec3 v_color;

void main() {
    // 变换位置
    gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
    
    // 变换法向量
    vec3 normal = normalize(u_normalMatrix * a_normal);
    
    // 计算光照方向
    vec3 lightDir = normalize(u_lightPosition - a_position);
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * u_lightColor;
    
    // 镜面反射
    vec3 viewDir = normalize(-a_position); // 假设相机在原点
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), u_shininess);
    vec3 specular = spec * u_lightColor;
    
    // 最终颜色
    v_color = u_ambientColor + diffuse + specular;
}
```

### 4.2 为片段着色器准备数据
```glsl
attribute vec3 a_position;
attribute vec3 a_normal;
attribute vec2 a_texCoord;

uniform mat4 u_modelMatrix;
uniform mat4 u_viewMatrix;
uniform mat4 u_projectionMatrix;
uniform mat3 u_normalMatrix;

varying vec3 v_worldPosition;    // 世界空间位置
varying vec3 v_viewPosition;     // 视图空间位置
varying vec3 v_normal;           // 世界空间法向量
varying vec2 v_texCoord;         // 纹理坐标
varying float v_depth;           // 深度值

void main() {
    // 计算各种空间的位置
    vec4 worldPos = u_modelMatrix * vec4(a_position, 1.0);
    vec4 viewPos = u_viewMatrix * worldPos;
    
    v_worldPosition = worldPos.xyz;
    v_viewPosition = viewPos.xyz;
    v_normal = normalize(u_normalMatrix * a_normal);
    v_texCoord = a_texCoord;
    v_depth = viewPos.z;
    
    gl_Position = u_projectionMatrix * viewPos;
}
```

## 5. 特殊效果

### 5.1 顶点动画
```glsl
attribute vec3 a_position;
attribute vec3 a_normal;

uniform float u_time;
uniform float u_amplitude;
uniform float u_frequency;

varying vec3 v_normal;

void main() {
    vec3 position = a_position;
    
    // 正弦波动画
    float wave = sin(position.x * u_frequency + u_time) * u_amplitude;
    position.y += wave;
    
    // 更新法向量（简化计算）
    vec3 normal = a_normal;
    float dWave = cos(position.x * u_frequency + u_time) * u_amplitude * u_frequency;
    normal.x -= dWave;
    normal = normalize(normal);
    
    v_normal = normal;
    gl_Position = u_mvpMatrix * vec4(position, 1.0);
}
```

### 5.2 顶点噪声
```glsl
attribute vec3 a_position;
uniform float u_time;
uniform float u_noiseScale;

// 简单噪声函数
float noise(vec3 p) {
    return fract(sin(dot(p, vec3(12.9898, 78.233, 45.164))) * 43758.5453);
}

void main() {
    vec3 position = a_position;
    
    // 添加噪声偏移
    vec3 noiseOffset = vec3(
        noise(position + u_time),
        noise(position + u_time + 100.0),
        noise(position + u_time + 200.0)
    ) * u_noiseScale;
    
    position += noiseOffset;
    
    gl_Position = u_mvpMatrix * vec4(position, 1.0);
}
```

## 6. 骨骼动画

### 6.1 基础骨骼动画
```glsl
attribute vec3 a_position;
attribute vec3 a_normal;
attribute vec4 a_boneIndices;    // 骨骼索引
attribute vec4 a_boneWeights;    // 骨骼权重

uniform mat4 u_boneMatrices[64]; // 骨骼变换矩阵数组
uniform mat4 u_mvpMatrix;

varying vec3 v_normal;

void main() {
    // 计算骨骼变换
    mat4 boneTransform = 
        u_boneMatrices[int(a_boneIndices.x)] * a_boneWeights.x +
        u_boneMatrices[int(a_boneIndices.y)] * a_boneWeights.y +
        u_boneMatrices[int(a_boneIndices.z)] * a_boneWeights.z +
        u_boneMatrices[int(a_boneIndices.w)] * a_boneWeights.w;
    
    // 应用骨骼变换
    vec4 skinnedPosition = boneTransform * vec4(a_position, 1.0);
    vec3 skinnedNormal = mat3(boneTransform) * a_normal;
    
    v_normal = normalize(skinnedNormal);
    gl_Position = u_mvpMatrix * skinnedPosition;
}
```

## 7. 内置变量

### 7.1 输出变量
```glsl
void main() {
    // 必须设置的位置
    gl_Position = vec4(0.0, 0.0, 0.0, 1.0);
    
    // 点渲染时的点大小
    gl_PointSize = 10.0;
}
```

### 7.2 输入变量（WebGL 2.0）
```glsl
void main() {
    // 顶点ID
    int vertexId = gl_VertexID;
    
    // 实例ID
    int instanceId = gl_InstanceID;
    
    // 使用顶点ID生成位置
    float x = float(vertexId % 10) - 5.0;
    float y = float(vertexId / 10) - 5.0;
    gl_Position = vec4(x * 0.1, y * 0.1, 0.0, 1.0);
}
```

## 8. 性能优化技巧

### 8.1 预计算矩阵
```glsl
// 在CPU端预计算组合矩阵
uniform mat4 u_mvpMatrix;        // MVP组合矩阵
uniform mat4 u_modelViewMatrix;  // 模型视图组合矩阵
uniform mat3 u_normalMatrix;     // 法向量变换矩阵

void main() {
    // 直接使用预计算的矩阵
    gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
}
```

### 8.2 减少varying变量
```glsl
// 避免传递过多的varying变量
varying vec3 v_worldPosition;
varying vec3 v_normal;
varying vec2 v_texCoord;

// 如果可能，在片段着色器中重新计算
// 而不是从顶点着色器传递
```

## 9. 调试技巧

### 9.1 颜色调试
```glsl
void main() {
    gl_Position = u_mvpMatrix * vec4(a_position, 1.0);
    
    // 将法向量作为颜色输出进行调试
    v_debugColor = a_normal * 0.5 + 0.5; // 映射到[0,1]范围
}
```

### 9.2 位置验证
```glsl
void main() {
    // 检查变换是否正确
    vec4 worldPos = u_modelMatrix * vec4(a_position, 1.0);
    
    // 输出世界坐标进行验证
    v_worldPosition = worldPos.xyz;
    
    gl_Position = u_projectionMatrix * u_viewMatrix * worldPos;
}
```

## 总结
顶点着色器是图形渲染管线的关键组件，负责顶点变换和数据准备。掌握顶点着色器的编写技巧对于实现各种视觉效果至关重要。关键要点包括：

- 正确理解坐标变换流程
- 高效处理顶点属性
- 合理选择在顶点着色器还是片段着色器中进行计算
- 优化性能，减少不必要的计算和数据传递
