# GLSL片段着色器详解

## 概述
片段着色器（Fragment Shader）是图形渲染管线中负责计算每个像素最终颜色的可编程阶段。它在光栅化之后执行，为屏幕上的每个片段（像素）独立运行一次。

## 1. 片段着色器的作用

### 1.1 主要职责
- **颜色计算**：确定每个像素的最终颜色
- **纹理采样**：从纹理中读取颜色和数据
- **光照计算**：执行逐像素光照计算（Phong着色）
- **特效处理**：实现各种视觉效果和后处理

### 1.2 输入和输出
```glsl
// 输入：从顶点着色器插值的数据
varying vec2 v_texCoord;      // 纹理坐标
varying vec3 v_normal;        // 法向量
varying vec3 v_worldPosition; // 世界坐标

// 输入：统一变量
uniform sampler2D u_texture;  // 纹理采样器
uniform vec3 u_lightColor;    // 光源颜色
uniform float u_time;         // 时间

// 输入：内置变量
gl_FragCoord;                 // 片段坐标
gl_FrontFacing;               // 是否为正面

// 输出：内置变量
gl_FragColor;                 // 最终颜色（WebGL 1.0）
// out vec4 fragColor;        // 最终颜色（WebGL 2.0）
```

## 2. 基础颜色计算

### 2.1 简单颜色输出
```glsl
precision mediump float;

void main() {
    // 输出纯红色
    gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0);
    
    // 输出渐变色
    vec2 uv = gl_FragCoord.xy / vec2(800.0, 600.0); // 假设屏幕尺寸
    gl_FragColor = vec4(uv.x, uv.y, 0.5, 1.0);
}
```

### 2.2 使用插值数据
```glsl
precision mediump float;

varying vec2 v_texCoord;
varying vec3 v_color;

void main() {
    // 使用从顶点着色器传来的颜色
    gl_FragColor = vec4(v_color, 1.0);
    
    // 基于纹理坐标的颜色
    gl_FragColor = vec4(v_texCoord, 0.0, 1.0);
}
```

## 3. 纹理采样

### 3.1 基础纹理采样
```glsl
precision mediump float;

uniform sampler2D u_diffuseTexture;
varying vec2 v_texCoord;

void main() {
    // 基础纹理采样
    vec4 texColor = texture2D(u_diffuseTexture, v_texCoord);
    gl_FragColor = texColor;
    
    // 只使用RGB通道
    gl_FragColor = vec4(texColor.rgb, 1.0);
    
    // 使用alpha通道控制透明度
    gl_FragColor = vec4(texColor.rgb, texColor.a * 0.8);
}
```

### 3.2 多纹理混合
```glsl
precision mediump float;

uniform sampler2D u_baseTexture;
uniform sampler2D u_detailTexture;
uniform sampler2D u_maskTexture;
uniform float u_blendFactor;

varying vec2 v_texCoord;

void main() {
    vec4 baseColor = texture2D(u_baseTexture, v_texCoord);
    vec4 detailColor = texture2D(u_detailTexture, v_texCoord * 4.0); // 平铺4次
    vec4 mask = texture2D(u_maskTexture, v_texCoord);
    
    // 使用遮罩混合纹理
    vec4 blendedColor = mix(baseColor, detailColor, mask.r * u_blendFactor);
    
    gl_FragColor = blendedColor;
}
```

### 3.3 立方体纹理采样
```glsl
precision mediump float;

uniform samplerCube u_envTexture;
varying vec3 v_worldPosition;
varying vec3 v_normal;

uniform vec3 u_cameraPosition;

void main() {
    // 计算反射方向
    vec3 viewDir = normalize(v_worldPosition - u_cameraPosition);
    vec3 reflectDir = reflect(viewDir, normalize(v_normal));
    
    // 采样环境贴图
    vec4 envColor = textureCube(u_envTexture, reflectDir);
    
    gl_FragColor = envColor;
}
```

## 4. 光照计算

### 4.1 Phong光照模型
```glsl
precision mediump float;

varying vec3 v_worldPosition;
varying vec3 v_normal;
varying vec2 v_texCoord;

uniform vec3 u_lightPosition;
uniform vec3 u_lightColor;
uniform vec3 u_ambientColor;
uniform vec3 u_cameraPosition;
uniform float u_shininess;
uniform sampler2D u_diffuseTexture;

void main() {
    // 归一化向量
    vec3 normal = normalize(v_normal);
    vec3 lightDir = normalize(u_lightPosition - v_worldPosition);
    vec3 viewDir = normalize(u_cameraPosition - v_worldPosition);
    
    // 环境光
    vec3 ambient = u_ambientColor;
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * u_lightColor;
    
    // 镜面反射
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), u_shininess);
    vec3 specular = spec * u_lightColor;
    
    // 纹理颜色
    vec3 texColor = texture2D(u_diffuseTexture, v_texCoord).rgb;
    
    // 最终颜色
    vec3 finalColor = (ambient + diffuse + specular) * texColor;
    gl_FragColor = vec4(finalColor, 1.0);
}
```

### 4.2 Blinn-Phong光照模型
```glsl
precision mediump float;

varying vec3 v_worldPosition;
varying vec3 v_normal;

uniform vec3 u_lightPosition;
uniform vec3 u_lightColor;
uniform vec3 u_cameraPosition;
uniform float u_shininess;

void main() {
    vec3 normal = normalize(v_normal);
    vec3 lightDir = normalize(u_lightPosition - v_worldPosition);
    vec3 viewDir = normalize(u_cameraPosition - v_worldPosition);
    
    // 半角向量
    vec3 halfwayDir = normalize(lightDir + viewDir);
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * u_lightColor;
    
    // 镜面反射（Blinn-Phong）
    float spec = pow(max(dot(normal, halfwayDir), 0.0), u_shininess);
    vec3 specular = spec * u_lightColor;
    
    vec3 finalColor = diffuse + specular;
    gl_FragColor = vec4(finalColor, 1.0);
}
```

### 4.3 多光源光照
```glsl
precision mediump float;

varying vec3 v_worldPosition;
varying vec3 v_normal;

uniform vec3 u_lightPositions[4];
uniform vec3 u_lightColors[4];
uniform float u_lightIntensities[4];
uniform int u_lightCount;
uniform vec3 u_cameraPosition;

vec3 calculateLight(vec3 lightPos, vec3 lightColor, float intensity) {
    vec3 normal = normalize(v_normal);
    vec3 lightDir = normalize(lightPos - v_worldPosition);
    vec3 viewDir = normalize(u_cameraPosition - v_worldPosition);
    
    // 距离衰减
    float distance = length(lightPos - v_worldPosition);
    float attenuation = 1.0 / (1.0 + 0.09 * distance + 0.032 * distance * distance);
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * lightColor * intensity * attenuation;
    
    // 镜面反射
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    vec3 specular = spec * lightColor * intensity * attenuation;
    
    return diffuse + specular;
}

void main() {
    vec3 totalLight = vec3(0.0);
    
    // 累加所有光源的贡献
    for (int i = 0; i < 4; i++) {
        if (i >= u_lightCount) break;
        totalLight += calculateLight(u_lightPositions[i], u_lightColors[i], u_lightIntensities[i]);
    }
    
    gl_FragColor = vec4(totalLight, 1.0);
}
```

## 5. 法线贴图

### 5.1 切线空间法线贴图
```glsl
precision mediump float;

varying vec3 v_worldPosition;
varying vec3 v_normal;
varying vec3 v_tangent;
varying vec3 v_bitangent;
varying vec2 v_texCoord;

uniform sampler2D u_diffuseTexture;
uniform sampler2D u_normalTexture;
uniform vec3 u_lightPosition;

void main() {
    // 构建TBN矩阵
    vec3 T = normalize(v_tangent);
    vec3 B = normalize(v_bitangent);
    vec3 N = normalize(v_normal);
    mat3 TBN = mat3(T, B, N);
    
    // 从法线贴图采样
    vec3 normalMap = texture2D(u_normalTexture, v_texCoord).rgb;
    normalMap = normalMap * 2.0 - 1.0; // 从[0,1]映射到[-1,1]
    
    // 变换到世界空间
    vec3 normal = normalize(TBN * normalMap);
    
    // 光照计算
    vec3 lightDir = normalize(u_lightPosition - v_worldPosition);
    float diff = max(dot(normal, lightDir), 0.0);
    
    vec3 texColor = texture2D(u_diffuseTexture, v_texCoord).rgb;
    vec3 finalColor = texColor * diff;
    
    gl_FragColor = vec4(finalColor, 1.0);
}
```

## 6. 特殊效果

### 6.1 程序化纹理
```glsl
precision mediump float;

varying vec2 v_texCoord;
uniform float u_time;

void main() {
    vec2 uv = v_texCoord;
    
    // 棋盘格图案
    vec2 grid = floor(uv * 8.0);
    float checker = mod(grid.x + grid.y, 2.0);
    
    // 动态波纹
    float wave = sin(uv.x * 10.0 + u_time) * sin(uv.y * 10.0 + u_time);
    
    // 组合效果
    vec3 color = vec3(checker) + vec3(wave * 0.2);
    
    gl_FragColor = vec4(color, 1.0);
}
```

### 6.2 边缘检测
```glsl
precision mediump float;

uniform sampler2D u_texture;
uniform vec2 u_textureSize;
varying vec2 v_texCoord;

void main() {
    vec2 texelSize = 1.0 / u_textureSize;
    
    // Sobel算子
    float tl = texture2D(u_texture, v_texCoord + vec2(-texelSize.x, -texelSize.y)).r; // 左上
    float tm = texture2D(u_texture, v_texCoord + vec2(0.0, -texelSize.y)).r;         // 上
    float tr = texture2D(u_texture, v_texCoord + vec2(texelSize.x, -texelSize.y)).r; // 右上
    float ml = texture2D(u_texture, v_texCoord + vec2(-texelSize.x, 0.0)).r;         // 左
    float mr = texture2D(u_texture, v_texCoord + vec2(texelSize.x, 0.0)).r;          // 右
    float bl = texture2D(u_texture, v_texCoord + vec2(-texelSize.x, texelSize.y)).r; // 左下
    float bm = texture2D(u_texture, v_texCoord + vec2(0.0, texelSize.y)).r;          // 下
    float br = texture2D(u_texture, v_texCoord + vec2(texelSize.x, texelSize.y)).r;  // 右下
    
    // 计算梯度
    float gx = -tl - 2.0*ml - bl + tr + 2.0*mr + br;
    float gy = -tl - 2.0*tm - tr + bl + 2.0*bm + br;
    
    float edge = sqrt(gx*gx + gy*gy);
    
    gl_FragColor = vec4(vec3(edge), 1.0);
}
```

### 6.3 模糊效果
```glsl
precision mediump float;

uniform sampler2D u_texture;
uniform vec2 u_textureSize;
uniform float u_blurRadius;
varying vec2 v_texCoord;

void main() {
    vec2 texelSize = 1.0 / u_textureSize;
    vec4 color = vec4(0.0);
    float totalWeight = 0.0;
    
    // 高斯模糊
    for (float x = -u_blurRadius; x <= u_blurRadius; x += 1.0) {
        for (float y = -u_blurRadius; y <= u_blurRadius; y += 1.0) {
            vec2 offset = vec2(x, y) * texelSize;
            float weight = exp(-(x*x + y*y) / (2.0 * u_blurRadius * u_blurRadius));
            
            color += texture2D(u_texture, v_texCoord + offset) * weight;
            totalWeight += weight;
        }
    }
    
    gl_FragColor = color / totalWeight;
}
```

## 7. 透明度和混合

### 7.1 Alpha测试
```glsl
precision mediump float;

uniform sampler2D u_texture;
uniform float u_alphaThreshold;
varying vec2 v_texCoord;

void main() {
    vec4 texColor = texture2D(u_texture, v_texCoord);
    
    // Alpha测试
    if (texColor.a < u_alphaThreshold) {
        discard; // 丢弃片段
    }
    
    gl_FragColor = texColor;
}
```

### 7.2 Alpha混合
```glsl
precision mediump float;

uniform sampler2D u_diffuseTexture;
uniform float u_opacity;
varying vec2 v_texCoord;

void main() {
    vec4 texColor = texture2D(u_diffuseTexture, v_texCoord);
    
    // 应用透明度
    gl_FragColor = vec4(texColor.rgb, texColor.a * u_opacity);
}
```

## 8. 内置变量和函数

### 8.1 内置输入变量
```glsl
void main() {
    // 片段坐标（屏幕空间）
    vec2 screenPos = gl_FragCoord.xy;
    
    // 判断是否为正面
    if (gl_FrontFacing) {
        gl_FragColor = vec4(1.0, 0.0, 0.0, 1.0); // 正面红色
    } else {
        gl_FragColor = vec4(0.0, 0.0, 1.0, 1.0); // 背面蓝色
    }
    
    // 基于屏幕位置的效果
    vec2 uv = screenPos / vec2(800.0, 600.0);
    gl_FragColor = vec4(uv, 0.0, 1.0);
}
```

### 8.2 偏导数函数
```glsl
precision mediump float;

varying vec2 v_texCoord;

void main() {
    // 计算纹理坐标的偏导数
    vec2 dUVdx = dFdx(v_texCoord);
    vec2 dUVdy = dFdy(v_texCoord);
    
    // 计算变化率
    float changeRate = length(dUVdx) + length(dUVdy);
    
    gl_FragColor = vec4(vec3(changeRate), 1.0);
}
```

## 9. 性能优化

### 9.1 减少纹理采样
```glsl
// 避免重复采样
vec4 texColor = texture2D(u_texture, v_texCoord);
vec3 rgb = texColor.rgb;
float alpha = texColor.a;

// 而不是
// vec3 rgb = texture2D(u_texture, v_texCoord).rgb;
// float alpha = texture2D(u_texture, v_texCoord).a;
```

### 9.2 使用适当的精度
```glsl
precision mediump float; // 大多数情况下足够

// 只在需要时使用高精度
highp float preciseValue = computePreciseValue();
```

## 总结
片段着色器是实现各种视觉效果的核心，从基础的颜色计算到复杂的光照模型和特效处理。掌握片段着色器编程技巧对于创建高质量的图形应用至关重要。关键要点包括：

- 理解片段着色器在渲染管线中的作用
- 掌握纹理采样和光照计算技术
- 学会实现各种视觉效果和后处理
- 注意性能优化，合理使用精度和减少不必要的计算
