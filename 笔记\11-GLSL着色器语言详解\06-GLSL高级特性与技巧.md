# GLSL高级特性与技巧

## 概述
本章节介绍GLSL的高级特性和编程技巧，包括高级数据结构、复杂算法实现、性能优化策略以及调试技巧。

## 1. 高级数据结构

### 1.1 结构体定义和使用
```glsl
// 定义材质结构体
struct Material {
    vec3 ambient;
    vec3 diffuse;
    vec3 specular;
    float shininess;
    sampler2D diffuseTexture;
    sampler2D normalTexture;
};

// 定义光源结构体
struct Light {
    vec3 position;
    vec3 color;
    float intensity;
    float constant;
    float linear;
    float quadratic;
};

// 使用结构体
uniform Material u_material;
uniform Light u_lights[8];

vec3 calculateLighting(Light light, Material material, vec3 fragPos, vec3 normal, vec3 viewDir) {
    vec3 lightDir = normalize(light.position - fragPos);
    
    // 距离衰减
    float distance = length(light.position - fragPos);
    float attenuation = 1.0 / (light.constant + light.linear * distance + light.quadratic * distance * distance);
    
    // 漫反射
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * material.diffuse * light.color;
    
    // 镜面反射
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), material.shininess);
    vec3 specular = spec * material.specular * light.color;
    
    return (diffuse + specular) * light.intensity * attenuation;
}
```

### 1.2 数组操作技巧
```glsl
// 动态数组访问
uniform float u_weights[16];
uniform int u_activeCount;

float getWeight(int index) {
    // 安全的数组访问
    if (index < 0 || index >= u_activeCount) {
        return 0.0;
    }
    
    // 在GLSL中，数组索引必须是常量或循环变量
    for (int i = 0; i < 16; i++) {
        if (i == index) {
            return u_weights[i];
        }
    }
    return 0.0;
}

// 数组求和
float sumArray(float arr[8], int count) {
    float sum = 0.0;
    for (int i = 0; i < 8; i++) {
        if (i >= count) break;
        sum += arr[i];
    }
    return sum;
}
```

## 2. 高级算法实现

### 2.1 噪声函数
```glsl
// 伪随机数生成
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}

// 2D噪声
float noise2D(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    
    // 四个角的随机值
    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));
    
    // 平滑插值
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}

// 分形噪声
float fbm(vec2 st, int octaves) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for (int i = 0; i < 8; i++) {
        if (i >= octaves) break;
        
        value += amplitude * noise2D(st * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

// 3D噪声
float noise3D(vec3 p) {
    vec3 i = floor(p);
    vec3 f = fract(p);
    
    // 8个角的随机值
    float n000 = random(i.xy + vec2(0.0, 0.0));
    float n100 = random(i.xy + vec2(1.0, 0.0));
    float n010 = random(i.xy + vec2(0.0, 1.0));
    float n110 = random(i.xy + vec2(1.0, 1.0));
    
    float n001 = random((i + vec3(0.0, 0.0, 1.0)).xy);
    float n101 = random((i + vec3(1.0, 0.0, 1.0)).xy);
    float n011 = random((i + vec3(0.0, 1.0, 1.0)).xy);
    float n111 = random((i + vec3(1.0, 1.0, 1.0)).xy);
    
    // 三线性插值
    vec3 u = f * f * (3.0 - 2.0 * f);
    
    float nx00 = mix(n000, n100, u.x);
    float nx01 = mix(n001, n101, u.x);
    float nx10 = mix(n010, n110, u.x);
    float nx11 = mix(n011, n111, u.x);
    
    float nxy0 = mix(nx00, nx10, u.y);
    float nxy1 = mix(nx01, nx11, u.y);
    
    return mix(nxy0, nxy1, u.z);
}
```

### 2.2 距离场函数（SDF）
```glsl
// 球体距离场
float sdSphere(vec3 p, float radius) {
    return length(p) - radius;
}

// 盒子距离场
float sdBox(vec3 p, vec3 b) {
    vec3 q = abs(p) - b;
    return length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);
}

// 圆环距离场
float sdTorus(vec3 p, vec2 t) {
    vec2 q = vec2(length(p.xz) - t.x, p.y);
    return length(q) - t.y;
}

// 距离场操作
float opUnion(float d1, float d2) {
    return min(d1, d2);
}

float opSubtraction(float d1, float d2) {
    return max(-d1, d2);
}

float opIntersection(float d1, float d2) {
    return max(d1, d2);
}

// 平滑合并
float opSmoothUnion(float d1, float d2, float k) {
    float h = clamp(0.5 + 0.5 * (d2 - d1) / k, 0.0, 1.0);
    return mix(d2, d1, h) - k * h * (1.0 - h);
}

// 场景距离场
float sceneSDF(vec3 p) {
    float sphere = sdSphere(p - vec3(0.0, 1.0, 0.0), 1.0);
    float box = sdBox(p - vec3(2.0, 0.0, 0.0), vec3(1.0));
    float torus = sdTorus(p - vec3(-2.0, 0.0, 0.0), vec2(1.0, 0.3));
    
    return opUnion(opUnion(sphere, box), torus);
}
```

### 2.3 光线行进（Ray Marching）
```glsl
// 光线行进算法
vec3 rayMarch(vec3 ro, vec3 rd) {
    float t = 0.0;
    const int maxSteps = 100;
    const float minDist = 0.001;
    const float maxDist = 100.0;
    
    for (int i = 0; i < maxSteps; i++) {
        vec3 p = ro + t * rd;
        float d = sceneSDF(p);
        
        if (d < minDist) {
            // 击中表面
            return p;
        }
        
        t += d;
        
        if (t > maxDist) {
            // 超出最大距离
            break;
        }
    }
    
    return vec3(-1.0); // 未击中
}

// 计算法向量
vec3 calculateNormal(vec3 p) {
    const float eps = 0.001;
    vec2 h = vec2(eps, 0.0);
    
    return normalize(vec3(
        sceneSDF(p + h.xyy) - sceneSDF(p - h.xyy),
        sceneSDF(p + h.yxy) - sceneSDF(p - h.yxy),
        sceneSDF(p + h.yyx) - sceneSDF(p - h.yyx)
    ));
}
```

## 3. 高级光照技术

### 3.1 基于物理的渲染（PBR）
```glsl
// PBR材质参数
struct PBRMaterial {
    vec3 albedo;
    float metallic;
    float roughness;
    float ao;
    vec3 normal;
};

// 分布函数（Normal Distribution Function）
float DistributionGGX(vec3 N, vec3 H, float roughness) {
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = 3.14159265 * denom * denom;
    
    return num / denom;
}

// 几何函数
float GeometrySchlickGGX(float NdotV, float roughness) {
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;
    
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    
    return num / denom;
}

float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness) {
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = GeometrySchlickGGX(NdotV, roughness);
    float ggx1 = GeometrySchlickGGX(NdotL, roughness);
    
    return ggx1 * ggx2;
}

// 菲涅尔反射
vec3 fresnelSchlick(float cosTheta, vec3 F0) {
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

// PBR光照计算
vec3 calculatePBR(PBRMaterial material, vec3 lightPos, vec3 lightColor, vec3 viewPos, vec3 fragPos) {
    vec3 N = normalize(material.normal);
    vec3 V = normalize(viewPos - fragPos);
    vec3 L = normalize(lightPos - fragPos);
    vec3 H = normalize(V + L);
    
    // 计算距离衰减
    float distance = length(lightPos - fragPos);
    float attenuation = 1.0 / (distance * distance);
    vec3 radiance = lightColor * attenuation;
    
    // Cook-Torrance BRDF
    vec3 F0 = vec3(0.04);
    F0 = mix(F0, material.albedo, material.metallic);
    vec3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);
    
    float NDF = DistributionGGX(N, H, material.roughness);
    float G = GeometrySmith(N, V, L, material.roughness);
    
    vec3 numerator = NDF * G * F;
    float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.0001;
    vec3 specular = numerator / denominator;
    
    vec3 kS = F;
    vec3 kD = vec3(1.0) - kS;
    kD *= 1.0 - material.metallic;
    
    float NdotL = max(dot(N, L), 0.0);
    return (kD * material.albedo / 3.14159265 + specular) * radiance * NdotL;
}
```

### 3.2 阴影映射
```glsl
// 阴影映射
uniform sampler2D u_shadowMap;
uniform mat4 u_lightSpaceMatrix;

float calculateShadow(vec4 fragPosLightSpace, vec3 normal, vec3 lightDir) {
    // 透视除法
    vec3 projCoords = fragPosLightSpace.xyz / fragPosLightSpace.w;
    
    // 变换到[0,1]范围
    projCoords = projCoords * 0.5 + 0.5;
    
    // 获取深度图中的深度值
    float closestDepth = texture2D(u_shadowMap, projCoords.xy).r;
    
    // 当前片段的深度
    float currentDepth = projCoords.z;
    
    // 计算偏移以减少阴影失真
    float bias = max(0.05 * (1.0 - dot(normal, lightDir)), 0.005);
    
    // PCF软阴影
    float shadow = 0.0;
    vec2 texelSize = 1.0 / vec2(1024.0); // 假设阴影贴图大小为1024x1024
    
    for (int x = -1; x <= 1; ++x) {
        for (int y = -1; y <= 1; ++y) {
            float pcfDepth = texture2D(u_shadowMap, projCoords.xy + vec2(x, y) * texelSize).r;
            shadow += currentDepth - bias > pcfDepth ? 1.0 : 0.0;
        }
    }
    shadow /= 9.0;
    
    // 超出阴影贴图范围的区域不在阴影中
    if (projCoords.z > 1.0) {
        shadow = 0.0;
    }
    
    return shadow;
}
```

## 4. 性能优化技巧

### 4.1 分支优化
```glsl
// 避免动态分支
// 不好的做法
if (u_enableFeature) {
    color = expensiveCalculation(color);
}

// 更好的做法
float enableFactor = float(u_enableFeature);
color = mix(color, expensiveCalculation(color), enableFactor);

// 或者使用step函数
color += step(0.5, u_enableFeature) * additionalColor;
```

### 4.2 纹理采样优化
```glsl
// 预计算纹理坐标
varying vec2 v_texCoord;
varying vec2 v_texCoord2;

// 批量采样
vec4 tex1 = texture2D(u_texture1, v_texCoord);
vec4 tex2 = texture2D(u_texture2, v_texCoord2);

// 避免在循环中采样纹理
// 不好的做法
for (int i = 0; i < 8; i++) {
    color += texture2D(u_texture, v_texCoord + offsets[i]);
}

// 更好的做法：展开循环或使用预计算的采样
```

### 4.3 数学优化
```glsl
// 使用内置函数
float len = length(vec3(x, y, z));
// 而不是
float len = sqrt(x*x + y*y + z*z);

// 避免除法，使用乘法
float invLength = inversesqrt(dot(v, v));
vec3 normalized = v * invLength;
// 而不是
vec3 normalized = v / length(v);

// 使用mad指令（乘加）
float result = a * b + c;
// 而不是分开计算
float temp = a * b;
float result = temp + c;
```

## 5. 调试技巧

### 5.1 可视化调试
```glsl
// 颜色编码调试
void debugVisualize() {
    // 法向量可视化
    vec3 debugColor = v_normal * 0.5 + 0.5;
    
    // 深度可视化
    float depth = gl_FragCoord.z;
    debugColor = vec3(depth);
    
    // 纹理坐标可视化
    debugColor = vec3(v_texCoord, 0.0);
    
    // 光照强度可视化
    float intensity = dot(normalize(v_normal), normalize(u_lightDirection));
    debugColor = vec3(intensity);
    
    gl_FragColor = vec4(debugColor, 1.0);
}
```

### 5.2 条件编译调试
```glsl
#define DEBUG_NORMALS 0
#define DEBUG_TEXCOORDS 1
#define DEBUG_LIGHTING 2

uniform int u_debugMode;

void main() {
    vec3 finalColor = calculateLighting();
    
    #if DEBUG_NORMALS
    if (u_debugMode == 0) {
        finalColor = v_normal * 0.5 + 0.5;
    }
    #endif
    
    #if DEBUG_TEXCOORDS
    if (u_debugMode == 1) {
        finalColor = vec3(v_texCoord, 0.0);
    }
    #endif
    
    gl_FragColor = vec4(finalColor, 1.0);
}
```

## 6. 跨平台兼容性

### 6.1 精度处理
```glsl
#ifdef GL_ES
precision mediump float;
#endif

// 根据平台选择精度
#ifdef GL_ES
    #define PRECISION mediump
#else
    #define PRECISION
#endif

PRECISION float computeValue();
```

### 6.2 扩展检查
```glsl
#ifdef GL_OES_standard_derivatives
    #extension GL_OES_standard_derivatives : enable
    float edge = fwidth(v_texCoord.x);
#else
    float edge = 0.01; // 回退值
#endif
```

## 总结
GLSL的高级特性为实现复杂的视觉效果提供了强大的工具。掌握这些技巧可以：

- 实现更复杂和真实的渲染效果
- 优化着色器性能
- 提高代码的可维护性和可读性
- 确保跨平台兼容性

在实际开发中，应该根据目标平台和性能要求选择合适的技术和优化策略。
